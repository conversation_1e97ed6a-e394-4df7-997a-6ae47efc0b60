import EmblaCarousel from "embla-carousel";
import { addPrevNextBtnsClickHandlers } from "./carousel-next-previouse-lisnters.js";

// Templates Carousel Configuration
const TEMPLATES_CAROUSEL_CONFIG = {
  loop: true,
  direction: "rtl",
  align: "start",
  slidesToScroll: 1,
  containScroll: "trimSnaps",
  breakpoints: {
    '(min-width: 768px)': { slidesToScroll: 2 },
    '(min-width: 1024px)': { slidesToScroll: 3 }
  }
};

// Initialize templates carousel
export function initTemplatesCarousel() {
  const emblaNode = document.querySelector(".embla_templates");
  
  if (!emblaNode) {
    console.warn('Templates carousel: .embla_templates not found');
    return null;
  }

  const viewportNode = emblaNode.querySelector(".embla__viewport");
  const nextBtn = document.querySelector(".next-btn");
  const prevBtn = document.querySelector(".prev-btn");

  if (!viewportNode) {
    console.warn('Templates carousel: .embla__viewport not found');
    return null;
  }

  if (!nextBtn || !prevBtn) {
    console.warn('Templates carousel: Navigation buttons not found');
    return null;
  }

  // Initialize Embla carousel
  const emblaApi = EmblaCarousel(viewportNode, TEMPLATES_CAROUSEL_CONFIG);

  // Store the API reference on the DOM element for direct access
  emblaNode.emblaApi = emblaApi;

  // Add navigation button handlers
  const removeNavHandlers = addPrevNextBtnsClickHandlers(emblaApi, prevBtn, nextBtn);

  // Initialize with a manual scroll to ensure everything is set up
  setTimeout(() => {
    emblaApi.reInit();
    emblaApi.scrollTo(0);
  }, 100);

  // Return cleanup function
  return () => {
    removeNavHandlers();
    emblaApi.destroy();
  };
}

// Advanced templates carousel with additional features
export function initTemplatesCarouselAdvanced(options = {}) {
  const defaultOptions = {
    loop: true,
    direction: "rtl",
    align: "start",
    slidesToScroll: 1,
    containScroll: "trimSnaps",
    breakpoints: {
      '(min-width: 768px)': { slidesToScroll: 2 },
      '(min-width: 1024px)': { slidesToScroll: 3 }
    }
  };

  const config = { ...defaultOptions, ...options };
  const emblaNode = document.querySelector(".embla_templates");
  
  if (!emblaNode) {
    return null;
  }

  const viewportNode = emblaNode.querySelector(".embla__viewport");
  const nextBtn = document.querySelector(".next-btn");
  const prevBtn = document.querySelector(".prev-btn");

  if (!viewportNode || !nextBtn || !prevBtn) {
    return null;
  }

  const emblaApi = EmblaCarousel(viewportNode, config);
  emblaNode.emblaApi = emblaApi;

  const removeNavHandlers = addPrevNextBtnsClickHandlers(emblaApi, prevBtn, nextBtn);

  // Add slide change callback for animations
  emblaApi.on('select', () => {
    // Add any slide change animations here
    console.log('Templates carousel slide changed to:', emblaApi.selectedScrollSnap());
  });

  setTimeout(() => {
    emblaApi.reInit();
    emblaApi.scrollTo(0);
  }, 100);

  return () => {
    removeNavHandlers();
    emblaApi.destroy();
  };
}
