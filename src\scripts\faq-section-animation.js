import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

// FAQ Section Animation
export function initFAQSectionAnimation() {
  // Section title animation
  const sectionTitle = document.querySelector('.section-title');
  const sectionSubtitle = document.querySelector('.section-subtitle');

  if (sectionTitle) {
    // Set initial states
    gsap.set(sectionTitle, {
      autoAlpha: 0,
      y: 50,
      scale: 0.9
    });

    gsap.set(sectionTitle.querySelector('span'), {
      scaleX: 0,
      transformOrigin: 'right center' // RTL direction
    });

    if (sectionSubtitle) {
      gsap.set(sectionSubtitle, {
        autoAlpha: 0,
        y: 30
      });
    }

    // Create timeline for section headers
    const headerTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: sectionTitle,
        start: "top 85%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    // Animate title
    headerTimeline
      .to(sectionTitle, {
        autoAlpha: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        ease: "back.out(1.7)"
      })
      .to(sectionTitle.querySelector('span'), {
        scaleX: 1,
        duration: 0.6,
        ease: "power2.out"
      }, "-=0.4");

    // Animate subtitle
    if (sectionSubtitle) {
      headerTimeline.to(sectionSubtitle, {
        autoAlpha: 1,
        y: 0,
        duration: 0.6,
        ease: "power2.out"
      }, "-=0.3");
    }
  }

  // Accordion items stagger animation
  const accordionItems = document.querySelectorAll('.accordion-item');
  if (accordionItems.length > 0) {
    gsap.set(accordionItems, {
      autoAlpha: 0,
      y: 40,
      scale: 0.95,
      rotationX: 15
    });

    const accordionTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: ".accordion-container",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    accordionTimeline.to(accordionItems, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      duration: 0.8,
      stagger: {
        amount: 0.6,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    });
  }

  // Add hover effects to accordion headers
  const accordionHeaders = document.querySelectorAll('.accordion-header');
  accordionHeaders.forEach(header => {
    const item = header.closest('.accordion-item');

    header.addEventListener('mouseenter', () => {
      if (!item.classList.contains('accordion-open')) {
        gsap.to(header, {
          scale: 1.02,
          y: -2,
          duration: 0.3,
          ease: "power2.out"
        });

        gsap.to(item, {
          boxShadow: "0 10px 25px rgba(0,0,0,0.15)",
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });

    header.addEventListener('mouseleave', () => {
      if (!item.classList.contains('accordion-open')) {
        gsap.to(header, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        });

        gsap.to(item, {
          boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  });
}
