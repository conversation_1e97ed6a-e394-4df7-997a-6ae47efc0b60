import { gsap } from "gsap";

export function initPaymentTabs() {
  const countryTabs = document.querySelectorAll('.country-tab');
  const paymentTitle = document.getElementById('payment-title');
  const paymentSubtitle = document.getElementById('payment-subtitle');
  const egyptPayments = document.getElementById('egypt-payments');
  const saudiPayments = document.getElementById('saudi-payments');
  const paginationDots = document.querySelectorAll('.pagination-dot');

  // Content for different countries
  const countryContent = {
    egypt: {
      title: 'يمكنك الدفع عن طريق البنوك المصرية',
      subtitle: 'اختر طريقة الدفع التي تناسبك من هذه البنوك المصرية'
    },
    saudi: {
      title: 'يمكنك الدفع عن طريق البنوك السعودية',
      subtitle: 'اختر طريقة الدفع التي تناسبك من هذه البنوك السعودية'
    }
  };

  // Current page tracking
  let currentPage = 1;
  let currentCountry = 'egypt';

  // Initialize tabs
  if (countryTabs.length > 0) {
    countryTabs.forEach(tab => {
      tab.addEventListener('click', function() {
        const country = this.dataset.country;
        switchCountry(country);
      });
    });
  }

  // Initialize pagination
  if (paginationDots.length > 0) {
    paginationDots.forEach(dot => {
      dot.addEventListener('click', function() {
        const page = parseInt(this.dataset.page);
        switchPage(page);
      });
    });
  }

  function switchCountry(country) {
    if (country === currentCountry) return;

    // Update active tab
    countryTabs.forEach(tab => {
      if (tab.dataset.country === country) {
        tab.classList.add('active');
        tab.classList.remove('border-light-blue');
        tab.classList.add('border-primary-yellow');
      } else {
        tab.classList.remove('active');
        tab.classList.remove('border-primary-yellow');
        tab.classList.add('border-light-blue');
      }
    });

    // Animate content change
    const timeline = gsap.timeline();

    // Fade out current content
    timeline.to([paymentTitle, paymentSubtitle], {
      opacity: 0,
      y: -20,
      duration: 0.3,
      ease: "power2.inOut"
    });

    // Hide current payment methods
    const currentPaymentGrid = country === 'egypt' ? saudiPayments : egyptPayments;
    const newPaymentGrid = country === 'egypt' ? egyptPayments : saudiPayments;

    timeline.to(currentPaymentGrid, {
      opacity: 0,
      y: 20,
      duration: 0.3,
      ease: "power2.inOut",
      onComplete: () => {
        currentPaymentGrid.classList.add('hidden');
        currentPaymentGrid.classList.remove('grid');
      }
    }, "<");

    // Update content and show new payment methods
    timeline.call(() => {
      paymentTitle.textContent = countryContent[country].title;
      paymentSubtitle.textContent = countryContent[country].subtitle;
      
      newPaymentGrid.classList.remove('hidden');
      newPaymentGrid.classList.add('grid');
    });

    // Fade in new content
    timeline.fromTo([paymentTitle, paymentSubtitle], {
      opacity: 0,
      y: 20
    }, {
      opacity: 1,
      y: 0,
      duration: 0.4,
      ease: "power2.out"
    });

    timeline.fromTo(newPaymentGrid, {
      opacity: 0,
      y: 30
    }, {
      opacity: 1,
      y: 0,
      duration: 0.5,
      ease: "power2.out"
    }, "<0.1");

    currentCountry = country;
    
    // Reset pagination to page 1 when switching countries
    switchPage(1);
  }

  function switchPage(page) {
    if (page === currentPage) return;

    // Update active pagination dot
    paginationDots.forEach(dot => {
      const dotPage = parseInt(dot.dataset.page);
      const halfCircle = dot.querySelector('div');
      
      if (dotPage === page) {
        dot.classList.add('active');
        gsap.to(halfCircle, {
          scale: 1,
          duration: 0.3,
          ease: "back.out(1.7)"
        });
      } else {
        dot.classList.remove('active');
        gsap.to(halfCircle, {
          scale: 0,
          duration: 0.3,
          ease: "power2.inOut"
        });
      }
    });

    // Add page switching animation for payment cards
    const activePaymentGrid = currentCountry === 'egypt' ? egyptPayments : saudiPayments;
    const paymentCards = activePaymentGrid.querySelectorAll('.flex.items-center.shadow-md');

    // Animate cards based on page
    gsap.to(paymentCards, {
      opacity: 0.3,
      scale: 0.95,
      duration: 0.3,
      ease: "power2.inOut",
      onComplete: () => {
        // Simulate different content for different pages
        // In a real implementation, you would show different sets of banks
        gsap.to(paymentCards, {
          opacity: 1,
          scale: 1,
          duration: 0.4,
          ease: "back.out(1.2)",
          stagger: 0.1
        });
      }
    });

    currentPage = page;
  }

  // Add hover effects to tabs
  countryTabs.forEach(tab => {
    tab.addEventListener('mouseenter', function() {
      if (!this.classList.contains('active')) {
        gsap.to(this, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });

    tab.addEventListener('mouseleave', function() {
      if (!this.classList.contains('active')) {
        gsap.to(this, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  });

  // Add hover effects to pagination dots
  paginationDots.forEach(dot => {
    dot.addEventListener('mouseenter', function() {
      if (!this.classList.contains('active')) {
        gsap.to(this, {
          scale: 1.1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });

    dot.addEventListener('mouseleave', function() {
      if (!this.classList.contains('active')) {
        gsap.to(this, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  });

  // Initialize active state for first pagination dot
  if (paginationDots.length > 0) {
    const firstDot = paginationDots[0];
    const halfCircle = firstDot.querySelector('div');
    gsap.set(halfCircle, { scale: 1 });
  }
}
