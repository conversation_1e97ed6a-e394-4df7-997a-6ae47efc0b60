// Social Media Requirements Form Validation
export function initSocialMediaForm() {
  const form = document.getElementById('socialMediaForm');
  if (!form) return;

  // Get form elements
  const clientName = document.getElementById('clientName');
  const email = document.getElementById('email');
  const phoneNumber = document.getElementById('phoneNumber');
  const countryCode = document.getElementById('countryCode');
  const workTime = document.getElementById('workTime');
  const branchAddresses = document.getElementById('branchAddresses');
  const yourServices = document.getElementById('yourServices');
  const socialMedia = document.getElementById('socialMedia');
  const specialOffers = document.getElementById('specialOffers');
  const competitiveFeatures = document.getElementById('competitiveFeatures');
  const fileInput = document.getElementById('fileInput');
  const dropZone = document.getElementById('dropZone');
  const fileList = document.getElementById('fileList');

  // Validation rules
  const validationRules = {
    clientName: {
      required: true,
      minLength: 2,
      pattern: /^[\u0600-\u06FFa-zA-Z0-9\s&.-]+$/,
      errorMessages: {
        required: 'اسم المؤسسة أو العمل مطلوب',
        minLength: 'الاسم يجب أن يكون أكثر من حرفين',
        pattern: 'الاسم يحتوي على أحرف غير صالحة'
      }
    },
    email: {
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      errorMessages: {
        required: 'البريد الإلكتروني مطلوب',
        pattern: 'يرجى إدخال بريد إلكتروني صحيح'
      }
    },
    phoneNumber: {
      required: true,
      pattern: /^[0-9]{8,15}$/,
      errorMessages: {
        required: 'رقم الجوال مطلوب',
        pattern: 'يرجى إدخال رقم جوال صحيح (8-15 رقم)'
      }
    },
    workTime: {
      required: false,
      minLength: 3,
      maxLength: 200,
      errorMessages: {
        minLength: 'مواعيد العمل يجب أن تكون أكثر من 3 أحرف',
        maxLength: 'مواعيد العمل يجب أن تكون أقل من 200 حرف'
      }
    },
    branchAddresses: {
      required: false,
      maxLength: 1000,
      errorMessages: {
        maxLength: 'عناوين الفروع يجب أن تكون أقل من 1000 حرف'
      }
    },
    yourServices: {
      required: false,
      maxLength: 1000,
      errorMessages: {
        maxLength: 'وصف الخدمات يجب أن يكون أقل من 1000 حرف'
      }
    },
    socialMedia: {
      required: false,
      maxLength: 500,
      errorMessages: {
        maxLength: 'معلومات وسائل التواصل يجب أن تكون أقل من 500 حرف'
      }
    },
    specialOffers: {
      required: false,
      maxLength: 500,
      errorMessages: {
        maxLength: 'وصف العروض الخاصة يجب أن يكون أقل من 500 حرف'
      }
    },
    competitiveFeatures: {
      required: false,
      maxLength: 500,
      errorMessages: {
        maxLength: 'وصف الميزات التنافسية يجب أن يكون أقل من 500 حرف'
      }
    }
  };

  // Show error message
  function showError(field, message) {
    const errorSpan = field.parentNode.querySelector('.error-message');
    if (errorSpan) {
      errorSpan.textContent = message;
      errorSpan.classList.remove('hidden');
    }
    field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.remove('border-gray', 'focus:ring-primary-blue', 'focus:border-transparent');
  }

  // Hide error message
  function hideError(field) {
    const errorSpan = field.parentNode.querySelector('.error-message');
    if (errorSpan) {
      errorSpan.classList.add('hidden');
    }
    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.add('border-gray', 'focus:ring-primary-blue', 'focus:border-transparent');
  }

  // Validate single field
  function validateField(field, rules) {
    if (!field || !rules) return false;
    const value = field.value.trim();

    // Check required
    if (rules.required && !value) {
      showError(field, rules.errorMessages.required);
      return false;
    }

    // Check pattern
    if (value && rules.pattern && !rules.pattern.test(value)) {
      showError(field, rules.errorMessages.pattern);
      return false;
    }

    // Check min length
    if (value && rules.minLength && value.length < rules.minLength) {
      showError(field, rules.errorMessages.minLength);
      return false;
    }

    // Check max length
    if (value && rules.maxLength && value.length > rules.maxLength) {
      showError(field, rules.errorMessages.maxLength);
      return false;
    }

    hideError(field);
    return true;
  }

  // File handling
  let uploadedFiles = [];

  function handleFiles(files) {
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    Array.from(files).forEach(file => {
      if (!allowedTypes.includes(file.type)) {
        alert(`نوع الملف غير مدعوم: ${file.name}`);
        return;
      }

      if (file.size > maxSize) {
        alert(`حجم الملف كبير جداً: ${file.name}`);
        return;
      }

      uploadedFiles.push(file);
      displayFile(file);
    });
  }

  function displayFile(file) {
    const fileDiv = document.createElement('div');
    fileDiv.className = 'flex items-center justify-between p-3 bg-gray-50 rounded-lg';
    
    fileDiv.innerHTML = `
      <div class="flex items-center gap-3">
        <i class="fas fa-file text-primary-blue"></i>
        <span class="text-sm text-gray-700">${file.name}</span>
        <span class="text-xs text-gray-500">(${(file.size / 1024).toFixed(1)} KB)</span>
      </div>
      <button type="button" class="text-red-500 hover:text-red-700 transition-colors" onclick="removeFile('${file.name}')">
        <i class="fas fa-times"></i>
      </button>
    `;
    
    fileList.appendChild(fileDiv);
  }

  // Make removeFile function global
  window.removeFile = function(fileName) {
    uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
    const fileElements = fileList.querySelectorAll('div');
    fileElements.forEach(element => {
      if (element.textContent.includes(fileName)) {
        element.remove();
      }
    });
  };

  // Drag and drop functionality
  if (dropZone) {
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropZone.classList.add('border-primary-blue', 'bg-blue-50');
    });

    dropZone.addEventListener('dragleave', (e) => {
      e.preventDefault();
      dropZone.classList.remove('border-primary-blue', 'bg-blue-50');
    });

    dropZone.addEventListener('drop', (e) => {
      e.preventDefault();
      dropZone.classList.remove('border-primary-blue', 'bg-blue-50');
      handleFiles(e.dataTransfer.files);
    });

    dropZone.addEventListener('click', () => {
      fileInput.click();
    });
  }

  // File input change
  if (fileInput) {
    fileInput.addEventListener('change', (e) => {
      handleFiles(e.target.files);
    });
  }

  // Real-time validation for required fields
  if (clientName) {
    clientName.addEventListener('input', () => {
      validateField(clientName, validationRules.clientName);
    });
  }

  if (email) {
    email.addEventListener('input', () => {
      validateField(email, validationRules.email);
    });
  }

  if (phoneNumber) {
    phoneNumber.addEventListener('input', () => {
      validateField(phoneNumber, validationRules.phoneNumber);
    });
  }

  // Real-time validation for optional fields
  if (workTime) {
    workTime.addEventListener('input', () => {
      validateField(workTime, validationRules.workTime);
    });
  }

  if (branchAddresses) {
    branchAddresses.addEventListener('input', () => {
      validateField(branchAddresses, validationRules.branchAddresses);
    });
  }

  if (yourServices) {
    yourServices.addEventListener('input', () => {
      validateField(yourServices, validationRules.yourServices);
    });
  }

  if (socialMedia) {
    socialMedia.addEventListener('input', () => {
      validateField(socialMedia, validationRules.socialMedia);
    });
  }

  if (specialOffers) {
    specialOffers.addEventListener('input', () => {
      validateField(specialOffers, validationRules.specialOffers);
    });
  }

  if (competitiveFeatures) {
    competitiveFeatures.addEventListener('input', () => {
      validateField(competitiveFeatures, validationRules.competitiveFeatures);
    });
  }

  // Form submission
  form.addEventListener('submit', (e) => {
    e.preventDefault();

    // Validate required fields
    const isClientNameValid = validateField(clientName, validationRules.clientName);
    const isEmailValid = validateField(email, validationRules.email);
    const isPhoneValid = validateField(phoneNumber, validationRules.phoneNumber);

    // Validate optional fields
    const isWorkTimeValid = validateField(workTime, validationRules.workTime);
    const isBranchAddressesValid = validateField(branchAddresses, validationRules.branchAddresses);
    const isYourServicesValid = validateField(yourServices, validationRules.yourServices);
    const isSocialMediaValid = validateField(socialMedia, validationRules.socialMedia);
    const isSpecialOffersValid = validateField(specialOffers, validationRules.specialOffers);
    const isCompetitiveFeaturesValid = validateField(competitiveFeatures, validationRules.competitiveFeatures);

    const isFormValid = isClientNameValid && isEmailValid && isPhoneValid && 
                       isWorkTimeValid && isBranchAddressesValid && isYourServicesValid && 
                       isSocialMediaValid && isSpecialOffersValid && isCompetitiveFeaturesValid;

    if (isFormValid) {
      // Collect form data
      const formData = new FormData();
      formData.append('clientName', clientName.value.trim());
      formData.append('email', email.value.trim());
      formData.append('phoneNumber', phoneNumber.value.trim());
      formData.append('countryCode', countryCode.value);
      formData.append('workTime', workTime.value.trim());
      formData.append('branchAddresses', branchAddresses.value.trim());
      formData.append('yourServices', yourServices.value.trim());
      formData.append('socialMedia', socialMedia.value.trim());
      formData.append('specialOffers', specialOffers.value.trim());
      formData.append('competitiveFeatures', competitiveFeatures.value.trim());

      // Add files
      uploadedFiles.forEach((file, index) => {
        formData.append(`file_${index}`, file);
      });

      // Show success message (replace with actual submission logic)
      alert('تم إرسال النموذج بنجاح! سنتواصل معك قريباً.');
      
      // Reset form
      form.reset();
      uploadedFiles = [];
      fileList.innerHTML = '';
      
      // Hide all error messages
      const errorMessages = form.querySelectorAll('.error-message');
      errorMessages.forEach(error => error.classList.add('hidden'));
      
      // Reset field styles
      const inputs = form.querySelectorAll('input, textarea');
      inputs.forEach(input => {
        input.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
        input.classList.add('border-gray', 'focus:ring-primary-blue', 'focus:border-transparent');
      });
    } else {
      // Focus on first invalid field
      const firstInvalidField = form.querySelector('.border-red-500');
      if (firstInvalidField) {
        firstInvalidField.focus();
        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  });
}
