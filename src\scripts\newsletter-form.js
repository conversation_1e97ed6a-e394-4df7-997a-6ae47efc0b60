// Newsletter Form Validation and Submission
export function initNewsletterForm() {
  const form = document.getElementById('newsletterForm');

  if (!form) {
    return;
  }


  // Get form elements
  const emailInput = form.querySelector('#newsletterEmail');
  const submitButton = form.querySelector('button[type="submit"]');
  const errorSpan = form.querySelector('#newsletterError');
  const successSpan = form.querySelector('#newsletterSuccess');

  // Email validation pattern
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Show error message
  function showError(message) {
    if (errorSpan) {
      errorSpan.textContent = message;
      errorSpan.classList.remove('hidden');
    }
    if (successSpan) {
      successSpan.classList.add('hidden');
    }
    emailInput.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    emailInput.classList.remove('border-gray-300', 'focus:ring-primary-blue', 'focus:border-primary-blue');
  }

  // Show success message
  function showSuccess(message) {
    if (successSpan) {
      successSpan.textContent = message;
      successSpan.classList.remove('hidden');
    }
    if (errorSpan) {
      errorSpan.classList.add('hidden');
    }
    emailInput.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    emailInput.classList.add('border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
  }

  // Hide messages
  function hideMessages() {
    if (errorSpan) {
      errorSpan.classList.add('hidden');
    }
    if (successSpan) {
      successSpan.classList.add('hidden');
    }
    emailInput.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500', 'border-green-500', 'focus:ring-green-500', 'focus:border-green-500');
    emailInput.classList.add('border-gray-300', 'focus:ring-primary-blue', 'focus:border-primary-blue');
  }

  // Validate email
  function validateEmail(email) {
    if (!email.trim()) {
      return 'البريد الإلكتروني مطلوب';
    }

    if (!emailPattern.test(email)) {
      return 'يرجى إدخال بريد إلكتروني صحيح';
    }

    return null;
  }

  // Real-time validation on input
  emailInput.addEventListener('input', () => {
    const email = emailInput.value.trim();

    if (email === '') {
      hideMessages();
      return;
    }

    const error = validateEmail(email);
    if (error) {
      showError(error);
    } else {
      hideMessages();
    }
  });

  // Form submission
  form.addEventListener('submit', (e) => {
    e.preventDefault();

    const email = emailInput.value.trim();
    const error = validateEmail(email);

    if (error) {
      showError(error);
      emailInput.focus();
      return;
    }

    // Show loading state
    submitButton.disabled = true;
    submitButton.textContent = 'جاري الإشتراك...';
    submitButton.classList.add('opacity-75', 'cursor-not-allowed');

    // Simulate form submission
    setTimeout(() => {
      // Show success message
      showSuccess('تم الاشتراك بنجاح! شكراً لك');

      // Reset form
      emailInput.value = '';

      // Reset button
      submitButton.disabled = false;
      submitButton.textContent = 'إشتراك';
      submitButton.classList.remove('opacity-75', 'cursor-not-allowed');

      // Show success notification
      showSuccessNotification();


      // Hide success message after 3 seconds
      setTimeout(() => {
        hideMessages();
      }, 3000);
    }, 1500);
  });

  // Show success notification
  function showSuccessNotification() {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    successDiv.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span>تم الاشتراك في النشرة الإخبارية بنجاح!</span>
      </div>
    `;

    document.body.appendChild(successDiv);

    // Animate in
    setTimeout(() => {
      successDiv.classList.remove('translate-x-full');
    }, 100);

    // Remove after 4 seconds
    setTimeout(() => {
      successDiv.classList.add('translate-x-full');
      setTimeout(() => {
        if (document.body.contains(successDiv)) {
          document.body.removeChild(successDiv);
        }
      }, 300);
    }, 4000);
  }

  // Focus styling
  emailInput.addEventListener('focus', () => {
    if (!emailInput.classList.contains('border-red-500') && !emailInput.classList.contains('border-green-500')) {
      emailInput.classList.add('ring-2', 'ring-primary-blue', 'ring-opacity-50');
    }
  });

  emailInput.addEventListener('blur', () => {
    emailInput.classList.remove('ring-2', 'ring-primary-blue', 'ring-opacity-50');
  });

}
