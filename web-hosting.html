<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />

    <!-- font awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
      integrity="sha512-..."
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <title>Serv5</title>
    <style>
      /* Override template-actions width for hosting buttons */
      @media (max-width: 768px) {
        .hosting-btn.btn-purchase {
          width: auto !important;
          max-width: fit-content !important;
        }
      }
    </style>
  </head>

  <body
    class="flex flex-col gap-4 md:gap-8 lg:gap-12 2xl:gap-16 overflow-x-hidden"
  >
    <header class="bg-main-blue relative">
      <!-- start of the  logo -->
      <div class="container flex justify-between h-[127px] items-center">
        <svg
          width="101"
          height="98"
          class="size-[98px] header-logo"
          viewBox="0 0 101 98"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <!-- Outer circle (first to animate) -->
          <path
            id="logo-outer-circle"
            d="M84.9157 80.1711C76.8332 87.5147 65.6703 92.0596 53.3487 92.0596C28.7056 92.0596 8.69727 73.8802 8.69727 51.4897C8.69727 29.0993 28.7056 10.9199 53.3487 10.9199"
            stroke="#DE8545"
            stroke-width="4.6875"
            stroke-linecap="round"
            fill="none"
          />
          <!-- Inner circle (second to animate) -->
          <path
            id="logo-inner-circle"
            d="M53.3486 10.9199C77.9917 10.9199 98 29.0993 98 51.4897C98 73.8802 77.9917 92.0596 53.3486 92.0596C48.5334 92.0596 43.8953 91.3655 39.5488 90.0816"
            stroke="#DE8545"
            stroke-width="4.6875"
            stroke-linecap="round"
            fill="none"
          />
          <!-- Base rectangle (third to animate) -->
          <path
            id="logo-base-rectangle"
            d="M52.2359 98.0003H11.1552C5.01991 98.0003 0 93.4395 0 87.865C0 82.2905 5.01991 77.7295 11.1552 77.7295H52.2359C58.3713 77.7295 63.3911 82.2905 63.3911 87.865C63.3911 93.4395 58.3713 98.0003 52.2359 98.0003Z"
            fill="#15205C"
            stroke="#DE8545"
            stroke-width="0"
          />
          <!-- Text/Icons (fourth to animate) -->
          <g id="logo-text-icons">
            <path
              class="logo-letter"
              d="M14.4084 85.3265C14.1143 84.3605 13.5939 83.2712 12.1689 83.2712C10.9021 83.2712 10.3027 84.0831 10.3027 84.9051C10.3027 85.9635 11.0831 86.4774 12.4629 87.0837C13.8994 87.7208 15.3923 88.4299 15.3923 90.0845C15.3923 91.6978 13.8767 92.9824 11.5694 92.9824C10.8681 92.9824 10.3027 92.8694 9.87296 92.7358C9.44313 92.6124 9.20561 92.4891 9.03603 92.4171C8.92287 92.1191 8.67414 90.7524 8.57227 90.0227L9.08116 89.8891C9.3639 90.8449 10.1896 92.4171 11.9202 92.4171C13.1529 92.4171 13.8768 91.7286 13.8768 90.6495C13.8768 89.5603 12.972 89.0465 11.6826 88.4299C10.5063 87.8956 8.88894 87.1454 8.88894 85.4908C8.88894 83.9905 10.2235 82.7471 12.429 82.7471C13.2321 82.7471 13.9898 82.9218 14.6232 83.1068C14.691 83.6411 14.7702 84.2577 14.9172 85.2442L14.4084 85.3265Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M25.1869 90.3099C25.0738 90.8751 24.7457 92.2625 24.5987 92.7454H16.2969V92.3138C17.8238 92.2111 17.9708 92.0672 17.9708 90.834V84.9456C17.9708 83.5685 17.8238 83.5275 16.5118 83.4247V82.9727H24.1802C24.2028 83.363 24.2706 84.3599 24.3386 85.2025L23.8184 85.2643C23.66 84.6373 23.4789 84.288 23.2641 83.9591C23.0152 83.6303 22.5515 83.5275 21.3866 83.5275H20.131C19.5768 83.5275 19.543 83.5685 19.543 84.0207V87.3913H21.0925C22.608 87.3913 22.7211 87.2682 22.936 86.2507H23.445V89.2001H22.936C22.6986 88.1004 22.5741 88.0183 21.0925 88.0183H19.543V90.7826C19.543 91.4917 19.6335 91.8308 19.9501 92.0158C20.2781 92.1905 20.9115 92.1905 21.6806 92.1905C22.9134 92.1905 23.4223 92.0877 23.8069 91.6972C24.101 91.3787 24.4176 90.8545 24.6665 90.2482L25.1869 90.3099Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M36.2622 92.879C36.0586 92.879 35.8664 92.8688 35.6628 92.8482C34.2942 92.7865 33.593 92.4577 32.8126 91.4712C32.2358 90.7415 31.6816 89.8269 31.1726 89.1076C30.8672 88.6759 30.5732 88.491 29.7022 88.491H29.2273V90.834C29.2273 92.0979 29.3856 92.2111 30.7428 92.3138V92.7454H26.1621V92.3138C27.5307 92.2111 27.689 92.0979 27.689 90.834V84.8737C27.689 83.5891 27.542 83.5069 26.23 83.4247V82.9727H30.5618C31.8399 82.9727 32.6656 83.1062 33.3102 83.4966C34.0001 83.8872 34.43 84.5552 34.43 85.4697C34.43 86.7748 33.5026 87.5558 32.2923 88.0183C32.5864 88.5115 33.2763 89.4981 33.7853 90.1866C34.396 90.9779 34.7579 91.3992 35.1538 91.7692C35.5836 92.2111 35.9568 92.3755 36.3414 92.4577L36.2622 92.879ZM29.985 87.9874C30.8333 87.9874 31.3987 87.864 31.8286 87.5558C32.4732 87.1037 32.7335 86.5179 32.7335 85.7061C32.7335 84.0926 31.5685 83.4966 30.3129 83.4966C29.804 83.4966 29.5326 83.5584 29.4082 83.6405C29.295 83.7228 29.2273 83.8974 29.2273 84.288V87.9874H29.985Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M47.2054 83.4247C46.0518 83.5377 45.8141 83.7021 45.1468 85.0792C44.683 86.0555 42.9187 89.7961 41.6066 92.9098H41.0298C39.8875 90.1558 38.4511 86.9188 37.5011 84.7812C36.992 83.6612 36.7093 83.5069 35.6914 83.435V82.9727H40.0007V83.4247C38.7678 83.5377 38.7566 83.6713 39.0167 84.3496C39.4804 85.4286 40.7472 88.3573 41.8216 90.7723H41.8441C42.7716 88.7377 43.9593 85.9937 44.4795 84.6065C44.8188 83.7227 44.7283 83.5685 43.3484 83.4247V82.9727H47.2054V83.4247Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M49.5919 87.3093C50.2818 87.0729 51.1075 86.8365 51.5485 86.8365C53.6974 86.8365 54.8173 88.1211 54.8173 89.3645C54.8173 90.2791 54.3309 91.1115 53.2791 91.8822C52.3629 92.5193 51.2206 92.9202 50.3157 92.9202C49.558 92.9202 48.8453 92.6221 48.574 92.3962C48.3251 92.1803 48.2346 92.057 48.2686 91.8616C48.2686 91.6973 48.4382 91.4609 48.6532 91.3068C48.8115 91.204 48.9472 91.204 49.1168 91.3274C49.49 91.6253 50.2026 92.1494 51.2545 92.1494C52.623 92.1494 53.2791 91.0807 53.2791 89.9914C53.2791 88.6555 52.3629 87.7306 50.5646 87.7306C49.8407 87.7306 49.1961 87.9155 48.8002 88.0286L49.4788 83.4248H54.3987L54.5005 83.5378L54.0368 84.6168H50.033L49.5919 87.3093Z"
              fill="#DE8545"
            />
            <path
              class="logo-icon"
              d="M55.4247 0H56.7964C56.8458 0.0246814 56.8921 0.0596832 56.945 0.0727212C59.5635 0.716344 60.7644 2.86669 59.7677 5.14651C59.2144 6.41208 58.6382 7.66938 58.0845 8.93474C57.8685 9.42846 57.6858 9.93419 57.4878 10.4344C57.407 10.5157 57.2931 10.5848 57.2509 10.6801C56.9786 11.2952 56.7216 11.9157 56.4603 12.5348C56.0419 13.5264 55.6241 14.5184 55.2061 15.5101C55.1242 15.6648 55.0325 15.8159 54.9619 15.9746C53.9279 18.2971 52.8966 20.6206 51.8647 22.9438C50.8327 25.2672 49.8065 27.5928 48.767 29.9134C48.0078 31.6082 46.1724 32.5722 44.3537 32.2499C41.7707 31.7922 40.3793 29.507 41.3334 27.2672C41.8675 26.0133 42.4286 24.7688 42.9671 23.544C42.8816 23.1901 42.8147 22.8671 42.7252 22.5494C42.2066 20.7099 41.4807 18.9575 40.1214 17.4737C39.5633 16.9424 39.0463 16.368 38.4381 15.8892C37.2331 14.9402 35.8555 14.2739 34.2565 14.0419C31.5012 13.5398 28.9524 13.9091 26.7367 15.5686C25.3568 16.6021 24.4801 17.8941 24.4541 19.5752C24.4129 19.7185 24.3246 19.8657 24.3389 20.0042C24.3947 20.5443 24.4363 21.0913 24.5686 21.618C24.8788 22.8525 25.4523 23.9791 26.391 24.934C26.5235 25.1351 26.629 25.3567 26.7927 25.5338C27.7984 26.6223 28.9601 27.5432 30.286 28.3095C31.9011 29.2429 33.4741 30.2363 35.0684 31.1997C36.1384 31.8463 37.2158 32.4827 38.2861 33.129C41.3919 35.004 44.5448 36.833 47.2642 39.168C48.9088 40.58 50.4845 42.0582 51.7042 43.8387C52.6133 45.1656 53.3738 46.5389 53.9229 48.0006C54.8725 50.5281 55.15 53.1352 54.8573 55.7992C54.4484 59.5217 53.0273 62.8719 50.2761 65.6639C44.3354 71.6926 36.7972 73.6354 28.1724 72.5071C26.3798 72.2726 24.6425 71.6955 22.8748 71.2943C22.1074 71.1202 21.3274 70.9917 20.6339 70.858C20.0577 71.2993 19.7734 71.7973 19.6408 72.4982C18.4475 74.6642 17.0417 75.3768 14.7209 74.9923C14.5521 74.919 14.3852 74.8417 14.2142 74.773C12.8143 74.2108 12.0251 73.218 11.8234 71.8615C11.7541 71.395 11.8539 70.9078 11.8764 70.4299C12.8938 68.1477 13.9111 65.8655 14.9285 63.5832C15.8547 61.5287 16.7885 59.477 17.7052 57.419C19.0576 54.3825 20.3931 51.3396 21.7453 48.303C22.3909 46.8529 23.5788 45.9991 25.3014 45.929C28.1445 45.8131 30.2687 48.261 29.1576 50.8921C27.7091 54.3216 26.1434 57.7101 24.6285 61.1165C24.3521 61.7382 24.0772 62.3605 23.7801 63.0314C24.2092 63.3795 24.5757 63.7598 25.0242 64.0258C26.6954 65.017 28.4817 65.7478 30.506 65.9144C34.0947 66.2098 37.2482 65.4815 39.5056 62.7212C41.5034 60.2783 41.8772 57.6559 40.4039 54.8734C39.5932 53.3425 38.4358 52.0404 37.1359 50.8381C34.6009 48.4935 31.6556 46.6094 28.7373 44.6965C25.5967 42.6377 22.4437 40.5943 19.316 38.5195C18.4452 37.9419 17.6447 37.2771 16.812 36.6522C16.2078 36.0812 15.5446 35.5518 15.0138 34.9293C14.2637 34.0495 13.6115 33.1009 12.9174 32.1817C12.9045 32.1137 12.9069 32.0394 12.8765 31.9786C11.8476 29.9147 11.4662 27.7315 11.4016 25.4855C11.3013 21.9913 12.0287 18.6743 14.0017 15.6403C16.244 12.1918 19.4768 9.77352 23.4851 8.1489C26.5217 6.91818 29.7003 6.28437 33.0012 6.13835C37.615 5.93425 42.0943 6.61149 46.4615 7.94561C47.4594 8.25054 48.4478 8.58132 49.4626 8.90727C50.3527 6.90236 51.28 4.95216 52.0829 2.9605C52.6895 1.45553 53.6803 0.399987 55.4247 0Z"
              fill="#2458A7"
            />
            <path
              class="logo-icon"
              d="M61.4293 17.3108H66.2065C68.3308 17.3197 70.4552 17.3357 72.5795 17.3357C74.7416 17.3356 76.9038 17.3196 79.0659 17.3105C80.1093 17.3105 81.1528 17.3105 82.2397 17.3105C82.4814 16.6209 82.7241 15.9357 82.9617 15.2491C83.6356 13.3025 85.8093 12.2428 87.9217 12.8328C90.0475 13.4266 91.2142 15.4589 90.548 17.4087C89.7385 19.778 88.9285 22.1472 88.1187 24.5164C87.5134 26.2267 86.908 27.937 86.3031 29.6473C86.2169 29.8911 86.1333 30.1356 86.0485 30.3797C86.0028 30.4334 85.9527 30.4845 85.9121 30.5412C84.7456 32.1723 82.6243 32.7534 80.7813 31.9456C78.9182 31.1292 78.0841 29.2351 78.762 27.384C79.0378 26.6307 79.2925 25.8709 79.5526 25.1128C79.6461 24.8404 79.7269 24.5645 79.8383 24.2116H66.8508C65.473 27.6652 64.0931 31.1242 62.6844 34.6553C62.9697 34.6116 63.1348 34.5891 63.2987 34.5608C67.7437 33.7909 72.1577 33.8927 76.5196 35.0112C82.5933 36.5687 87.6532 39.4903 91.2743 44.269C92.121 45.3864 92.8161 46.5986 93.581 47.7673C95.0354 50.8007 96.0776 53.9422 95.9567 57.2763C95.6538 65.6337 90.9819 72.7107 83.0579 77.1835C82.8326 77.3107 83.2826 77.0557 83.0579 77.1835C83.0075 77.2147 83.1082 77.1523 83.0579 77.1835C83.0179 77.2229 83.0979 77.1443 83.0579 77.1835C82.7679 77.285 83.3335 77.0565 83.0579 77.1835C79.4099 78.8651 74.6148 80.1694 70.5251 80.0128C62.0075 79.6865 55.0993 76.5363 50.0989 70.157C50.0774 70.1297 50.0675 70.0948 50.0293 70.0163C51.519 68.5236 53.0239 67.0157 54.5414 65.4952C55.1319 65.5267 55.7173 65.5579 56.1404 65.5805C57.2816 66.7397 58.2374 67.901 59.3971 68.8584C69.2744 77.0136 83.4219 72.2073 87.0103 62.1986C90.2255 53.2312 83.6993 43.3419 73.3653 41.5232C69.705 40.879 66.0924 41.1678 62.5053 41.92C60.9152 42.2534 59.353 42.6967 57.7781 43.0898C57.5969 43.1351 57.4151 43.1785 57.2096 43.2286C55.6099 41.9478 54.0152 40.6708 52.4036 39.3805C53.1658 37.4712 53.9153 35.5908 54.667 33.7109C56.4433 29.2687 58.2394 24.833 59.9756 20.378"
              fill="#DE8545"
            />
          </g>

          <!-- Gradient definition for shine effect -->
          <defs>
            <linearGradient
              id="shineGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              <stop offset="0%" style="stop-color: white; stop-opacity: 0" />
              <stop offset="50%" style="stop-color: white; stop-opacity: 0.2" />
              <stop offset="100%" style="stop-color: white; stop-opacity: 0" />
            </linearGradient>
          </defs>

          <!-- Shine effect overlay (hidden initially) -->
          <rect
            id="logo-shine"
            x="-20"
            y="0"
            width="20"
            height="98"
            fill="url(#shineGradient)"
            opacity="0"
          />
        </svg>

        <!-- end of the  logo -->

        <!-- links -->
        <ul class="hidden lg:flex items-center gap-10 text-white font-light">
          <!-- main -->
          <li
            class="nav-item active text-primary-yellow flex gap-1.5 items-center relative font-semibold transition-all duration-300"
          >
            <a href="/" class="flex gap-2 items-center">
              <i class="fa-solid fa-house"></i>
              <span data-nav="home">الرئيسية</span>
            </a>
            <!-- floating arrow -->
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[60px] h-[20px] absolute -bottom-5 left-1/2 animate-scale-x opacity-100"
            />
          </li>
          <!--نبذة عنا  -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-5"></i>
              <span data-nav="about">نبذة عنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- خدماتنا   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-plus"></i>
              <span data-nav="services">خدماتنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- أعمالــنا   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-briefcase"></i>
              <span data-nav="portfolio">أعمالــنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- المدونــة   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-regular fa-rectangle-list"></i>
              <span data-nav="blog">المدونــة</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- الكتيب التعريفي   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-file-pdf"></i>
              <span data-nav="brochure">الكتيب التعريفي</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
        </ul>
        <!-- Desktop Navigation -->
        <div class="hidden lg:flex gap-2 items-center">
          <!-- button to switch lang  -->
          <button
            id="langToggle"
            class="rounded-md text-primary-blue font-bold bg-white p-4 hover:bg-gray-100 transition-all duration-300 header-button"
          >
            EN
          </button>
          <!-- company profile button  -->
          <button
            class="rounded-md flex items-center gap-2 text-white border border-white font-bold bg-transparent p-4 hover:bg-white hover:text-primary-blue transition-all duration-300 header-button header-profile-btn"
          >
            <i class="fa-solid fa-book"></i>
            <span data-nav="profile">بروفايل الشركة</span>
          </button>

          <!-- our vision -->
          <img
            src="/public/shared/header/our-vision.webp"
            alt="Serv5 future vision for 2030"
            class="w-[125px] h-[84px] ms-8 header-vision"
          />
        </div>

        <!-- Mobile Navigation -->
        <div class="lg:hidden flex items-center gap-4">
          <!-- Mobile Language Toggle -->
          <button
            id="mobileLangToggle"
            class="rounded-md text-primary-blue font-bold bg-white p-3 hover:bg-gray-100 transition-all duration-300 text-sm"
          >
            EN
          </button>

          <!-- Burger Menu Button -->
          <button
            id="burgerMenuBtn"
            class="relative w-8 h-8 flex flex-col justify-center items-center space-y-1 focus:outline-none burger-menu-btn"
            aria-label="Toggle mobile menu"
          >
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
          </button>
        </div>
      </div>

      <!-- Mobile Menu Overlay -->
      <div
        id="mobileMenuOverlay"
        class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden opacity-0 pointer-events-none transition-opacity duration-300"
      ></div>

      <!-- Mobile Menu -->
      <div
        id="mobileMenu"
        class="fixed top-0 w-80 h-full bg-main-blue z-50 lg:hidden transform transition-transform duration-300 ease-in-out mobile-menu"
      >
        <!-- Mobile Menu Header -->
        <div
          class="flex items-center justify-between p-6 border-b border-gray-700"
        >
          <div class="flex items-center gap-3">
            <svg
              width="101"
              height="98"
              class="size-[98px] header-logo"
              viewBox="0 0 101 98"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <!-- Outer circle (first to animate) -->
              <path
                id="logo-outer-circle"
                d="M84.9157 80.1711C76.8332 87.5147 65.6703 92.0596 53.3487 92.0596C28.7056 92.0596 8.69727 73.8802 8.69727 51.4897C8.69727 29.0993 28.7056 10.9199 53.3487 10.9199"
                stroke="#DE8545"
                stroke-width="4.6875"
                stroke-linecap="round"
                fill="none"
              />
              <!-- Inner circle (second to animate) -->
              <path
                id="logo-inner-circle"
                d="M53.3486 10.9199C77.9917 10.9199 98 29.0993 98 51.4897C98 73.8802 77.9917 92.0596 53.3486 92.0596C48.5334 92.0596 43.8953 91.3655 39.5488 90.0816"
                stroke="#DE8545"
                stroke-width="4.6875"
                stroke-linecap="round"
                fill="none"
              />
              <!-- Base rectangle (third to animate) -->
              <path
                id="logo-base-rectangle"
                d="M52.2359 98.0003H11.1552C5.01991 98.0003 0 93.4395 0 87.865C0 82.2905 5.01991 77.7295 11.1552 77.7295H52.2359C58.3713 77.7295 63.3911 82.2905 63.3911 87.865C63.3911 93.4395 58.3713 98.0003 52.2359 98.0003Z"
                fill="#15205C"
                stroke="#DE8545"
                stroke-width="0"
              />
              <!-- Text/Icons (fourth to animate) -->
              <g id="logo-text-icons">
                <path
                  class="logo-letter"
                  d="M14.4084 85.3265C14.1143 84.3605 13.5939 83.2712 12.1689 83.2712C10.9021 83.2712 10.3027 84.0831 10.3027 84.9051C10.3027 85.9635 11.0831 86.4774 12.4629 87.0837C13.8994 87.7208 15.3923 88.4299 15.3923 90.0845C15.3923 91.6978 13.8767 92.9824 11.5694 92.9824C10.8681 92.9824 10.3027 92.8694 9.87296 92.7358C9.44313 92.6124 9.20561 92.4891 9.03603 92.4171C8.92287 92.1191 8.67414 90.7524 8.57227 90.0227L9.08116 89.8891C9.3639 90.8449 10.1896 92.4171 11.9202 92.4171C13.1529 92.4171 13.8768 91.7286 13.8768 90.6495C13.8768 89.5603 12.972 89.0465 11.6826 88.4299C10.5063 87.8956 8.88894 87.1454 8.88894 85.4908C8.88894 83.9905 10.2235 82.7471 12.429 82.7471C13.2321 82.7471 13.9898 82.9218 14.6232 83.1068C14.691 83.6411 14.7702 84.2577 14.9172 85.2442L14.4084 85.3265Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M25.1869 90.3099C25.0738 90.8751 24.7457 92.2625 24.5987 92.7454H16.2969V92.3138C17.8238 92.2111 17.9708 92.0672 17.9708 90.834V84.9456C17.9708 83.5685 17.8238 83.5275 16.5118 83.4247V82.9727H24.1802C24.2028 83.363 24.2706 84.3599 24.3386 85.2025L23.8184 85.2643C23.66 84.6373 23.4789 84.288 23.2641 83.9591C23.0152 83.6303 22.5515 83.5275 21.3866 83.5275H20.131C19.5768 83.5275 19.543 83.5685 19.543 84.0207V87.3913H21.0925C22.608 87.3913 22.7211 87.2682 22.936 86.2507H23.445V89.2001H22.936C22.6986 88.1004 22.5741 88.0183 21.0925 88.0183H19.543V90.7826C19.543 91.4917 19.6335 91.8308 19.9501 92.0158C20.2781 92.1905 20.9115 92.1905 21.6806 92.1905C22.9134 92.1905 23.4223 92.0877 23.8069 91.6972C24.101 91.3787 24.4176 90.8545 24.6665 90.2482L25.1869 90.3099Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M36.2622 92.879C36.0586 92.879 35.8664 92.8688 35.6628 92.8482C34.2942 92.7865 33.593 92.4577 32.8126 91.4712C32.2358 90.7415 31.6816 89.8269 31.1726 89.1076C30.8672 88.6759 30.5732 88.491 29.7022 88.491H29.2273V90.834C29.2273 92.0979 29.3856 92.2111 30.7428 92.3138V92.7454H26.1621V92.3138C27.5307 92.2111 27.689 92.0979 27.689 90.834V84.8737C27.689 83.5891 27.542 83.5069 26.23 83.4247V82.9727H30.5618C31.8399 82.9727 32.6656 83.1062 33.3102 83.4966C34.0001 83.8872 34.43 84.5552 34.43 85.4697C34.43 86.7748 33.5026 87.5558 32.2923 88.0183C32.5864 88.5115 33.2763 89.4981 33.7853 90.1866C34.396 90.9779 34.7579 91.3992 35.1538 91.7692C35.5836 92.2111 35.9568 92.3755 36.3414 92.4577L36.2622 92.879ZM29.985 87.9874C30.8333 87.9874 31.3987 87.864 31.8286 87.5558C32.4732 87.1037 32.7335 86.5179 32.7335 85.7061C32.7335 84.0926 31.5685 83.4966 30.3129 83.4966C29.804 83.4966 29.5326 83.5584 29.4082 83.6405C29.295 83.7228 29.2273 83.8974 29.2273 84.288V87.9874H29.985Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M47.2054 83.4247C46.0518 83.5377 45.8141 83.7021 45.1468 85.0792C44.683 86.0555 42.9187 89.7961 41.6066 92.9098H41.0298C39.8875 90.1558 38.4511 86.9188 37.5011 84.7812C36.992 83.6612 36.7093 83.5069 35.6914 83.435V82.9727H40.0007V83.4247C38.7678 83.5377 38.7566 83.6713 39.0167 84.3496C39.4804 85.4286 40.7472 88.3573 41.8216 90.7723H41.8441C42.7716 88.7377 43.9593 85.9937 44.4795 84.6065C44.8188 83.7227 44.7283 83.5685 43.3484 83.4247V82.9727H47.2054V83.4247Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M49.5919 87.3093C50.2818 87.0729 51.1075 86.8365 51.5485 86.8365C53.6974 86.8365 54.8173 88.1211 54.8173 89.3645C54.8173 90.2791 54.3309 91.1115 53.2791 91.8822C52.3629 92.5193 51.2206 92.9202 50.3157 92.9202C49.558 92.9202 48.8453 92.6221 48.574 92.3962C48.3251 92.1803 48.2346 92.057 48.2686 91.8616C48.2686 91.6973 48.4382 91.4609 48.6532 91.3068C48.8115 91.204 48.9472 91.204 49.1168 91.3274C49.49 91.6253 50.2026 92.1494 51.2545 92.1494C52.623 92.1494 53.2791 91.0807 53.2791 89.9914C53.2791 88.6555 52.3629 87.7306 50.5646 87.7306C49.8407 87.7306 49.1961 87.9155 48.8002 88.0286L49.4788 83.4248H54.3987L54.5005 83.5378L54.0368 84.6168H50.033L49.5919 87.3093Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-icon"
                  d="M55.4247 0H56.7964C56.8458 0.0246814 56.8921 0.0596832 56.945 0.0727212C59.5635 0.716344 60.7644 2.86669 59.7677 5.14651C59.2144 6.41208 58.6382 7.66938 58.0845 8.93474C57.8685 9.42846 57.6858 9.93419 57.4878 10.4344C57.407 10.5157 57.2931 10.5848 57.2509 10.6801C56.9786 11.2952 56.7216 11.9157 56.4603 12.5348C56.0419 13.5264 55.6241 14.5184 55.2061 15.5101C55.1242 15.6648 55.0325 15.8159 54.9619 15.9746C53.9279 18.2971 52.8966 20.6206 51.8647 22.9438C50.8327 25.2672 49.8065 27.5928 48.767 29.9134C48.0078 31.6082 46.1724 32.5722 44.3537 32.2499C41.7707 31.7922 40.3793 29.507 41.3334 27.2672C41.8675 26.0133 42.4286 24.7688 42.9671 23.544C42.8816 23.1901 42.8147 22.8671 42.7252 22.5494C42.2066 20.7099 41.4807 18.9575 40.1214 17.4737C39.5633 16.9424 39.0463 16.368 38.4381 15.8892C37.2331 14.9402 35.8555 14.2739 34.2565 14.0419C31.5012 13.5398 28.9524 13.9091 26.7367 15.5686C25.3568 16.6021 24.4801 17.8941 24.4541 19.5752C24.4129 19.7185 24.3246 19.8657 24.3389 20.0042C24.3947 20.5443 24.4363 21.0913 24.5686 21.618C24.8788 22.8525 25.4523 23.9791 26.391 24.934C26.5235 25.1351 26.629 25.3567 26.7927 25.5338C27.7984 26.6223 28.9601 27.5432 30.286 28.3095C31.9011 29.2429 33.4741 30.2363 35.0684 31.1997C36.1384 31.8463 37.2158 32.4827 38.2861 33.129C41.3919 35.004 44.5448 36.833 47.2642 39.168C48.9088 40.58 50.4845 42.0582 51.7042 43.8387C52.6133 45.1656 53.3738 46.5389 53.9229 48.0006C54.8725 50.5281 55.15 53.1352 54.8573 55.7992C54.4484 59.5217 53.0273 62.8719 50.2761 65.6639C44.3354 71.6926 36.7972 73.6354 28.1724 72.5071C26.3798 72.2726 24.6425 71.6955 22.8748 71.2943C22.1074 71.1202 21.3274 70.9917 20.6339 70.858C20.0577 71.2993 19.7734 71.7973 19.6408 72.4982C18.4475 74.6642 17.0417 75.3768 14.7209 74.9923C14.5521 74.919 14.3852 74.8417 14.2142 74.773C12.8143 74.2108 12.0251 73.218 11.8234 71.8615C11.7541 71.395 11.8539 70.9078 11.8764 70.4299C12.8938 68.1477 13.9111 65.8655 14.9285 63.5832C15.8547 61.5287 16.7885 59.477 17.7052 57.419C19.0576 54.3825 20.3931 51.3396 21.7453 48.303C22.3909 46.8529 23.5788 45.9991 25.3014 45.929C28.1445 45.8131 30.2687 48.261 29.1576 50.8921C27.7091 54.3216 26.1434 57.7101 24.6285 61.1165C24.3521 61.7382 24.0772 62.3605 23.7801 63.0314C24.2092 63.3795 24.5757 63.7598 25.0242 64.0258C26.6954 65.017 28.4817 65.7478 30.506 65.9144C34.0947 66.2098 37.2482 65.4815 39.5056 62.7212C41.5034 60.2783 41.8772 57.6559 40.4039 54.8734C39.5932 53.3425 38.4358 52.0404 37.1359 50.8381C34.6009 48.4935 31.6556 46.6094 28.7373 44.6965C25.5967 42.6377 22.4437 40.5943 19.316 38.5195C18.4452 37.9419 17.6447 37.2771 16.812 36.6522C16.2078 36.0812 15.5446 35.5518 15.0138 34.9293C14.2637 34.0495 13.6115 33.1009 12.9174 32.1817C12.9045 32.1137 12.9069 32.0394 12.8765 31.9786C11.8476 29.9147 11.4662 27.7315 11.4016 25.4855C11.3013 21.9913 12.0287 18.6743 14.0017 15.6403C16.244 12.1918 19.4768 9.77352 23.4851 8.1489C26.5217 6.91818 29.7003 6.28437 33.0012 6.13835C37.615 5.93425 42.0943 6.61149 46.4615 7.94561C47.4594 8.25054 48.4478 8.58132 49.4626 8.90727C50.3527 6.90236 51.28 4.95216 52.0829 2.9605C52.6895 1.45553 53.6803 0.399987 55.4247 0Z"
                  fill="#2458A7"
                />
                <path
                  class="logo-icon"
                  d="M61.4293 17.3108H66.2065C68.3308 17.3197 70.4552 17.3357 72.5795 17.3357C74.7416 17.3356 76.9038 17.3196 79.0659 17.3105C80.1093 17.3105 81.1528 17.3105 82.2397 17.3105C82.4814 16.6209 82.7241 15.9357 82.9617 15.2491C83.6356 13.3025 85.8093 12.2428 87.9217 12.8328C90.0475 13.4266 91.2142 15.4589 90.548 17.4087C89.7385 19.778 88.9285 22.1472 88.1187 24.5164C87.5134 26.2267 86.908 27.937 86.3031 29.6473C86.2169 29.8911 86.1333 30.1356 86.0485 30.3797C86.0028 30.4334 85.9527 30.4845 85.9121 30.5412C84.7456 32.1723 82.6243 32.7534 80.7813 31.9456C78.9182 31.1292 78.0841 29.2351 78.762 27.384C79.0378 26.6307 79.2925 25.8709 79.5526 25.1128C79.6461 24.8404 79.7269 24.5645 79.8383 24.2116H66.8508C65.473 27.6652 64.0931 31.1242 62.6844 34.6553C62.9697 34.6116 63.1348 34.5891 63.2987 34.5608C67.7437 33.7909 72.1577 33.8927 76.5196 35.0112C82.5933 36.5687 87.6532 39.4903 91.2743 44.269C92.121 45.3864 92.8161 46.5986 93.581 47.7673C95.0354 50.8007 96.0776 53.9422 95.9567 57.2763C95.6538 65.6337 90.9819 72.7107 83.0579 77.1835C82.8326 77.3107 83.2826 77.0557 83.0579 77.1835C83.0075 77.2147 83.1082 77.1523 83.0579 77.1835C83.0179 77.2229 83.0979 77.1443 83.0579 77.1835C82.7679 77.285 83.3335 77.0565 83.0579 77.1835C79.4099 78.8651 74.6148 80.1694 70.5251 80.0128C62.0075 79.6865 55.0993 76.5363 50.0989 70.157C50.0774 70.1297 50.0675 70.0948 50.0293 70.0163C51.519 68.5236 53.0239 67.0157 54.5414 65.4952C55.1319 65.5267 55.7173 65.5579 56.1404 65.5805C57.2816 66.7397 58.2374 67.901 59.3971 68.8584C69.2744 77.0136 83.4219 72.2073 87.0103 62.1986C90.2255 53.2312 83.6993 43.3419 73.3653 41.5232C69.705 40.879 66.0924 41.1678 62.5053 41.92C60.9152 42.2534 59.353 42.6967 57.7781 43.0898C57.5969 43.1351 57.4151 43.1785 57.2096 43.2286C55.6099 41.9478 54.0152 40.6708 52.4036 39.3805C53.1658 37.4712 53.9153 35.5908 54.667 33.7109C56.4433 29.2687 58.2394 24.833 59.9756 20.378"
                  fill="#DE8545"
                />
              </g>

              <!-- Gradient definition for shine effect -->
              <defs>
                <linearGradient
                  id="shineGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="0%"
                >
                  <stop
                    offset="0%"
                    style="stop-color: white; stop-opacity: 0"
                  />
                  <stop
                    offset="50%"
                    style="stop-color: white; stop-opacity: 0.2"
                  />
                  <stop
                    offset="100%"
                    style="stop-color: white; stop-opacity: 0"
                  />
                </linearGradient>
              </defs>

              <!-- Shine effect overlay (hidden initially) -->
              <rect
                id="logo-shine"
                x="-20"
                y="0"
                width="20"
                height="98"
                fill="url(#shineGradient)"
                opacity="0"
              />
            </svg>
            <span class="text-white font-bold text-lg">Serv5</span>
          </div>
          <button
            id="closeMobileMenu"
            class="text-white hover:text-primary-yellow transition-colors duration-300 p-2"
            aria-label="Close mobile menu"
          >
            <i class="fa-solid fa-times text-xl"></i>
          </button>
        </div>

        <!-- Mobile Menu Navigation -->
        <nav class="p-6">
          <ul class="space-y-4 mobile-nav-items">
            <!-- Home -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-house text-lg"></i>
                <span data-nav="home" class="font-medium">الرئيسية</span>
              </a>
            </li>
            <!-- About -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-5 text-lg"></i>
                <span data-nav="about" class="font-medium">نبذة عنا</span>
              </a>
            </li>
            <!-- Services -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-plus text-lg"></i>
                <span data-nav="services" class="font-medium">خدماتنا</span>
              </a>
            </li>
            <!-- Portfolio -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-briefcase text-lg"></i>
                <span data-nav="portfolio" class="font-medium">أعمالــنا</span>
              </a>
            </li>
            <!-- Blog -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-regular fa-rectangle-list text-lg"></i>
                <span data-nav="blog" class="font-medium">المدونــة</span>
              </a>
            </li>
            <!-- Brochure -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-file-pdf text-lg"></i>
                <span data-nav="brochure" class="font-medium"
                  >الكتيب التعريفي</span
                >
              </a>
            </li>
          </ul>

          <!-- Mobile Menu Actions -->
          <div class="mt-8 space-y-4 mobile-menu-actions">
            <button
              class="w-full rounded-md flex items-center justify-center gap-2 text-white border border-white font-bold bg-transparent p-4 hover:bg-white hover:text-primary-blue transition-all duration-300"
            >
              <i class="fa-solid fa-book"></i>
              <span data-nav="profile">بروفايل الشركة</span>
            </button>

            <!-- Mobile Vision Image -->
            <div class="flex justify-center mt-6">
              <img
                src="/public/shared/header/our-vision.webp"
                alt="Serv5 future vision for 2030"
                class="w-[100px] h-[67px]"
              />
            </div>
          </div>
        </nav>
      </div>
    </header>

    <!-- start of breadcrumbs -->
    <section
      class="breadcrumbs-section bottom-banner bg-light-blue lg:h-[251px] rounded-md container py-10 relative overflow-hidden"
    >
      <!-- flying icons start -->
      <div
        class="absolute flex top-1/2 -translate-y-1/2 left-1/2 lg:start-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="/public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="/public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-20 lg:end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-5 lg:bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>
        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute hidden lg:inline top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="/public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>
      <!-- flying icons end -->
      <div
        class="absolute top-1/2 -translate-y-1/2 hidden lg:flex end-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="/public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="/public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>

        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="/public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>

      <div class="flex flex-col">
        <!-- start of navigations -->
        <ul class="flex gap-2 text-white self-start text-sm items-center">
          <li>
            <a href="#" class="hover:text-primary-yellow cursor-pointer"
              >الرئيسية</a
            >
          </li>
          <li>
            <img
              src="/public/shared/cross-pages/serv5-small-logo.webp"
              alt="Serv5 logo"
              class="size-[12px]"
            />
          </li>
          <li>
            <a href="#" class="hover:text-primary-yellow cursor-pointer"
              >أعمالــنا</a
            >
          </li>
        </ul>
        <!-- end of navigations -->
        <p class="banner-text text-white font-bold mt-10 text-2xl lg:text-4xl">
          أعمالنا
        </p>
      </div>
    </section>
    <!-- end of breadcrumbs -->

    <!-- start of sub header -->
    <section
      class="flex flex-col lg:flex-row gap-5 items-center justify-between container px-8 py-6 rounded-md bg-secondary-blue text-white"
    >
      <p class="font-semibold">تصميم مواقع</p>

      <ul
        class="flex items-center flex-wrap justify-center gap-y-2 gap-x-4 xl:gap-[50px]"
      >
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#"> نظرة عامة </a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">المميزات</a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">أحدث أعمالنا</a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">حدد موقعك بنفسك</a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">أعمال متعلقة بالخدمة</a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">مقالات مشابهة</a>
        </li>
      </ul>

      <button
        class="bg-white rounded-md text-light-blue font-bold p-3 flex justify-between items-center gap-1 hover:text-white hover:bg-light-blue transition-all"
      >
        <i class="fa-solid fa-arrow-right"></i>
        طلب الخدمة
      </button>
    </section>
    <!-- end of sub header -->

    <!-- end of section headers -->
    <div class="shadow-xl">
      <section class="best-company-section container">
        <div class="text-center mb-16">
          <h1
            class="section-title text-primary-blue font-bold text-3xl md:text-5xl relative w-fit mx-auto"
          >
            <span
              class="absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
            ></span>
            استضافة مواقع قوية
          </h1>
          <p class="section-subtitle text-gray mt-5">
            قم بكتابة بياناتك و سيتم الرد عليك خلال 24 ساعة كحد أقصي
          </p>
        </div>
        <!-- main container -->
        <div class="flex flex-col lg:flex-row lg:gap-20">
          <!-- images -->
          <div class="services-grid w-full hidden xl:block flex-1">
            <!-- the gap between each item is 20px -->
            <div
              class="relative w-full mx-auto max-w-full md:max-w-[700px] h-[400px] sm:h-[500px] md:h-[600px] lg:h-[738px] overflow-hidden rounded-xl"
            >
              <!-- Div #1 - Top left organic shape -->
              <div
                class="grid-item absolute top-8 sm:top-16 md:top-24 lg:top-32 left-2 sm:left-4 md:left-6 lg:left-10 w-[40vw] max-w-[200px] h-[120px] sm:h-[140px] md:h-[170px] lg:h-[200px] rounded-md overflow-hidden shadow-lg"
                data-index="1"
              >
                <img
                  src="/public/pages/web-hosting/people-working-on-server.webp"
                  alt="Service Image"
                  class="w-full h-full object-cover transition-all duration-500"
                  style="object-position: 15% 15%"
                />
              </div>
              <!-- Div #2 - Top center organic shape -->
              <div
                class="grid-item absolute top-2 sm:top-6 md:top-8 lg:top-10 left-[35vw] sm:left-[180px] md:left-[220px] lg:left-[250px] w-[25vw] max-w-[155px] h-[120px] sm:h-[140px] md:h-[170px] lg:h-[200px] rounded-md overflow-hidden shadow-lg"
                data-index="2"
              >
                <img
                  src="/public/pages/web-hosting/people-working-on-server.webp"
                  alt="Service Image"
                  class="w-full h-full object-cover transition-all duration-500"
                  style="object-position: 50% 20%"
                />
              </div>

              <!-- Div #3 - center left organic shape -->
              <div
                class="grid-item absolute top-[50vw] sm:top-[250px] md:top-[300px] lg:top-[338px] left-4 sm:left-8 md:left-12 lg:left-20 w-[25vw] max-w-[159px] h-[100px] sm:h-[120px] md:h-[140px] lg:h-[159px] rounded-md overflow-hidden shadow-lg"
                data-index="3"
              >
                <img
                  src="/public/pages/web-hosting/people-working-on-server.webp"
                  alt="Service Image"
                  class="w-full h-full object-cover transition-all duration-500"
                  style="object-position: 20% 50%"
                />
              </div>

              <!-- Div #4 - center center organic shape -->
              <div
                class="grid-item absolute top-[35vw] sm:top-[180px] md:top-[220px] lg:top-[250px] left-[30vw] sm:left-[160px] md:left-[200px] lg:left-[250px] w-[30vw] max-w-[200px] h-[160px] sm:h-[190px] md:h-[230px] lg:h-[268px] rounded-md overflow-hidden shadow-lg"
                data-index="4"
              >
                <img
                  src="/public/pages/web-hosting/people-working-on-server.webp"
                  alt="Service Image"
                  class="w-full h-full object-cover transition-all duration-500"
                  style="object-position: 50% 50%"
                />
              </div>
              <!-- Div #5 - bottom center organic shape -->
              <div
                class="grid-item absolute top-[70vw] sm:top-[350px] md:top-[450px] lg:top-[528px] left-[20vw] sm:left-[110px] md:left-[140px] lg:left-[170px] w-[45vw] max-w-[281px] h-[100px] sm:h-[120px] md:h-[140px] lg:h-[159px] rounded-md overflow-hidden shadow-lg"
                data-index="5"
              >
                <img
                  src="/public/pages/web-hosting/people-working-on-server.webp"
                  alt="Service Image"
                  class="w-full h-full object-cover transition-all duration-500"
                  style="object-position: 50% 80%"
                />
              </div>
              <!-- Div #6 - center right organic shape -->
              <div
                class="grid-item absolute top-[30vw] sm:top-[160px] md:top-[190px] lg:top-[220px] left-[55vw] sm:left-[320px] md:left-[380px] lg:left-[460px] w-[45vw] max-w-[281px] h-[80px] sm:h-[95px] md:h-[110px] lg:h-[126px] rounded-md overflow-hidden shadow-lg"
                data-index="6"
              >
                <img
                  src="/public/pages/web-hosting/people-working-on-server.webp"
                  alt="Service Image"
                  class="w-full h-full object-cover transition-all duration-500"
                  style="object-position: 80% 45%"
                />
              </div>
              <!-- Div #7 - bottom right organic shape -->
              <div
                class="grid-item absolute top-[55vw] sm:top-[250px] md:top-[300px] lg:top-[356px] left-[55vw] sm:left-[320px] md:left-[380px] lg:left-[460px] w-[30vw] max-w-[200px] h-[160px] sm:h-[190px] md:h-[230px] lg:h-[267px] rounded-md overflow-hidden shadow-lg"
                data-index="7"
              >
                <img
                  src="/public/pages/web-hosting/people-working-on-server.webp"
                  alt="Service Image"
                  class="w-full h-full object-cover transition-all duration-500"
                  style="object-position: 75% 70%"
                />
              </div>
            </div>
          </div>
          <!-- content -->
          <div
            class="flex-1 flex flex-col gap-6 lg:gap-12 justify-center bg-section-gray p-4 lg:p-14"
          >
            <h2
              class="text-primary-blue font-bold text-xl sm:text-3xl md:text-5xl"
            >
              سيرفرات قوية آمنة
            </h2>

            <p class="text-gray leading-7">
              معنا سوف تضاعف نسبة أرباحك؛ كل ما عليك فعله هو الاتصال بنا وطلب
              خدمة تصميم متجر الكتروني.. ان فكرة امتلاك متجر الكتروني تعد من
              الأفكار السديدة التي من خلالها سوف تجني أضعاف الأرباح التى من
              الممكن الحصول عليها من خلال المتجر التقليدي؛ وذلك لما يتسم به
              المتجر الالكتروني من مرونة وشمولية الجانب الجغرافي فمن خصائصه
              التوسع والعرض على قاعدة جماهيرية لا تتقيد بالظروف المكانية او
              المناخية ولا بالحدود الدولية مما يتيح لك عدد عملاء أكبر وبالتالي
              تحقيق مبيعات أكبر؛ ومضاعفة الأرباح.
            </p>
            <!-- seprator -->
            <div class="w-full h-[1px] bg-gray"></div>

            <!-- features -->
            <div class="flex flex-wrap gap-x-10 gap-y-5">
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- start of our features section -->
    <section class="features-section container overflow-hidden">
      <div
        class="w-full bg-cover bg-center bg-no-repeat flex-col md:flex-row flex gap-4"
      >
        <div class="">
          <!-- section header -->
          <h2
            class="features-title text-primary-blue font-bold text-3xl md:text-5xl relative"
          >
            <span
              class="absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
            ></span>
            مميزات متعددة
          </h2>
          <div
            id="lottie-animation-server"
            class="w-full md:w-[300px] 2xl:w-[502px] mt-10 mx-auto my-auto"
          ></div>
        </div>

        <div class="space-y-4 flex-1 py-10">
          <ul class="grid md:grid-cols-2 gap-2">
            <!-- first feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">دعم فني متواصل</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- second feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="public/pages/home-page/serv5-features/key-settings.webp"
                  alt="Key Settings"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">إعدادات متقدمة</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- third feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="public/pages/home-page/serv5-features/search-web.webp"
                  alt="Search Web"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">بحث متقدم</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- fourth feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="public/pages/home-page/serv5-features/target.webp"
                  alt="Target"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">استهداف دقيق</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- fifth feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">تحليل البيانات</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- sixth feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">دعم فني متواصل</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <!-- end of our features section -->

    <!-- start of choose your hosting section -->
    <section class="hosting-plans-section container">
      <!-- start of section headers -->
      <div class="text-center mb-16">
        <h2
          class="hosting-title section-title text-primary-blue font-bold text-3xl md:text-5xl relative w-fit mx-auto"
        >
          <span
            class="hosting-underline absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
          ></span>
          إختر خطة الإستضافة
        </h2>
        <p class="hosting-subtitle section-subtitle text-gray mt-5">
          قم بكتابة بياناتك و سيتم الرد عليك خلال 24 ساعة كحد أقصي
        </p>
      </div>
      <!-- end of section headers -->

      <!-- tabs -->
      <div
        class="hosting-tabs flex flex-wrap items-center justify-center gap-6 mb-12"
      >
        <button
          class="hosting-tab work-tab active hover:text-white cursor-pointer roumded-md hover:bg-secondary-blue bg-secondary-blue text-white outline outline-secondary-blue transition-all py-2 px-4 sm:py-4 sm:px-7 rounded-md font-semibold text-sm"
          data-category="all"
          role="tab"
          aria-selected="true"
          tabindex="0"
        >
          الكل
        </button>
        <button
          class="hosting-tab work-tab hover:text-white cursor-pointer roumded-md hover:bg-secondary-blue bg-white text-secondary-blue outline outline-secondary-blue transition-all py-2 px-4 sm:py-4 sm:px-7 rounded-md font-semibold text-sm"
          data-category="news"
          role="tab"
          aria-selected="false"
          tabindex="-1"
        >
          موقع اخباري
        </button>
        <button
          class="hosting-tab work-tab hover:text-white cursor-pointer roumded-md hover:bg-secondary-blue bg-white text-secondary-blue outline outline-secondary-blue transition-all py-2 px-4 sm:py-4 sm:px-7 rounded-md font-semibold text-sm"
          data-category="featured"
        >
          أعمال مميزة
        </button>
        <button
          class="hosting-tab work-tab hover:text-white cursor-pointer roumded-md hover:bg-secondary-blue bg-white text-secondary-blue outline outline-secondary-blue transition-all py-2 px-4 sm:py-4 sm:px-7 rounded-md font-semibold text-sm"
          data-category="motion"
        >
          موشن جرافيك
        </button>
      </div>

      <!-- start of services card part -->
      <div
        class="hosting-cards grid sm:grid-cols-2 2xl:grid-cols-4 gap-5 justify-items-center"
      >
        <!-- first card -->
        <div class="hosting-card relative">
          <!-- gray circle at the top start-->
          <img
            src="/public/pages/web-hosting/gray-circle.webp"
            alt="Gray circle"
            class="absolute top-0 start-0"
          />
          <!-- gray circle at the bottom end -->
          <img
            src="/public/pages/web-hosting/big-gray-elipse.webp"
            alt="Gray circle"
            class="absolute bottom-0 end-0"
          />
          <svg
            width="409"
            height="768"
            viewBox="0 0 409 768"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <defs>
              <linearGradient
                id="paint0_linear_528_7052"
                x1="0"
                y1="384"
                x2="409"
                y2="384"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#2C5BBA" />
                <stop offset="1" stop-color="#15205C" />
              </linearGradient>
            </defs>
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M409 10C409 4.47715 404.523 0 399 0H10C4.47715 0 0 4.47714 0 9.99998V758C0 763.523 4.47714 768 9.99999 768H183.241C186.877 768 190.227 766.026 191.989 762.844L224.653 703.858C228.705 696.541 236.41 692 244.774 692H399C404.523 692 409 687.523 409 682V10Z"
              fill="url(#paint0_linear_528_7052)"
            />

            <foreignObject class="w-full h-full">
              <div xmlns="http://www.w3.org/1999/xhtml">
                <!-- banner -->
                <svg
                  width="374"
                  height="107"
                  viewBox="0 0 374 107"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="ms-auto mt-[18px]"
                >
                  <path
                    d="M0 0H342.222L374 52.4906L342.222 107H0V0Z"
                    fill="white"
                  />

                  <foreignObject
                    class="w-full h-full flex items-center justify-center text-center"
                  >
                    <div xmlns="http://www.w3.org/1999/xhtml">
                      <p class="font-bold text-3xl text-[#2A56B2] mt-8">
                        سيرف المميز
                      </p>
                    </div>
                  </foreignObject>
                </svg>
                <!-- the list -->
                <ul class="flex gap-2 flex-col text-white mt-8 ps-8">
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                </ul>

                <!-- the price -->
                <div class="mt-auto absolute bottom-10 end-5 text-center">
                  <p>
                    <span class="font-bold text text-white text-4xl">
                      180
                    </span>
                    <span class="font-semibold text text-white text-3xl">
                      دولار
                    </span>
                    <br />
                    <span class="text-white">/ سنويا</span>
                  </p>
                </div>
              </div>
            </foreignObject>
          </svg>
          <!-- down button for booking now  -->

          <button
            class="hosting-btn btn-purchase template-actions hover:text-white text-primary-blue py-3 px-7 rounded-lg border-primary-blue border text-sm font-medium absolute bottom-14 start-5"
            style="width: auto !important; max-width: fit-content !important"
          >
            <i class="fa-solid fa-arrow-right"></i>
            اشترك الآن
          </button>
        </div>
        <!-- second card -->
        <div class="hosting-card relative">
          <!-- gray circle at the top start-->
          <img
            src="/public/pages/web-hosting/gray-circle.webp"
            alt="Gray circle"
            class="absolute top-0 start-0"
          />
          <!-- gray circle at the bottom end -->
          <img
            src="/public/pages/web-hosting/big-gray-elipse.webp"
            alt="Gray circle"
            class="absolute bottom-0 end-0"
          />
          <svg
            width="409"
            height="768"
            viewBox="0 0 409 768"
            class="shadow-xl"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M409 10C409 4.47715 404.523 0 399 0H10C4.47715 0 0 4.47714 0 9.99998V758C0 763.523 4.47714 768 9.99999 768H183.241C186.877 768 190.227 766.026 191.989 762.844L224.653 703.858C228.705 696.541 236.41 692 244.774 692H399C404.523 692 409 687.523 409 682V10Z"
            />

            <foreignObject class="w-full h-full">
              <div xmlns="http://www.w3.org/1999/xhtml">
                <!-- banner -->
                <svg
                  width="382"
                  class="ms-auto mt-[18px]"
                  height="107"
                  viewBox="0 0 382 107"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 0H349.542L382 52.4906L349.542 107H0V0Z"
                    fill="url(#paint0_linear_525_8227)"
                  />
                  <foreignObject
                    class="w-full h-full flex items-center justify-center text-center"
                  >
                    <div xmlns="http://www.w3.org/1999/xhtml">
                      <p class="font-bold text-3xl text-white mt-8">
                        سيرف الفضي
                      </p>
                    </div>
                  </foreignObject>
                  <defs>
                    <linearGradient
                      id="paint0_linear_525_8227"
                      x1="0"
                      y1="53.5"
                      x2="382"
                      y2="53.5"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stop-color="#2C5BBA" />
                      <stop offset="1" stop-color="#15205C" />
                    </linearGradient>
                  </defs>
                </svg>

                <!-- the list -->
                <ul class="flex gap-2 flex-col mt-8 ps-8">
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184] items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                </ul>

                <!-- the price -->
                <div class="mt-auto absolute bottom-10 end-5 text-center">
                  <p>
                    <span class="font-bold text text-secondary-blue text-4xl">
                      180
                    </span>
                    <span
                      class="font-semibold text text-secondary-blue text-3xl"
                    >
                      دولار
                    </span>
                    <br />
                    <span class=" ">/ سنويا</span>
                  </p>
                </div>
              </div>
            </foreignObject>
          </svg>
          <!-- down button for booking now  -->

          <button
            class="hosting-btn btn-purchase template-actions hover:text-white text-primary-blue py-3 px-7 rounded-lg border-primary-blue border text-sm font-medium absolute bottom-14 start-5"
            style="width: auto !important; max-width: fit-content !important"
          >
            <i class="fa-solid fa-arrow-right"></i>
            اشترك الآن
          </button>
        </div>
        <!-- third card -->
        <div class="hosting-card relative">
          <!-- gray circle at the top start-->
          <img
            src="/public/pages/web-hosting/gray-circle.webp"
            alt="Gray circle"
            class="absolute top-0 start-0"
          />
          <!-- gray circle at the bottom end -->
          <img
            src="/public/pages/web-hosting/big-gray-elipse.webp"
            alt="Gray circle"
            class="absolute bottom-0 end-0"
          />

          <svg
            width="409"
            height="768"
            viewBox="0 0 409 768"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M409 8C409 3.58172 405.418 0 401 0H8C3.58172 0 0 3.58174 0 8.00002V760C0 764.418 3.58173 768 8 768H182.204C185.114 768 187.794 766.421 189.203 763.875L224.653 699.858C228.705 692.541 236.41 688 244.774 688H401C405.418 688 409 684.418 409 680V8Z"
              fill="url(#paint0_linear_528_7100)"
            />

            <foreignObject class="w-full h-full">
              <div xmlns="http://www.w3.org/1999/xhtml">
                <!-- banner -->
                <svg
                  width="374"
                  height="107"
                  viewBox="0 0 374 107"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="ms-auto mt-[18px]"
                >
                  <path
                    d="M0 0H342.222L374 52.4906L342.222 107H0V0Z"
                    fill="white"
                  />

                  <foreignObject
                    class="w-full h-full flex items-center justify-center text-center"
                  >
                    <div xmlns="http://www.w3.org/1999/xhtml">
                      <p class="font-bold text-3xl text-[#2A56B2] mt-8">
                        سيرف برونزي
                      </p>
                    </div>
                  </foreignObject>
                </svg>

                <!-- the list -->
                <ul class="flex gap-2 flex-col mt-8 ps-8">
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#83A1DE] text-white items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#83A1DE] text-white items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#83A1DE] text-white items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#83A1DE] text-white items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#83A1DE] text-white items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#83A1DE] text-white items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#83A1DE] text-white items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                </ul>

                <!-- the price -->
                <div class="mt-auto absolute bottom-10 end-5 text-center">
                  <p>
                    <span class="font-bold text text-secondary-blue text-4xl">
                      180
                    </span>
                    <span
                      class="font-semibold text text-secondary-blue text-3xl"
                    >
                      دولار
                    </span>
                    <br />
                    <span class="text-light-blue">/ سنويا</span>
                  </p>
                </div>
              </div>
            </foreignObject>

            <defs>
              <linearGradient
                id="paint0_linear_528_7100"
                x1="0"
                y1="384"
                x2="409"
                y2="384"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#DBE7FF" />
                <stop offset="1" stop-color="#EDF0FF" />
              </linearGradient>
            </defs>
          </svg>

          <button
            class="hosting-btn btn-purchase template-actions hover:text-white text-primary-blue py-3 px-7 rounded-lg border-primary-blue border text-sm font-medium absolute bottom-14 start-5"
            style="width: auto !important; max-width: fit-content !important"
          >
            <i class="fa-solid fa-arrow-right"></i>
            اشترك الآن
          </button>
        </div>
        <!-- fourth card -->
        <div class="hosting-card relative">
          <!-- gray circle at the top start-->
          <img
            src="/public/pages/web-hosting/gray-circle.webp"
            alt="Gray circle"
            class="absolute top-0 start-0"
          />
          <!-- gray circle at the bottom end -->
          <img
            src="/public/pages/web-hosting/big-gray-elipse.webp"
            alt="Gray circle"
            class="absolute bottom-0 end-0"
          />

          <svg
            width="409"
            height="768"
            viewBox="0 0 409 768"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M409 10C409 4.47715 404.523 0 399 0H10C4.47715 0 0 4.47714 0 9.99998V758C0 763.523 4.47714 768 9.99999 768H183.241C186.877 768 190.227 766.026 191.989 762.844L224.653 703.858C228.705 696.541 236.41 692 244.774 692H399C404.523 692 409 687.523 409 682V10Z"
              fill="#FF9B53"
            />

            <foreignObject class="w-full h-full">
              <div xmlns="http://www.w3.org/1999/xhtml">
                <!-- banner -->
                <svg
                  width="374"
                  height="107"
                  viewBox="0 0 374 107"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  class="ms-auto mt-[18px]"
                >
                  <path
                    d="M0 0H342.222L374 52.4906L342.222 107H0V0Z"
                    fill="white"
                  />

                  <foreignObject
                    class="w-full h-full flex items-center justify-center text-center"
                  >
                    <div xmlns="http://www.w3.org/1999/xhtml">
                      <p class="font-bold text-3xl text-primary-yellow mt-8">
                        سيرف الذهبي
                      </p>
                    </div>
                  </foreignObject>
                </svg>

                <!-- the list -->
                <ul class="flex gap-2 flex-col mt-8 ps-8">
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184]/10 items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184]/10 items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184]/10 items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184]/10 items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184]/10 items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184]/10 items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                  <li class="flex items-center gap-3">
                    <span
                      class="size-[24px] bg-[#465184]/10 items-center flex justify-center rounded-full"
                      ><i class="fa-solid fa-check text-white"></i
                    ></span>
                    <span>المساحة : 5 جيجا</span>
                  </li>
                </ul>

                <!-- the price -->
                <div
                  class="mt-auto absolute bottom-10 end-5 text-center text-white"
                >
                  <p>
                    <span class="font-bold text text-4xl"> 180 </span>
                    <span class="font-semibold text text-3xl"> دولار </span>
                    <br />
                    <span class="">/ سنويا</span>
                  </p>
                </div>
              </div>
            </foreignObject>
          </svg>

          <button
            class="hosting-btn btn-purchase template-actions hover:text-white text-primary-blue py-3 px-7 rounded-lg border-primary-blue border text-sm font-medium absolute bottom-14 start-5"
            style="width: auto !important; max-width: fit-content !important"
          >
            <i class="fa-solid fa-arrow-right"></i>
            اشترك الآن
          </button>
        </div>
      </div>
      <!-- end of services card part -->
    </section>
    <!-- end of choose your hosting section -->

    <!-- start of section choose your hosting service -->
    <section
      class="container mt-10 lg:mt-20 py-10 lg:py-20 flex flex-col items-center justify-center"
    >
      <!-- header section -->
      <div class="text-center mb-16">
        <h2
          class="section-title text-primary-blue font-bold text-3xl md:text-5xl relative w-fit mx-auto"
        >
          <span
            class="absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
          ></span>
          إختر خصائص الإستضافة بنفسك
        </h2>
        <p class="section-subtitle text-gray mt-5">
          نمكنك الآن من تخصيص خدمتك بشكل مناسب لك ولمنتجك
        </p>
      </div>
      <!-- content -->
      <div class="relative w-[1031px] h-[434px]">
        <!-- first feature hosting -->
        <div
          class="bg-white text-light-blue text-xl flex font-bold absolute top-0 start-0"
        >
          <p>ecommerce hosting</p>
          <svg
            width="191"
            height="191"
            viewBox="0 0 191 191"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M23.9789 90.5853C24.2088 90.3555 24.4906 90.1694 24.8068 90.0561L139.041 48.9321C140.432 48.4321 141.804 48.6647 142.713 49.5568C143.62 50.4478 143.877 51.8115 143.399 53.2045L126.359 102.813C125.758 104.563 126.088 106.353 127.218 107.482L170.743 115.252C171.374 115.883 171.268 117.007 170.507 117.769C169.746 118.53 168.621 118.636 167.991 118.005L124.465 110.235C122.533 108.303 121.971 105.244 122.996 102.25L140.036 52.6417C140.08 52.5153 140.06 52.4099 139.977 52.3292C139.895 52.2497 139.788 52.2311 139.662 52.2754L25.4275 93.3993C24.4568 93.75 23.5292 93.2849 23.3579 92.362C23.2421 91.737 23.5013 91.0629 23.9789 90.5853Z"
              fill="#4B88C5"
            />
            <path
              d="M44.1125 81.1655L46.8418 91.2354L68.5759 85.3395L65.8466 75.2696L44.1125 81.1655Z"
              fill="#5793CE"
            />
            <path
              d="M68.3296 76.1841C69.9974 77.8436 70.8612 81.0309 70.2595 83.3059C69.6564 85.5795 67.8164 86.0793 66.1472 84.4212C64.4794 82.7616 63.6156 79.5744 64.2173 77.2994C64.8217 75.0244 66.6618 74.5246 68.3296 76.1841Z"
              fill="#5793CE"
            />
            <path
              d="M11.5166 86.0303L11.5166 104.53L51.4772 93.3264L47.054 77.0072L11.5166 86.0303Z"
              fill="#5793CE"
            />
            <path
              d="M50.4445 90.2607L57.8008 88.2656C58.3503 84.9447 57.2223 80.7831 55.0716 78.1947L47.7154 80.1898C47.1658 83.5107 48.2938 87.6723 50.4445 90.2607Z"
              fill="#396CAA"
            />
            <path
              d="M51.0698 78.4917C53.7738 81.1792 55.1741 86.3454 54.1965 90.0311C53.2189 93.7168 50.2366 94.525 47.534 91.8389C44.83 89.1515 43.4296 83.9852 44.4072 80.2996C45.3834 76.6125 48.3659 75.8043 51.0698 78.4917Z"
              fill="#5793CE"
            />
            <path
              d="M65.6861 82.2479C66.7514 81.9588 67.3857 82.5904 67.1035 83.657C66.8212 84.7236 65.727 85.8234 64.6617 86.1125C63.5965 86.4017 62.9621 85.77 63.2444 84.7034C63.5267 83.6368 64.6195 82.5357 65.6861 82.2479Z"
              fill="#D4ECFF"
            />
          </svg>
        </div>
        <!--  second feature vps hosting   -->
        <div
          class="bg-white text-light-blue text-xl flex font-bold absolute bottom-0 start-0"
        >
          <p>ecommerce hosting</p>
          <svg
            width="165"
            height="167"
            viewBox="0 0 165 167"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M149.238 59.2869C149.1 59.5814 148.899 59.8525 148.64 60.0669L55.3033 137.712C54.1662 138.657 52.7971 138.907 51.6385 138.379C50.4813 137.851 49.7742 136.657 49.7489 135.184L48.8324 82.7383C48.8 80.8884 47.8786 79.3192 46.4314 78.6431L23.4357 67.8994C22.6282 67.5221 22.3437 66.4288 22.7994 65.4535C23.255 64.4783 24.2761 63.9949 25.0836 64.3722L48.0793 75.116C50.5545 76.2724 52.1277 78.9561 52.185 82.1197L53.1016 134.565C53.103 134.699 53.1578 134.791 53.2638 134.839C53.3683 134.886 53.475 134.867 53.5786 134.782L146.916 57.1363C147.708 56.4754 148.739 56.596 149.215 57.4049C149.537 57.9529 149.524 58.675 149.238 59.2869Z"
              fill="#4B88C5"
            />
            <path
              d="M133.528 75.013L127.525 66.4795L109.109 79.4397L115.111 87.9732L133.528 75.013Z"
              fill="#5793CE"
            />
            <path
              d="M112.465 87.9605C110.331 86.9699 108.431 84.2689 108.22 81.9251C108.011 79.5822 109.57 78.4844 111.705 79.4732C113.839 80.4638 115.739 83.1648 115.95 85.5085C116.158 87.8532 114.599 88.9511 112.465 87.9605Z"
              fill="#5793CE"
            />
            <path
              d="M161.069 59.598L151.342 45.7686L122.458 66.0949L132.186 79.9244L161.069 59.598Z"
              fill="#5793CE"
            />
            <path
              d="M124.472 68.6243L118.238 73.0104C118.855 76.3195 121.336 79.8462 124.241 81.5451L130.475 77.1589C129.858 73.8498 127.377 70.3231 124.472 68.6243Z"
              fill="#396CAA"
            />
            <path
              d="M127.902 79.8995C124.443 78.2963 121.363 73.9184 121.024 70.1204C120.685 66.3223 123.212 64.5447 126.669 66.147C130.128 67.7501 133.208 72.1281 133.547 75.9261C133.888 79.7249 131.361 81.5026 127.902 79.8995Z"
              fill="#5793CE"
            />
            <path
              d="M112.88 81.3584C111.978 81.9938 111.166 81.6166 111.067 80.5177C110.968 79.4188 111.622 78.0116 112.524 77.3763C113.427 76.7409 114.239 77.118 114.337 78.217C114.436 79.3159 113.785 80.7239 112.88 81.3584Z"
              fill="#D4ECFF"
            />
          </svg>
        </div>
        <!-- third feature cloud hosting -->
        <div
          class="bg-white text-light-blue text-xl flex font-bold absolute top-0 start-0"
        >
          <p>ecommerce hosting</p>
          <svg
            width="180"
            height="181"
            viewBox="0 0 180 181"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M23.4334 90.3508C23.6633 90.5807 23.9451 90.7668 24.2613 90.88L138.495 132.004C139.887 132.504 141.259 132.271 142.167 131.379C143.075 130.488 143.332 129.125 142.853 127.732L125.813 78.1227C125.212 76.3729 125.543 74.5834 126.672 73.4539L159.262 76.2076C160.51 74.499 159.412 74.0779 159.025 73.6915C158.789 73.4548 156.509 73.5011 156.509 73.4548L123.92 70.7011C121.988 72.633 121.425 75.6924 122.451 78.6856L139.491 128.294C139.535 128.421 139.515 128.526 139.431 128.607C139.349 128.686 139.242 128.705 139.116 128.661L24.882 87.5368C23.9113 87.1861 22.9837 87.6513 22.8124 88.5741C22.6966 89.1991 22.9558 89.8732 23.4334 90.3508Z"
              fill="#4B88C5"
            />
            <path
              d="M43.5676 99.771L46.2969 89.7012L68.031 95.597L65.3016 105.667L43.5676 99.771Z"
              fill="#5793CE"
            />
            <path
              d="M67.7847 104.752C69.4525 103.093 70.3163 99.9056 69.7146 97.6306C69.1115 95.357 67.2714 94.8572 65.6023 96.5154C63.9345 98.1749 63.0706 101.362 63.6723 103.637C64.2768 105.912 66.1169 106.412 67.7847 104.752Z"
              fill="#5793CE"
            />
            <path
              d="M10.9717 94.9062L10.9717 76.4062L50.9322 87.6102L46.5091 103.929L10.9717 94.9062Z"
              fill="#5793CE"
            />
            <path
              d="M49.8996 90.6758L57.2558 92.6709C57.8054 95.9918 56.6774 100.153 54.5267 102.742L47.1705 100.747C46.6209 97.4258 47.7489 93.2642 49.8996 90.6758Z"
              fill="#396CAA"
            />
            <path
              d="M50.5249 102.445C53.2289 99.7573 54.6292 94.5911 53.6516 90.9054C52.674 87.2198 49.6916 86.4115 46.989 89.0976C44.2851 91.7851 42.8847 96.9513 43.8623 100.637C44.8385 104.324 47.8209 105.132 50.5249 102.445Z"
              fill="#5793CE"
            />
            <path
              d="M65.1412 98.6886C66.2065 98.9777 66.8408 98.3461 66.5585 97.2795C66.2763 96.2129 65.182 95.1132 64.1168 94.824C63.0516 94.5349 62.4172 95.1665 62.6995 96.2331C62.9818 97.2997 64.0746 98.4008 65.1412 98.6886Z"
              fill="#D4ECFF"
            />
          </svg>
        </div>
        <!-- fourth feature vps hosting hosting -->
        <div
          class="bg-white text-light-blue text-xl flex font-bold absolute bottom-0 end-0"
        >
          <p>ecommerce hosting</p>
          <svg
            width="165"
            height="167"
            viewBox="0 0 165 167"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M149.238 59.5164C149.1 59.8109 148.899 60.082 148.64 60.2964L55.3033 137.942C54.1662 138.887 52.7971 139.136 51.6385 138.608C50.4813 138.08 49.7742 136.886 49.7489 135.414L48.8324 82.9678C48.8 81.1179 47.8786 79.5487 46.4314 78.8726L23.4357 68.1288C22.6282 67.7515 22.3437 66.6583 22.7994 65.683C23.255 64.7078 24.2761 64.2244 25.0836 64.6017L48.0793 75.3454C50.5545 76.5019 52.1277 79.1856 52.185 82.3492L53.1016 134.795C53.103 134.929 53.1578 135.021 53.2638 135.068C53.3683 135.115 53.475 135.096 53.5786 135.011L146.916 57.3658C147.708 56.7049 148.739 56.8255 149.215 57.6344C149.537 58.1824 149.524 58.9045 149.238 59.5164Z"
              fill="#4B88C5"
            />
            <path
              d="M133.528 75.2454L127.525 66.7119L109.109 79.6721L115.111 88.2056L133.528 75.2454Z"
              fill="#5793CE"
            />
            <path
              d="M112.465 88.1929C110.331 87.2023 108.431 84.5013 108.22 82.1576C108.011 79.8146 109.57 78.7168 111.705 79.7056C113.839 80.6962 115.739 83.3972 115.95 85.7409C116.158 88.0856 114.599 89.1835 112.465 88.1929Z"
              fill="#5793CE"
            />
            <path
              d="M161.069 59.8304L151.342 46.001L122.458 66.3273L132.186 80.1568L161.069 59.8304Z"
              fill="#5793CE"
            />
            <path
              d="M124.472 68.8567L118.238 73.2428C118.855 76.5519 121.336 80.0786 124.241 81.7775L130.475 77.3913C129.858 74.0823 127.377 70.5556 124.472 68.8567Z"
              fill="#396CAA"
            />
            <path
              d="M127.902 80.1319C124.443 78.5288 121.363 74.1508 121.024 70.3528C120.685 66.5548 123.212 64.7771 126.669 66.3794C130.128 67.9825 133.208 72.3605 133.547 76.1585C133.888 79.9573 131.361 81.735 127.902 80.1319Z"
              fill="#5793CE"
            />
            <path
              d="M112.88 81.5879C111.978 82.2233 111.166 81.8461 111.067 80.7472C110.968 79.6483 111.622 78.2411 112.524 77.6058C113.427 76.9704 114.239 77.3475 114.337 78.4464C114.436 79.5453 113.785 80.9534 112.88 81.5879Z"
              fill="#D4ECFF"
            />
          </svg>
        </div>
        <div
          class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
        >
          <svg
            width="242"
            height="434"
            viewBox="0 0 242 434"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M241.099 379.541V84.439L123.752 53.7332L102.136 41.2572C97.1755 38.3946 88.5117 38.7524 82.7827 42.0587L62.6156 53.6998L0.0263672 53.664L18.559 338.545C18.4183 340.482 19.4991 342.352 21.8828 343.728L138.987 432.067C143.947 434.93 152.611 434.572 158.34 431.266L236.384 386.218C239.689 384.312 241.283 381.841 241.099 379.541Z"
              fill="#396CAA"
            />
            <path
              d="M93.1588 39.3408C89.5295 39.4291 85.7619 40.3427 82.7841 42.0603L62.617 53.7014L0.027777 53.6656V349.562C-0.113002 351.499 0.967832 353.37 3.35153 354.746L138.991 432.069C141.374 433.445 144.615 434.068 147.972 433.987L149.37 136.241L93.1588 39.3408Z"
              fill="#D4ECFF"
            />
            <path
              d="M140.234 432.677C144.513 434.509 150.702 434.406 155.703 432.5V132.72H140.234V432.677Z"
              fill="#5793CE"
            />
            <path
              d="M138.988 136.276L3.34881 58.953C-1.61185 56.0904 -0.98908 51.0881 4.73751 47.7818L82.7814 2.73448C88.5104 -0.571793 97.1742 -0.92962 102.135 1.93295L237.774 79.2558C242.735 82.1183 242.112 87.1207 236.385 90.4269L158.341 135.474C152.612 138.778 143.949 139.138 138.988 136.276Z"
              fill="#D4ECFF"
            />
            <path
              d="M236.385 84.9391L158.342 129.986C152.613 133.293 143.949 133.65 138.988 130.788L3.349 53.4651C2.24186 52.8258 1.43062 52.0792 0.874663 51.2705C-0.828998 54.0114 -0.0559384 56.9861 3.349 58.9517L138.988 136.275C143.949 139.137 152.613 138.779 158.342 135.473L236.385 90.4257C240.835 87.8565 242.188 84.2688 240.251 81.4491C239.471 82.7015 238.18 83.9038 236.385 84.9391Z"
              fill="#FAFDFF"
            />
            <path
              d="M142.822 104.943L52.5158 52.6105C50.1941 51.2699 50.4851 48.9297 53.1647 47.384L89.686 26.3035C92.3656 24.7553 96.422 24.5883 98.7412 25.929L189.047 78.2616C191.369 79.6022 191.078 81.9423 188.398 83.4881L151.877 104.569C149.197 106.114 145.143 106.284 142.822 104.943Z"
              fill="#173D7A"
            />
            <path
              d="M94.0682 54.9592L88.8355 51.9392C88.5134 51.7532 88.554 51.4311 88.9262 51.2164L93.9799 48.299C94.3498 48.0843 94.9129 48.0628 95.2326 48.2465L100.465 51.2665C100.787 51.4526 100.747 51.7746 100.375 51.9893L95.3209 54.9068C94.951 55.1214 94.3903 55.1429 94.0682 54.9592Z"
              fill="#D4ECFF"
            />
            <path
              d="M111.552 65.0081L106.319 61.988C105.997 61.802 106.037 61.4799 106.41 61.2652L111.463 58.3478C111.833 58.1331 112.396 58.1093 112.716 58.2953L117.949 61.3153C118.271 61.5014 118.23 61.8234 117.858 62.0381L112.804 64.9556C112.432 65.1703 111.871 65.1917 111.552 65.0081Z"
              fill="#94CFFE"
            />
            <path
              d="M129.031 75.0608L123.798 72.0408C123.476 71.8547 123.517 71.5327 123.889 71.318L128.943 68.4005C129.313 68.1859 129.876 68.1644 130.195 68.3481L135.428 71.3681C135.75 71.5541 135.71 71.8762 135.337 72.0909L130.284 75.0083C129.914 75.2206 129.353 75.2445 129.031 75.0608Z"
              fill="#89C7F7"
            />
            <path
              d="M146.517 85.1077L141.285 82.0877C140.963 81.9016 141.003 81.5795 141.375 81.3648L146.429 78.4474C146.799 78.2327 147.362 78.2089 147.682 78.3949L152.914 81.415C153.237 81.601 153.196 81.9231 152.824 82.1378L147.77 85.0552C147.4 85.2699 146.84 85.2937 146.517 85.1077Z"
              fill="#48AAF8"
            />
            <path
              d="M52.5179 52.6078L52.6253 52.6698C52.8019 52.5434 52.9641 52.4145 53.1693 52.2953L89.6907 31.2148C92.3702 29.6667 96.4265 29.4997 98.7482 30.8403L188.947 83.1132C191.087 81.5817 191.199 79.4968 189.054 78.2588L98.7435 25.9262C96.4218 24.5856 92.3678 24.755 89.6859 26.3007L53.1645 47.3812C50.4874 48.9294 50.1963 51.2695 52.5179 52.6078Z"
              fill="#5793CE"
            />
            <path
              d="M173.578 194.743L224.922 165.354C227.812 163.687 230.155 159.212 230.155 155.361V137.005C230.155 133.155 227.812 131.383 224.922 133.05L173.578 162.439C170.689 164.107 168.346 168.582 168.346 172.432V190.788C168.348 194.641 170.689 196.411 173.578 194.743Z"
              fill="#173D7A"
            />
            <path
              d="M227.464 132.499C228.271 133.26 228.777 134.558 228.777 136.318V154.674C228.777 158.525 226.433 163 223.544 164.667L172.2 194.056C171.272 194.591 170.413 194.738 169.659 194.61C170.618 195.514 172.009 195.65 173.577 194.746L224.921 165.357C227.81 163.689 230.153 159.214 230.153 155.364V137.008C230.153 134.391 229.061 132.771 227.464 132.499Z"
              fill="white"
            />
            <path
              d="M189.02 174.802L193.731 172.082C194.36 171.72 194.871 170.744 194.871 169.904V164.876C194.871 164.036 194.36 163.65 193.731 164.015L189.02 166.734C188.39 167.097 187.88 168.072 187.88 168.912V173.94C187.88 174.78 188.39 175.164 189.02 174.802Z"
              fill="white"
            />
            <path
              d="M204.769 165.711L209.48 162.991C210.109 162.629 210.62 161.653 210.62 160.813V155.785C210.62 154.945 210.109 154.559 209.48 154.924L204.769 157.643C204.14 158.006 203.629 158.981 203.629 159.821V164.85C203.629 165.689 204.14 166.076 204.769 165.711Z"
              fill="#A1BFFF"
            />
            <path
              d="M173.578 327.393L224.922 298.004C227.812 296.336 230.155 291.861 230.155 288.011V269.655C230.155 265.802 227.812 264.032 224.922 265.7L173.578 295.089C170.689 296.756 168.346 301.231 168.346 305.082V323.438C168.348 327.29 170.689 329.06 173.578 327.393Z"
              fill="#173D7A"
            />
            <path
              d="M227.464 265.148C228.271 265.909 228.777 267.207 228.777 268.968V287.324C228.777 291.174 226.433 295.649 223.544 297.317L172.2 326.706C171.272 327.242 170.413 327.388 169.659 327.259C170.618 328.163 172.009 328.299 173.577 327.395L224.921 298.006C227.81 296.339 230.153 291.863 230.153 288.013V269.657C230.153 267.04 229.061 265.418 227.464 265.148Z"
              fill="white"
            />
            <path
              d="M189.02 307.454L193.731 304.735C194.36 304.372 194.871 303.396 194.871 302.557V297.528C194.871 296.688 194.36 296.302 193.731 296.667L189.02 299.386C188.39 299.749 187.88 300.725 187.88 301.564V306.595C187.88 307.43 188.39 307.817 189.02 307.454Z"
              fill="white"
            />
            <path
              d="M204.769 298.363L209.48 295.644C210.109 295.281 210.62 294.305 210.62 293.466V288.437C210.62 287.598 210.109 287.211 209.48 287.576L204.769 290.295C204.14 290.658 203.629 291.634 203.629 292.473V297.504C203.629 298.342 204.14 298.726 204.769 298.363Z"
              fill="#A1BFFF"
            />
            <path
              d="M173.578 393.711L224.922 364.322C227.812 362.655 230.155 358.18 230.155 354.329V335.973C230.155 332.121 227.812 330.351 224.922 332.018L173.578 361.407C170.689 363.075 168.346 367.55 168.346 371.4V389.756C168.348 393.609 170.689 395.381 173.578 393.711Z"
              fill="#173D7A"
            />
            <path
              d="M227.464 331.467C228.271 332.228 228.777 333.525 228.777 335.286V353.642C228.777 357.495 226.434 361.967 223.544 363.635L172.2 393.024C171.272 393.558 170.413 393.706 169.659 393.577C170.618 394.482 172.009 394.617 173.577 393.713L224.921 364.324C227.81 362.657 230.153 358.182 230.153 354.332V335.975C230.153 333.358 229.061 331.739 227.464 331.467Z"
              fill="white"
            />
            <path
              d="M189.02 373.772L193.731 371.053C194.36 370.69 194.871 369.715 194.871 368.875V363.846C194.871 363.007 194.36 362.62 193.731 362.985L189.02 365.705C188.39 366.067 187.88 367.043 187.88 367.883V372.914C187.88 373.751 188.39 374.135 189.02 373.772Z"
              fill="white"
            />
            <path
              d="M204.769 364.682L209.48 361.962C210.109 361.599 210.62 360.624 210.62 359.784V354.756C210.62 353.916 210.109 353.529 209.48 353.894L204.769 356.614C204.14 356.976 203.629 357.952 203.629 358.792V363.82C203.629 364.66 204.14 365.046 204.769 364.682Z"
              fill="#A1BFFF"
            />
            <path
              d="M173.578 261.07L224.922 231.681C227.812 230.013 230.155 225.538 230.155 221.688V203.332C230.155 199.479 227.812 197.709 224.922 199.377L173.578 228.766C170.689 230.433 168.346 234.908 168.346 238.758V257.115C168.348 260.967 170.689 262.737 173.578 261.07Z"
              fill="#173D7A"
            />
            <path
              d="M227.464 198.821C228.271 199.582 228.777 200.88 228.777 202.64V220.997C228.777 224.847 226.433 229.322 223.544 230.989L172.2 260.378C171.272 260.915 170.413 261.061 169.659 260.932C170.618 261.836 172.009 261.972 173.577 261.068L224.921 231.679C227.81 230.011 230.153 225.536 230.153 221.686V203.33C230.153 200.715 229.061 199.093 227.464 198.821Z"
              fill="white"
            />
            <path
              d="M189.02 241.127L193.731 238.407C194.36 238.045 194.871 237.069 194.871 236.229V231.201C194.871 230.361 194.36 229.975 193.731 230.34L189.02 233.059C188.39 233.422 187.88 234.397 187.88 235.237V240.266C187.88 241.105 188.39 241.492 189.02 241.127Z"
              fill="white"
            />
            <path
              d="M204.769 232.039L209.48 229.32C210.109 228.957 210.62 227.981 210.62 227.142V222.113C210.62 221.273 210.109 220.887 209.48 221.252L204.769 223.971C204.14 224.334 203.629 225.31 203.629 226.149V231.178C203.629 232.015 204.14 232.402 204.769 232.039Z"
              fill="#A1BFFF"
            />
            <path
              d="M129.108 175.95L10.6533 107.575C8.00238 106.044 5.85254 101.938 5.85254 98.4055V75.8246C5.85254 72.2917 8.00238 70.6672 10.6533 72.1963L129.11 140.571C131.761 142.103 133.911 146.208 133.911 149.741V172.322C133.909 175.855 131.761 177.479 129.108 175.95Z"
              fill="#5793CE"
            />
            <path
              opacity="0.4"
              d="M125.707 168.032L14.0504 103.584C12.7047 102.806 11.6143 100.724 11.6143 98.932V81.9546C11.6143 80.1607 12.7047 79.3377 14.0504 80.113L125.707 144.561C127.053 145.339 128.143 147.422 128.143 149.213V166.191C128.143 167.984 127.053 168.807 125.707 168.032Z"
              fill="#D4ECFF"
            />
            <path
              d="M37.3239 108.43L20.6595 98.8113C19.3543 98.0575 18.2949 96.0346 18.2949 94.2932C18.2949 92.5518 19.3543 91.7527 20.6595 92.5065L37.3239 102.125C38.6291 102.879 39.6885 104.901 39.6885 106.643C39.6885 108.384 38.6291 109.183 37.3239 108.43Z"
              fill="#D4ECFF"
            />
            <path
              d="M51.5208 113.48C51.5208 115.221 50.2991 115.927 48.7887 115.057C47.2807 114.186 46.0566 112.068 46.0566 110.329C46.0566 108.587 47.2807 107.881 48.7887 108.752C50.2967 109.62 51.5208 111.738 51.5208 113.48Z"
              fill="#173D7A"
            />
            <path
              d="M61.1555 119.038C61.1555 120.78 59.9338 121.486 58.4234 120.615C56.9154 119.744 55.6914 117.626 55.6914 115.887C55.6914 114.146 56.9154 113.44 58.4234 114.31C59.9314 115.179 61.1555 117.297 61.1555 119.038Z"
              fill="#173D7A"
            />
            <path
              d="M70.7923 124.598C70.7923 126.339 69.5682 127.045 68.0602 126.175C66.5522 125.304 65.3281 123.186 65.3281 121.447C65.3281 119.705 66.5522 118.999 68.0602 119.87C69.5682 120.741 70.7923 122.857 70.7923 124.598Z"
              fill="#D4ECFF"
            />
            <path
              d="M80.424 130.159C80.424 131.9 79.2024 132.607 77.692 131.736C76.184 130.865 74.96 128.747 74.96 127.008C74.96 125.269 76.1816 124.56 77.692 125.431C79.2024 126.302 80.424 128.418 80.424 130.159Z"
              fill="#173D7A"
            />
            <path
              d="M121.011 158.58L115.399 155.34C114.562 154.856 113.882 153.558 113.882 152.442V146.504C113.882 145.388 114.562 144.873 115.399 145.357L121.011 148.596C121.849 149.081 122.529 150.378 122.529 151.495V157.432C122.529 158.549 121.851 159.061 121.011 158.58Z"
              fill="#173D7A"
            />
            <path
              d="M129.105 215.769L10.6484 147.394C7.9975 145.865 5.84766 141.757 5.84766 138.224V115.643C5.84766 112.108 7.9975 110.486 10.6484 112.015L129.105 180.387C131.756 181.919 133.906 186.024 133.906 189.557V212.138C133.906 215.676 131.759 217.3 129.105 215.769Z"
              fill="#5793CE"
            />
            <path
              opacity="0.4"
              d="M125.706 207.85L14.0514 143.402C12.7057 142.624 11.6152 140.542 11.6152 138.75V121.773C11.6152 119.979 12.7057 119.156 14.0514 119.931L125.708 184.38C127.054 185.157 128.144 187.24 128.144 189.031V206.009C128.142 207.803 127.051 208.626 125.706 207.85Z"
              fill="#D4ECFF"
            />
            <path
              d="M37.32 148.253L20.6556 138.635C19.3504 137.881 18.291 135.858 18.291 134.116C18.291 132.375 19.3504 131.576 20.6556 132.33L37.32 141.948C38.6252 142.702 39.6846 144.725 39.6846 146.466C39.6846 148.205 38.6252 149.007 37.32 148.253Z"
              fill="#D4ECFF"
            />
            <path
              d="M51.5169 153.298C51.5169 155.04 50.2952 155.746 48.7848 154.875C47.2768 154.004 46.0527 151.886 46.0527 150.147C46.0527 148.406 47.2768 147.699 48.7848 148.57C50.2928 149.438 51.5169 151.557 51.5169 153.298Z"
              fill="white"
            />
            <path
              d="M61.1546 158.857C61.1546 160.598 59.9329 161.304 58.4225 160.434C56.9145 159.563 55.6904 157.445 55.6904 155.706C55.6904 153.964 56.9145 153.258 58.4225 154.129C59.9305 154.999 61.1546 157.115 61.1546 158.857Z"
              fill="#173D7A"
            />
            <path
              d="M70.7884 164.418C70.7884 166.159 69.5643 166.865 68.0563 165.995C66.5483 165.124 65.3242 163.006 65.3242 161.267C65.3242 159.528 66.5483 158.819 68.0563 159.69C69.5643 160.561 70.7884 162.676 70.7884 164.418Z"
              fill="#D4ECFF"
            />
            <path
              d="M80.4211 169.98C80.4211 171.721 79.1994 172.427 77.6891 171.557C76.1811 170.686 74.957 168.57 74.957 166.829C74.957 165.087 76.1787 164.381 77.6891 165.252C79.1994 166.12 80.4211 168.238 80.4211 169.98Z"
              fill="#173D7A"
            />
            <path
              d="M121.01 198.398L115.397 195.158C114.56 194.674 113.88 193.377 113.88 192.26V186.323C113.88 185.206 114.56 184.691 115.397 185.175L121.01 188.415C121.847 188.899 122.527 190.197 122.527 191.313V197.251C122.527 198.367 121.849 198.882 121.01 198.398Z"
              fill="#173D7A"
            />
            <path
              d="M129.105 255.59L10.6484 187.217C7.9975 185.688 5.84766 181.58 5.84766 178.048V155.467C5.84766 151.931 7.9975 150.307 10.6484 151.838L129.105 220.211C131.756 221.742 133.906 225.848 133.906 229.381V251.961C133.906 255.497 131.759 257.121 129.105 255.59Z"
              fill="#5793CE"
            />
            <path
              opacity="0.4"
              d="M125.708 247.673L14.0514 183.224C12.7057 182.447 11.6152 180.364 11.6152 178.573V161.595C11.6152 159.801 12.7057 158.978 14.0514 159.754L125.708 224.202C127.054 224.98 128.144 227.062 128.144 228.854V245.831C128.144 247.625 127.054 248.45 125.708 247.673Z"
              fill="#D4ECFF"
            />
            <path
              d="M37.32 188.073L20.6556 178.455C19.3504 177.701 18.291 175.678 18.291 173.937C18.291 172.195 19.3504 171.396 20.6556 172.15L37.32 181.768C38.6252 182.522 39.6846 184.545 39.6846 186.286C39.6846 188.025 38.6252 188.827 37.32 188.073Z"
              fill="#D4ECFF"
            />
            <path
              d="M51.5169 193.118C51.5169 194.859 50.2952 195.565 48.7848 194.694C47.2768 193.824 46.0527 191.705 46.0527 189.966C46.0527 188.225 47.2768 187.519 48.7848 188.389C50.2928 189.26 51.5169 191.376 51.5169 193.118Z"
              fill="white"
            />
            <path
              d="M61.1546 198.677C61.1546 200.418 59.9329 201.125 58.4225 200.254C56.9145 199.383 55.6904 197.265 55.6904 195.526C55.6904 193.784 56.9145 193.078 58.4225 193.949C59.9305 194.82 61.1546 196.936 61.1546 198.677Z"
              fill="#173D7A"
            />
            <path
              d="M70.7884 204.241C70.7884 205.982 69.5643 206.688 68.0563 205.817C66.5483 204.947 65.3242 202.831 65.3242 201.089C65.3242 199.348 66.5483 198.642 68.0563 199.513C69.5643 200.381 70.7884 202.499 70.7884 204.241Z"
              fill="#D4ECFF"
            />
            <path
              d="M80.4211 209.8C80.4211 211.542 79.1994 212.248 77.6891 211.377C76.1811 210.506 74.957 208.39 74.957 206.649C74.957 204.908 76.1787 204.201 77.6891 205.072C79.1994 205.94 80.4211 208.059 80.4211 209.8Z"
              fill="#173D7A"
            />
            <path
              d="M121.01 238.22L115.397 234.981C114.56 234.497 113.88 233.199 113.88 232.082V226.145C113.88 225.029 114.56 224.513 115.397 224.998L121.01 228.237C121.847 228.721 122.527 230.019 122.527 231.135V237.073C122.527 238.189 121.849 238.704 121.01 238.22Z"
              fill="#173D7A"
            />
            <path
              d="M129.105 295.416L10.6484 227.041C7.9975 225.51 5.84766 221.405 5.84766 217.872V195.291C5.84766 191.756 7.9975 190.131 10.6484 191.663L129.105 260.035C131.756 261.564 133.906 265.672 133.906 269.205V291.786C133.906 295.321 131.759 296.945 129.105 295.416Z"
              fill="#5793CE"
            />
            <path
              opacity="0.4"
              d="M125.703 287.497L14.0465 223.049C12.7008 222.271 11.6104 220.188 11.6104 218.397V201.419C11.6104 199.626 12.7008 198.803 14.0465 199.578L125.703 264.026C127.049 264.804 128.139 266.886 128.139 268.678V285.655C128.139 287.449 127.049 288.275 125.703 287.497Z"
              fill="#D4ECFF"
            />
            <path
              d="M37.32 227.897L20.6556 218.279C19.3504 217.525 18.291 215.502 18.291 213.761C18.291 212.02 19.3504 211.22 20.6556 211.974L37.32 221.593C38.6252 222.346 39.6846 224.369 39.6846 226.111C39.6846 227.852 38.6252 228.651 37.32 227.897Z"
              fill="#D4ECFF"
            />
            <path
              d="M51.5169 232.941C51.5169 234.683 50.2952 235.389 48.7848 234.518C47.2768 233.647 46.0527 231.529 46.0527 229.79C46.0527 228.051 47.2768 227.343 48.7848 228.213C50.2928 229.084 51.5169 231.2 51.5169 232.941Z"
              fill="#173D7A"
            />
            <path
              d="M61.1546 238.501C61.1546 240.242 59.9329 240.948 58.4225 240.078C56.9145 239.207 55.6904 237.089 55.6904 235.35C55.6904 233.611 56.9145 232.902 58.4225 233.773C59.9305 234.644 61.1546 236.762 61.1546 238.501Z"
              fill="#173D7A"
            />
            <path
              d="M70.7884 244.065C70.7884 245.806 69.5643 246.512 68.0563 245.642C66.5483 244.771 65.3242 242.653 65.3242 240.914C65.3242 239.172 66.5483 238.466 68.0563 239.337C69.5643 240.205 70.7884 242.323 70.7884 244.065Z"
              fill="#D4ECFF"
            />
            <path
              d="M80.4211 249.624C80.4211 251.366 79.1994 252.072 77.6891 251.201C76.1811 250.33 74.957 248.212 74.957 246.473C74.957 244.732 76.1787 244.026 77.6891 244.896C79.1994 245.765 80.4211 247.883 80.4211 249.624Z"
              fill="#173D7A"
            />
            <path
              d="M121.01 278.044L115.397 274.805C114.56 274.321 113.88 273.023 113.88 271.907V265.969C113.88 264.853 114.56 264.337 115.397 264.822L121.01 268.061C121.847 268.545 122.527 269.843 122.527 270.96V276.897C122.527 278.016 121.849 278.529 121.01 278.044Z"
              fill="#D4ECFF"
            />
            <path
              d="M129.105 335.238L10.6484 266.863C7.9975 265.334 5.84766 261.226 5.84766 257.693V235.112C5.84766 231.577 7.9975 229.952 10.6484 231.484L129.105 299.859C131.756 301.388 133.906 305.496 133.906 309.028V331.609C133.906 335.142 131.759 336.767 129.105 335.238Z"
              fill="#5793CE"
            />
            <path
              opacity="0.4"
              d="M125.703 327.318L14.0465 262.87C12.7008 262.092 11.6104 260.01 11.6104 258.218V241.241C11.6104 239.447 12.7008 238.624 14.0465 239.399L125.703 303.848C127.049 304.623 128.139 306.708 128.139 308.499V325.477C128.139 327.271 127.049 328.096 125.703 327.318Z"
              fill="#D4ECFF"
            />
            <path
              d="M37.32 267.719L20.6556 258.1C19.3504 257.347 18.291 255.324 18.291 253.582C18.291 251.841 19.3504 251.042 20.6556 251.796L37.32 261.414C38.6252 262.168 39.6846 264.191 39.6846 265.932C39.6846 267.673 38.6252 268.472 37.32 267.719Z"
              fill="#D4ECFF"
            />
            <path
              d="M51.5169 272.761C51.5169 274.502 50.2952 275.209 48.7848 274.338C47.2768 273.467 46.0527 271.349 46.0527 269.61C46.0527 267.868 47.2768 267.162 48.7848 268.033C50.2928 268.904 51.5169 271.02 51.5169 272.761Z"
              fill="#173D7A"
            />
            <path
              d="M61.1546 278.325C61.1546 280.066 59.9329 280.772 58.4225 279.901C56.9145 279.031 55.6904 276.912 55.6904 275.173C55.6904 273.432 56.9145 272.726 58.4225 273.597C59.9305 274.465 61.1546 276.583 61.1546 278.325Z"
              fill="white"
            />
            <path
              d="M70.7884 283.886C70.7884 285.627 69.5643 286.333 68.0563 285.462C66.5483 284.592 65.3242 282.473 65.3242 280.734C65.3242 278.995 66.5483 278.287 68.0563 279.158C69.5643 280.026 70.7884 282.144 70.7884 283.886Z"
              fill="#D4ECFF"
            />
            <path
              d="M80.4211 289.445C80.4211 291.187 79.1994 291.893 77.6891 291.022C76.1811 290.151 74.957 288.033 74.957 286.294C74.957 284.555 76.1787 283.846 77.6891 284.717C79.1994 285.588 80.4211 287.704 80.4211 289.445Z"
              fill="white"
            />
            <path
              d="M121.01 317.866L115.397 314.626C114.56 314.142 113.88 312.844 113.88 311.728V305.79C113.88 304.674 114.56 304.159 115.397 304.643L121.01 307.882C121.847 308.367 122.527 309.664 122.527 310.781V316.718C122.527 317.837 121.849 318.35 121.01 317.866Z"
              fill="#D4ECFF"
            />
            <path
              d="M129.105 375.061L10.6484 306.686C7.9975 305.154 5.84766 301.049 5.84766 297.516V274.935C5.84766 271.402 7.9975 269.778 10.6484 271.307L129.105 339.682C131.756 341.213 133.906 345.318 133.906 348.851V371.432C133.906 374.965 131.759 376.59 129.105 375.061Z"
              fill="#5793CE"
            />
            <path
              opacity="0.4"
              d="M125.708 367.142L14.0514 302.694C12.7057 301.916 11.6152 299.834 11.6152 298.042V281.065C11.6152 279.271 12.7057 278.448 14.0514 279.223L125.708 343.672C127.054 344.449 128.144 346.532 128.144 348.323V365.301C128.144 367.095 127.054 367.918 125.708 367.142Z"
              fill="#D4ECFF"
            />
            <path
              d="M37.32 307.54L20.6556 297.922C19.3504 297.168 18.291 295.145 18.291 293.404C18.291 291.662 19.3504 290.863 20.6556 291.617L37.32 301.235C38.6252 301.989 39.6846 304.012 39.6846 305.753C39.6846 307.495 38.6252 308.294 37.32 307.54Z"
              fill="#D4ECFF"
            />
            <path
              d="M51.5169 312.587C51.5169 314.329 50.2952 315.035 48.7848 314.164C47.2768 313.293 46.0527 311.175 46.0527 309.436C46.0527 307.695 47.2768 306.989 48.7848 307.859C50.2928 308.73 51.5169 310.848 51.5169 312.587Z"
              fill="#173D7A"
            />
            <path
              d="M61.1546 318.149C61.1546 319.89 59.9329 320.596 58.4225 319.726C56.9145 318.855 55.6904 316.737 55.6904 314.998C55.6904 313.256 56.9145 312.55 58.4225 313.421C59.9305 314.289 61.1546 316.407 61.1546 318.149Z"
              fill="#173D7A"
            />
            <path
              d="M70.7884 323.71C70.7884 325.452 69.5643 326.158 68.0563 325.287C66.5483 324.416 65.3242 322.298 65.3242 320.559C65.3242 318.818 66.5483 318.112 68.0563 318.982C69.5643 319.853 70.7884 321.969 70.7884 323.71Z"
              fill="#D4ECFF"
            />
            <path
              d="M80.4211 329.268C80.4211 331.009 79.1994 331.715 77.6891 330.845C76.1811 329.974 74.957 327.856 74.957 326.117C74.957 324.375 76.1787 323.669 77.6891 324.54C79.1994 325.411 80.4211 327.526 80.4211 329.268Z"
              fill="#173D7A"
            />
            <path
              d="M121.01 357.69L115.397 354.451C114.56 353.967 113.88 352.669 113.88 351.552V345.615C113.88 344.496 114.56 343.983 115.397 344.468L121.01 347.707C121.847 348.191 122.527 349.489 122.527 350.605V356.543C122.527 357.659 121.849 358.172 121.01 357.69Z"
              fill="#173D7A"
            />
            <path
              d="M129.105 414.877L10.6484 346.502C7.9975 344.973 5.84766 340.866 5.84766 337.333V314.752C5.84766 311.216 7.9975 309.592 10.6484 311.123L129.105 379.496C131.756 381.027 133.906 385.133 133.906 388.666V411.247C133.906 414.784 131.759 416.409 129.105 414.877Z"
              fill="#5793CE"
            />
            <path
              opacity="0.4"
              d="M125.708 406.958L14.0514 342.509C12.7057 341.732 11.6152 339.649 11.6152 337.858V320.88C11.6152 319.086 12.7057 318.263 14.0514 319.039L125.708 383.487C127.054 384.265 128.144 386.347 128.144 388.139V405.116C128.144 406.91 127.054 407.733 125.708 406.958Z"
              fill="#D4ECFF"
            />
            <path
              d="M37.32 347.358L20.6556 337.74C19.3504 336.986 18.291 334.963 18.291 333.222C18.291 331.481 19.3504 330.681 20.6556 331.435L37.32 341.053C38.6252 341.807 39.6846 343.83 39.6846 345.572C39.6846 347.313 38.6252 348.112 37.32 347.358Z"
              fill="#D4ECFF"
            />
            <path
              d="M54.5635 354.561C54.5635 357.85 52.2537 359.184 49.4048 357.538C46.5558 355.894 44.2461 351.894 44.2461 348.604C44.2461 345.315 46.5558 343.981 49.4048 345.627C52.2537 347.273 54.5635 351.271 54.5635 354.561Z"
              fill="white"
            />
            <path
              d="M74.5188 366.232C74.5188 367.973 73.2971 368.679 71.7867 367.809C70.2787 366.938 69.0547 364.82 69.0547 363.081C69.0547 361.339 70.2763 360.633 71.7867 361.504C73.2971 362.374 74.5188 364.49 74.5188 366.232Z"
              fill="#173D7A"
            />
            <path
              d="M84.1525 371.793C84.1525 373.535 82.9309 374.241 81.4205 373.37C79.9125 372.499 78.6885 370.381 78.6885 368.642C78.6885 366.901 79.9101 366.195 81.4205 367.065C82.9309 367.936 84.1525 370.054 84.1525 371.793Z"
              fill="#D4ECFF"
            />
            <path
              d="M93.7913 377.355C93.7913 379.096 92.5696 379.802 91.0592 378.932C89.5512 378.061 88.3271 375.943 88.3271 374.204C88.3271 372.462 89.5488 371.756 91.0592 372.627C92.5696 373.495 93.7913 375.613 93.7913 377.355Z"
              fill="#173D7A"
            />
            <path
              d="M121.009 397.505L115.397 394.266C114.56 393.782 113.88 392.484 113.88 391.368V385.43C113.88 384.314 114.56 383.798 115.397 384.283L121.009 387.522C121.847 388.006 122.527 389.304 122.527 390.42V396.358C122.527 397.474 121.849 397.99 121.009 397.505Z"
              fill="#D4ECFF"
            />
          </svg>
        </div>
      </div>
    </section>
    <!-- end of choose your hosting service -->

    <footer class="bg-primary-blue text-white">
      <!-- first part -->
      <div
        class="grid grid-cols-2 sm:grid-cols-3 gap-x-2 gap-y-5 lg:grid-cols-4 xl:grid-cols-7 lg:gap-4 xl:gap-3 container pt-[93px] pb-12"
      >
        <!-- first col -->
        <div class="space-y-5 col-span-2 xl:col-span-2">
          <div class="flex items-center gap-6">
            <img
              src="/public/pages/home-page/footer/serv5-logo.svg"
              alt="Serv5 Logo"
              class="size-[98px]"
            />
            <p
              class="font-bold text-xl border-b border-b-primary-yellow pb-2 max-w-[300px]"
            >
              عن سيرف
            </p>
          </div>

          <p class="text-sm leading-7 max-2-[410px]">
            لقد وصلت لوجهتك الصحيحة؛ ما تبحث عنه امامك معنا سوف تحصل على خدمة
            تصميم مواقع ، برمجة مواقع ، برمجة تطبيقات جوال ، خدمات تسويقية
            متميزة.
          </p>
          <!-- the images of our projects  -->
          <div class="flex gap-2 items-center">
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/blmgan.webp"
                alt="Blmgan app"
                class="object-contain w-[52px]"
              />
            </div>
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/makok.webp"
                alt="Makok Website"
                class="object-contain w-[52px]"
              />
            </div>
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/needbots.webp"
                alt="Chat app neat bot"
                class="object-contain w-[52px]"
              />
            </div>
          </div>
        </div>
        <!-- second col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            روابط هامة
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- third col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            الخدمات
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- fourth col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            تواصل معنا
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-envelope text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-phone text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-location-pin-lock text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- fifth col -->
        <div class="flex flex-col gap-4 col-span-1 xl:col-span-2">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            إشترك معنا
          </p>

          <p class="mt-4 lg:mt-10 flex flex-col gap-3">
            قم بكتابة بريدك الإلكتروني وإشترك في النشرة البريدية
          </p>

          <!-- Newsletter Subscription Form -->
          <form id="newsletter-form" class="md:mt-6 max-w-[350px]">
            <div class="newsletter-input-group relative">
              <i
                class="fa-solid fa-arrow-right absolute text-primary-yellow top-1/2 -translate-y-1/2"
              ></i>
              <input
                type="email"
                id="newsletter-email"
                name="email"
                placeholder="أدخل بريدك الإلكتروني"
                class="w-full bg-transparent text-white placeholder-gray-300 py-3 border-0 border-b border-gray-400 ps-5 placeholder:text-sm focus:border-primary-yellow focus:outline-none transition-colors duration-300"
                required
              />
              <div
                id="email-error"
                class="text-red-400 text-xs mt-1 hidden"
              ></div>
            </div>

            <div
              id="newsletter-success"
              class="text-green-400 text-sm mt-2 hidden"
            >
              تم الاشتراك بنجاح! شكراً لك.
            </div>
          </form>
        </div>
      </div>
      <!-- second part -->
      <div
        class="container flex flex-col gap-5 sm:flex-row justify-between items-center py-5 w-full border-t border-t-gray"
      >
        <!-- go up and terms and condtions links -->
        <div class="flex gap-4 items-center">
          <button
            id="goToTopBtn"
            class="size-12 p-5 text-white bg-primary-yellow hover:scale-110 transition-colors cursor-pointer flex items-center justify-center"
          >
            <i class="fa-solid fa-arrow-up text-xl"></i>
          </button>

          <a href="#" class="hover:text-primary-yellow">سياسة الخصوصية </a>
          <div class="w-[1px] h-6 bg-white my-auto"></div>
          <a href="#" class="hover:text-primary-yellow">الشروط والأحكــام</a>
        </div>

        <!-- all right reserved text -->
        <p class="text-sm text-center">
          © 2025 جميع الحقوق محفوظة لشركة سيرف. جميع الحقوق محفوظة.
        </p>
      </div>
    </footer>

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
