import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Professional Banner Section GSAP Animation with Continuous Floating
export function initBannerGSAP() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);


  // Check if dots exist immediately
  const grayDots = document.querySelectorAll(".gray-dot");
  const orangeDots = document.querySelectorAll(".orange-dot");

  // Test immediate animation without ScrollTrigger first
  if (grayDots.length > 0) {
    gsap.to(".gray-dot", {
      x: 20,
      duration: 1,
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });
  }

  if (orangeDots.length > 0) {
    gsap.to(".orange-dot", {
      y: 20,
      duration: 1.5,
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });
  }

  // Create banner section timeline (exclude breadcrumbs section)
  const bannerTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".bottom-banner:not(.breadcrumbs-section)",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse",
    }
  });

  // Set initial states for banner elements (exclude breadcrumbs)
  gsap.set(".bottom-banner:not(.breadcrumbs-section) .banner-text", {
    autoAlpha: 0,
    y: 80,
    scale: 0.8,
    skewY: 6,
    filter: "blur(10px)"
  });

  gsap.set(".bottom-banner:not(.breadcrumbs-section) .banner-button", {
    autoAlpha: 0,
    scale: 0.7,
    x: -40,
    skewX: 8,
    filter: "blur(6px)"
  });

  // Set initial states for floating icons (exclude breadcrumbs)
  gsap.set(".bottom-banner:not(.breadcrumbs-section) .floating-icon", {
    autoAlpha: 0,
    scale: 0.6,
    rotation: 20,
    filter: "blur(8px)"
  });

  // Set initial states for gray dots
  gsap.set(".gray-dot", {
    autoAlpha: 0,
    scale: 0.1,
    rotation: 180,
    filter: "blur(8px)",
    y: -50
  });

  // Set initial states for orange dots
  gsap.set(".orange-dot", {
    autoAlpha: 0,
    scale: 0.1,
    rotation: -180,
    filter: "blur(8px)",
    y: -60
  });

  // Animation sequence
  bannerTimeline
    // 1. Banner text entrance
    .to(".bottom-banner:not(.breadcrumbs-section) .banner-text", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1.2,
      ease: "back.out(1.7)"
    })

    // 2. Banner button entrance
    .to(".bottom-banner:not(.breadcrumbs-section) .banner-button", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.6")

    // 3. Floating icons with stagger
    .to(".bottom-banner:not(.breadcrumbs-section) .floating-icon", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: 0.15,
      ease: "back.out(1.7)"
    }, "-=0.4")

    // 4. Gray dots entrance with dramatic effects
    .to(".gray-dot", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      y: 0,
      filter: "blur(0px)",
      duration: 1.2,
      stagger: {
        amount: 0.6,
        from: "random",
        ease: "power2.out"
      },
      ease: "elastic.out(1, 0.5)"
    }, "-=0.3")

    // 5. Orange dots entrance with different timing
    .to(".orange-dot", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      y: 0,
      filter: "blur(0px)",
      duration: 1.4,
      stagger: {
        amount: 0.8,
        from: "random",
        ease: "power2.out"
      },
      ease: "elastic.out(1, 0.3)"
    }, "-=0.8");

  return bannerTimeline;
}

// Add continuous floating animation to banner elements
export function initBannerFloatingAnimation() {
  // Wait for initial animation to complete
  setTimeout(() => {
    // Floating animation for chat icon
    gsap.to(".floating-icon[alt='Chat']", {
      y: "random(-20, 20)",
      x: "random(-15, 15)",
      rotation: "random(-8, 8)",
      duration: "random(4, 6)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    // Floating animation for book icon
    gsap.to(".floating-icon[alt='Book']", {
      y: "random(-25, 25)",
      x: "random(-12, 12)",
      rotation: "random(-10, 10)",
      duration: "random(3.5, 5.5)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    // Floating animation for envelope icons
    gsap.to(".floating-icon[alt='Envelope']", {
      y: "random(-18, 18)",
      x: "random(-20, 20)",
      rotation: "random(-6, 6)",
      duration: "random(4.5, 6.5)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    // Simple floating animation for gray dots (same as icons)
    gsap.to(".gray-dot", {
      y: "random(-15, 15)",
      x: "random(-12, 12)",
      rotation: "random(-5, 5)",
      duration: "random(3, 5)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    // Simple floating animation for orange dots (same as icons)
    gsap.to(".orange-dot", {
      y: "random(-18, 18)",
      x: "random(-15, 15)",
      rotation: "random(-8, 8)",
      duration: "random(4, 6)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    // Floating animation for dotted line container
    gsap.to(".bottom-banner .absolute.top-1\\/2.-translate-y-1\\/2", {
      y: "random(-5, 5)",
      scaleY: "random(0.98, 1.02)",
      duration: "random(6, 8)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

  }, 2500);
}

// Professional hover effects for banner elements
export function initBannerHoverEffects() {
  const bannerButton = document.querySelector('.banner-button');
  const floatingIcons = document.querySelectorAll('.bottom-banner .floating-icon');

  // Banner button hover effect
  if (bannerButton) {
    bannerButton.addEventListener('mouseenter', () => {
      gsap.to(bannerButton, {
        scale: 1.05,
        y: -5,
        boxShadow: "0 15px 30px rgba(0,0,0,0.2)",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    bannerButton.addEventListener('mouseleave', () => {
      gsap.to(bannerButton, {
        scale: 1,
        y: 0,
        boxShadow: "0 5px 15px rgba(0,0,0,0.1)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  }

  // Floating icons hover effects
  floatingIcons.forEach(icon => {
    icon.addEventListener('mouseenter', () => {
      gsap.to(icon, {
        scale: 1.2,
        rotation: 15,
        filter: "brightness(1.2) contrast(1.1)",
        duration: 0.3,
        ease: "back.out(1.7)"
      });
    });

    icon.addEventListener('mouseleave', () => {
      gsap.to(icon, {
        scale: 1,
        rotation: 0,
        filter: "brightness(1) contrast(1)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  });
}
