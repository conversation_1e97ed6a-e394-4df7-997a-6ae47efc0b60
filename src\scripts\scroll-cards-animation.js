import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Professional Scroll-Triggered Cards Animation
export function initScrollCardsAnimation() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);


  // Get all elements
  const section = document.querySelector('.scroll-cards-section');
  const container = document.querySelector('.scroll-cards-container');
  const title = document.querySelector('.scroll-cards-title');
  const cards = document.querySelectorAll('.scroll-card');
  const dots = document.querySelectorAll('.progress-dot');

  if (!section || !container || !title || cards.length === 0) {
    return;
  }


  // Set initial states
  gsap.set(title, {
    autoAlpha: 1,
    y: 0,
    scale: 1
  });

  gsap.set(cards, {
    autoAlpha: 0,
    y: 100,
    scale: 0.8,
    skewY: 5,
    filter: "blur(10px)",
    rotationX: 15
  });

  gsap.set(dots, {
    scale: 1,
    backgroundColor: "rgba(255, 255, 255, 0.3)"
  });

  // Create main timeline with ScrollTrigger
  const mainTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: section,
      start: "top top",
      end: "bottom bottom",
      scrub: 1,
      pin: container,
      anticipatePin: 1,
      onUpdate: (self) => {
        // Update background gradient based on scroll progress
        const progress = self.progress;
        const hue = 220 + (progress * 60); // From blue to purple
        section.style.background = `linear-gradient(135deg, 
          hsl(${hue}, 70%, 15%) 0%, 
          hsl(${hue + 20}, 60%, 25%) 50%, 
          hsl(${hue + 40}, 50%, 35%) 100%)`;
      },
    }
  });

  // Calculate timing for each card
  const cardDuration = 1 / (cards.length + 1); // +1 for title fade out

  // 1. Title fade out animation
  mainTimeline.to(title, {
    autoAlpha: 0.3,
    y: -50,
    scale: 0.9,
    duration: cardDuration,
    ease: "power2.inOut"
  }, 0);

  // 2. Animate each card
  cards.forEach((card, index) => {
    const cardIndex = parseInt(card.dataset.card);
    const startTime = (index + 1) * cardDuration;
    const endTime = (index + 2) * cardDuration;

    // Card entrance animation
    mainTimeline.to(card, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      rotationX: 0,
      duration: cardDuration * 0.6,
      ease: "back.out(1.7)"
    }, startTime);

    // Card exit animation (except for the last card)
    if (index < cards.length - 1) {
      mainTimeline.to(card, {
        autoAlpha: 0,
        y: -100,
        scale: 0.8,
        skewY: -5,
        filter: "blur(8px)",
        rotationX: -15,
        duration: cardDuration * 0.4,
        ease: "power2.in"
      }, endTime - cardDuration * 0.4);
    }

    // Update progress dots
    mainTimeline.to(dots[cardIndex], {
      scale: 1.5,
      backgroundColor: "#fbbf24", // yellow-400
      duration: 0.1,
      ease: "power2.out"
    }, startTime);

    // Reset previous dot (except for first card)
    if (index > 0) {
      const prevDotIndex = parseInt(cards[index - 1].dataset.card);
      mainTimeline.to(dots[prevDotIndex], {
        scale: 1,
        backgroundColor: "rgba(255, 255, 255, 0.3)",
        duration: 0.1,
        ease: "power2.out"
      }, startTime);
    }
  });

  // Add floating animation to cards
  cards.forEach((card, index) => {
    gsap.to(card, {
      y: "+=10",
      duration: 2 + (index * 0.2),
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: index * 0.3
    });
  });

  // Add floating animation to dots
  dots.forEach((dot, index) => {
    gsap.to(dot, {
      y: "+=3",
      duration: 1.5 + (index * 0.1),
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: index * 0.2
    });
  });

  // Refresh ScrollTrigger on window resize
  window.addEventListener('resize', () => {
    ScrollTrigger.refresh();
  });

}

// Enhanced version with more dramatic effects
export function initScrollCardsAnimationEnhanced() {
  gsap.registerPlugin(ScrollTrigger);


  const section = document.querySelector('.scroll-cards-section');
  const container = document.querySelector('.scroll-cards-container');
  const title = document.querySelector('.scroll-cards-title');
  const cards = document.querySelectorAll('.scroll-card');
  const dots = document.querySelectorAll('.progress-dot');

  if (!section || !container || !title || cards.length === 0) {
    return;
  }

  // More dramatic initial states
  gsap.set(title, {
    autoAlpha: 1,
    y: 0,
    scale: 1,
    rotationX: 0
  });

  gsap.set(cards, {
    autoAlpha: 0,
    y: 150,
    scale: 0.6,
    skewY: 10,
    skewX: 5,
    filter: "blur(20px)",
    rotationX: 30,
    rotationY: 15
  });

  // Enhanced timeline with more complex animations
  const enhancedTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: section,
      start: "top top",
      end: "bottom bottom",
      scrub: 0.5,
      pin: container,
      anticipatePin: 1,
      onUpdate: (self) => {
        const progress = self.progress;

        // Dynamic background with multiple gradients
        const hue1 = 220 + (progress * 80);
        const hue2 = 260 + (progress * 60);
        const hue3 = 300 + (progress * 40);

        section.style.background = `
          radial-gradient(circle at 20% 80%, hsl(${hue1}, 70%, 20%) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, hsl(${hue2}, 60%, 25%) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, hsl(${hue3}, 50%, 30%) 0%, transparent 50%),
          linear-gradient(135deg, hsl(${hue1}, 70%, 15%) 0%, hsl(${hue2}, 60%, 25%) 100%)
        `;
      }
    }
  });

  const cardDuration = 1 / (cards.length + 1);

  // Enhanced title animation
  enhancedTimeline.to(title, {
    autoAlpha: 0.2,
    y: -80,
    scale: 0.8,
    rotationX: -20,
    filter: "blur(5px)",
    duration: cardDuration,
    ease: "power3.inOut"
  }, 0);

  // Enhanced card animations
  cards.forEach((card, index) => {
    const cardIndex = parseInt(card.dataset.card);
    const startTime = (index + 1) * cardDuration;
    const endTime = (index + 2) * cardDuration;

    // Dramatic entrance
    enhancedTimeline.to(card, {
      autoAlpha: 1,
      y: 0,
      scale: 1.05,
      skewY: 0,
      skewX: 0,
      filter: "blur(0px)",
      rotationX: 0,
      rotationY: 0,
      duration: cardDuration * 0.7,
      ease: "elastic.out(1, 0.8)"
    }, startTime);

    // Scale back to normal
    enhancedTimeline.to(card, {
      scale: 1,
      duration: cardDuration * 0.3,
      ease: "power2.out"
    }, startTime + cardDuration * 0.2);

    // Dramatic exit (except last card)
    if (index < cards.length - 1) {
      enhancedTimeline.to(card, {
        autoAlpha: 0,
        y: -120,
        scale: 0.7,
        skewY: -8,
        skewX: -3,
        filter: "blur(15px)",
        rotationX: -25,
        rotationY: -10,
        duration: cardDuration * 0.5,
        ease: "power3.in"
      }, endTime - cardDuration * 0.5);
    }

    // Enhanced dot animations
    enhancedTimeline.to(dots[cardIndex], {
      scale: 2,
      backgroundColor: "#fbbf24",
      boxShadow: "0 0 20px #fbbf24",
      duration: 0.2,
      ease: "back.out(1.7)"
    }, startTime);

    if (index > 0) {
      const prevDotIndex = parseInt(cards[index - 1].dataset.card);
      enhancedTimeline.to(dots[prevDotIndex], {
        scale: 1,
        backgroundColor: "rgba(255, 255, 255, 0.3)",
        boxShadow: "none",
        duration: 0.2,
        ease: "power2.out"
      }, startTime);
    }
  });

}
