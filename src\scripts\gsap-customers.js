import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// Professional Customer Section GSAP Animation
export function initCustomersGSAP() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Create customer section timeline
  const customersTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".customers-section",
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });

  // Set initial states for section elements
  gsap.set(".customers-title", {
    autoAlpha: 0,
    y: 60,
    skewY: 5,
    filter: "blur(8px)"
  });

  gsap.set(".customers-subtitle", {
    autoAlpha: 0,
    y: 40,
    x: -30 * xDir,
    filter: "blur(6px)"
  });

  gsap.set(".customer-card", {
    autoAlpha: 0,
    scale: 0.7,
    y: 80,
    skewX: 8 * skewXDir,
    filter: "blur(10px)"
  });

  // Animation sequence - Optimized for speed (50% faster)
  customersTimeline
    // 1. Title animation with professional effects (faster)
    .to(".customers-title", {
      autoAlpha: 1,
      y: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.5,
      ease: "back.out(1.7)"
    })

    // 2. Subtitle slide from left/right (faster)
    .to(".customers-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      filter: "blur(0px)",
      duration: 0.4,
      ease: "power2.out"
    }, "-=0.25")

    // 3. Customer cards with staggered entrance (faster)
    .to(".customer-card", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.5,
      stagger: {
        amount: 0.6,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    }, "-=0.15");

  return customersTimeline;
}

// Advanced customer section animation with more complex effects
export function initCustomersGSAPAdvanced() {
  gsap.registerPlugin(ScrollTrigger);

  const customersTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".customers-section",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse",
    }
  });

  // More complex initial states
  gsap.set(".customers-title", {
    autoAlpha: 0,
    y: 80,
    rotationX: -15,
    skewY: 8,
    filter: "blur(12px)"
  });

  gsap.set(".customers-subtitle", {
    autoAlpha: 0,
    y: 50,
    x: -60,
    skewX: 5,
    filter: "blur(8px)"
  });

  // Individual card animations with different directions
  gsap.set(".customer-card:nth-child(1)", {
    autoAlpha: 0,
    scale: 0.5,
    x: -100,
    y: 60,
    rotation: -10,
    filter: "blur(15px)"
  });

  gsap.set(".customer-card:nth-child(2)", {
    autoAlpha: 0,
    scale: 0.6,
    y: -80,
    skewY: 12,
    filter: "blur(12px)"
  });

  gsap.set(".customer-card:nth-child(3)", {
    autoAlpha: 0,
    scale: 0.4,
    x: 80,
    y: 100,
    rotation: 15,
    filter: "blur(18px)"
  });

  gsap.set(".customer-card:nth-child(4)", {
    autoAlpha: 0,
    scale: 0.7,
    x: -60,
    y: -40,
    skewX: -8,
    filter: "blur(10px)"
  });

  gsap.set(".customer-card:nth-child(5)", {
    autoAlpha: 0,
    scale: 0.3,
    x: 120,
    y: 80,
    rotation: -20,
    filter: "blur(20px)"
  });

  // Complex animation sequence
  customersTimeline
    // Title with 3D rotation
    .to(".customers-title", {
      autoAlpha: 1,
      y: 0,
      rotationX: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1.2,
      ease: "back.out(2)"
    })

    // Subtitle with slide and skew
    .to(".customers-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "power3.out"
    }, "-=0.6")

    // Individual card animations
    .to(".customer-card:nth-child(1)", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      y: 0,
      rotation: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.7)"
    }, "-=0.4")

    .to(".customer-card:nth-child(2)", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.7)"
    }, "-=0.7")

    .to(".customer-card:nth-child(3)", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      y: 0,
      rotation: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.7)"
    }, "-=0.7")

    .to(".customer-card:nth-child(4)", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      y: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.7)"
    }, "-=0.7")

    .to(".customer-card:nth-child(5)", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      y: 0,
      rotation: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.7)"
    }, "-=0.7");

  return customersTimeline;
}

// Simple scroll-triggered customer animation
export function initCustomersScrollAnimation() {
  gsap.registerPlugin(ScrollTrigger);

  // Simple fade and scale animation
  gsap.fromTo(".customer-card",
    {
      autoAlpha: 0,
      scale: 0.8,
      y: 50
    },
    {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      duration: 0.8,
      stagger: 0.2,
      ease: "power2.out",
      scrollTrigger: {
        trigger: ".customers-section",
        start: "top 80%",
        toggleActions: "play none none reverse"
      }
    }
  );

  // Title animation
  gsap.fromTo(".customers-title",
    {
      autoAlpha: 0,
      y: 30
    },
    {
      autoAlpha: 1,
      y: 0,
      duration: 1,
      ease: "power2.out",
      scrollTrigger: {
        trigger: ".customers-section",
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    }
  );
}
