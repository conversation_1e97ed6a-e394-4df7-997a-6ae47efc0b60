// Newsletter Subscription Form Validation and Handling
export function initNewsletterForm() {
  const form = document.getElementById('newsletter-form');
  const emailInput = document.getElementById('newsletter-email');
  const submitButton = document.getElementById('newsletter-submit');
  const errorDiv = document.getElementById('email-error');
  const successDiv = document.getElementById('newsletter-success');

  if (!form || !emailInput || !submitButton) {
    return;
  }

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  // Real-time validation
  emailInput.addEventListener('input', function () {
    const email = this.value.trim();

    // Clear previous states
    errorDiv.classList.add('hidden');
    successDiv.classList.add('hidden');

    if (email === '') {
      // Empty input - no error shown
      this.classList.remove('border-red-400', 'border-green-400');
      this.classList.add('border-gray-400');
      return;
    }

    if (!emailRegex.test(email)) {
      // Invalid email
      this.classList.remove('border-gray-400', 'border-green-400');
      this.classList.add('border-red-400');
      showError('يرجى إدخال بريد إلكتروني صحيح');
    } else {
      // Valid email
      this.classList.remove('border-gray-400', 'border-red-400');
      this.classList.add('border-green-400');
      errorDiv.classList.add('hidden');
    }
  });

  // Form submission
  form.addEventListener('submit', function (e) {
    e.preventDefault();

    const email = emailInput.value.trim();

    // Clear previous states
    errorDiv.classList.add('hidden');
    successDiv.classList.add('hidden');

    // Validation
    if (email === '') {
      showError('يرجى إدخال بريدك الإلكتروني');
      emailInput.focus();
      return;
    }

    if (!emailRegex.test(email)) {
      showError('يرجى إدخال بريد إلكتروني صحيح');
      emailInput.focus();
      return;
    }

    // Show loading state
    submitButton.disabled = true;
    submitButton.textContent = 'جاري الإرسال...';

    // Simulate API call (replace with actual API endpoint)
    setTimeout(() => {
      // Simulate successful subscription
      showSuccess();

      // Reset form
      form.reset();
      emailInput.classList.remove('border-red-400', 'border-green-400');
      emailInput.classList.add('border-gray-400');

      // Reset button
      submitButton.disabled = false;
      submitButton.textContent = 'اشتراك';


    }, 2000); // 2 second delay to simulate network request
  });

  // Helper functions
  function showError(message) {
    errorDiv.textContent = message;
    errorDiv.classList.remove('hidden');
  }

  function showSuccess() {
    successDiv.classList.remove('hidden');

    // Hide success message after 5 seconds
    setTimeout(() => {
      successDiv.classList.add('hidden');
    }, 5000);
  }

  // Focus enhancement
  emailInput.addEventListener('focus', function () {
    this.classList.add('border-primary-yellow');
  });

  emailInput.addEventListener('blur', function () {
    if (!this.classList.contains('border-red-400') && !this.classList.contains('border-green-400')) {
      this.classList.remove('border-primary-yellow');
      this.classList.add('border-gray-400');
    }
  });

}

// Function to integrate with actual API (for future use)
export async function subscribeToNewsletter(email) {
  try {
    // Replace with your actual API endpoint
    const response = await fetch('/api/newsletter/subscribe', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email })
    });

    if (!response.ok) {
      throw new Error('Subscription failed');
    }

    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
}
