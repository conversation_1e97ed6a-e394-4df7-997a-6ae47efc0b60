import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Professional Features Section GSAP Animation
export function initFeaturesGSAP() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Create features section timeline
  const featuresTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".features-section",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse"
    }
  });

  // Set initial states for section elements
  gsap.set(".features-title", {
    autoAlpha: 0,
    y: 80,
    scale: 0.8,
    skewY: 6,
    filter: "blur(10px)"
  });

  gsap.set(".features-subtitle", {
    autoAlpha: 0,
    y: 50,
    x: -40,
    filter: "blur(6px)"
  });

  // Set initial state for the yellow underline
  gsap.set(".features-title span", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: "left center"
  });

  gsap.set(".feature-item", {
    autoAlpha: 0,
    scale: 0.7,
    y: 100,
    x: 50,
    skewX: 10,
    filter: "blur(8px)"
  });

  gsap.set(".bottom-banner:not(.breadcrumbs-section)", {
    autoAlpha: 0,
    y: 120,
    scale: 0.9,
    filter: "blur(12px)"
  });

  gsap.set(".bottom-banner:not(.breadcrumbs-section) .floating-icon", {
    autoAlpha: 0,
    scale: 0.3,
    rotation: 45,
    filter: "blur(6px)"
  });

  gsap.set(".bottom-banner:not(.breadcrumbs-section) .banner-text", {
    autoAlpha: 0,
    x: -80,
    skewX: 8,
    filter: "blur(8px)"
  });

  gsap.set(".banner-button", {
    autoAlpha: 0,
    scale: 0.6,
    y: 40,
    filter: "blur(5px)"
  });

  gsap.set(".banner-image", {
    autoAlpha: 0,
    x: 100,
    y: 60,
    scale: 0.8,
    filter: "blur(10px)"
  });

  gsap.set(".banner-logo", {
    autoAlpha: 0,
    scale: 0.2,
    rotation: 180,
    filter: "blur(8px)"
  });

  // Animation sequence - Optimized for speed (50% faster)
  featuresTimeline
    // 1. Title animation with professional effects (faster)
    .to(".features-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.4,
      ease: "back.out(1.7)"
    })

    // 2. Yellow underline animation (faster)
    .to(".features-title span", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.3,
      ease: "power2.out"
    }, "-=0.2")

    // 3. Subtitle slide from left (faster)
    .to(".features-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      filter: "blur(0px)",
      duration: 0.4,
      ease: "power2.out"
    }, "-=0.2")

    // 4. Feature items with staggered entrance (faster)
    .to(".feature-item", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      x: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.5,
      stagger: {
        amount: 0.9,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    }, "-=0.2")

    // 5. Bottom banner entrance (faster)
    .to(".bottom-banner:not(.breadcrumbs-section)", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.5,
      ease: "power3.out"
    }, "-=0.5")

    // 6. Banner text from left (faster)
    .to(".bottom-banner:not(.breadcrumbs-section) .banner-text", {
      autoAlpha: 1,
      x: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.4,
      ease: "back.out(1.4)"
    }, "-=0.3")

    // 6. Banner button
    .to(".banner-button", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      filter: "blur(0px)",
      duration: 0.7,
      ease: "back.out(1.7)"
    }, "-=0.4")

    // 7. Banner image from right
    .to(".banner-image", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.4)"
    }, "-=0.5")

    // 8. Banner logo with rotation
    .to(".banner-logo", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(2)"
    }, "-=0.3")

    // 9. Floating icons with stagger
    .to(".floating-icon", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: 0.1,
      ease: "back.out(1.7)"
    }, "-=0.6");

  return featuresTimeline;
}

// Add floating animation to icons after they appear
export function initFloatingIconsAnimation() {
  // Wait for initial animation to complete
  setTimeout(() => {
    gsap.to(".float-1", {
      y: "random(-15, 15)",
      x: "random(-10, 10)",
      rotation: "random(-5, 5)",
      duration: "random(3, 5)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    gsap.to(".float-2", {
      y: "random(-20, 20)",
      x: "random(-8, 8)",
      rotation: "random(-8, 8)",
      duration: "random(4, 6)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    gsap.to(".float-3", {
      y: "random(-12, 12)",
      x: "random(-15, 15)",
      rotation: "random(-6, 6)",
      duration: "random(3.5, 5.5)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });

    gsap.to(".float-4", {
      y: "random(-18, 18)",
      x: "random(-12, 12)",
      rotation: "random(-7, 7)",
      duration: "random(4, 6)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut"
    });
  }, 4000);
}

// Professional hover effects for feature items
export function initFeatureHoverEffects() {
  const featureItems = document.querySelectorAll('.feature-item');

  featureItems.forEach(item => {
    const icon = item.querySelector('.feature-icon');
    const handImage = item.querySelector('.feature-hand');

    item.addEventListener('mouseenter', () => {
      gsap.to(item, {
        y: -10,
        scale: 1.02,
        boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
        duration: 0.4,
        ease: "power2.out"
      });

      if (icon) {
        gsap.to(icon, {
          scale: 1.1,
          rotation: 5,
          duration: 0.4,
          ease: "back.out(1.7)"
        });
      }

      if (handImage) {
        gsap.to(handImage, {
          x: 10,
          scale: 1.05,
          duration: 0.4,
          ease: "power2.out"
        });
      }
    });

    item.addEventListener('mouseleave', () => {
      gsap.to(item, {
        y: 0,
        scale: 1,
        boxShadow: "0 5px 15px rgba(0,0,0,0.1)",
        duration: 0.4,
        ease: "power2.out"
      });

      if (icon) {
        gsap.to(icon, {
          scale: 1,
          rotation: 0,
          duration: 0.4,
          ease: "power2.out"
        });
      }

      if (handImage) {
        gsap.to(handImage, {
          x: 0,
          scale: 1,
          duration: 0.4,
          ease: "power2.out"
        });
      }
    });
  });
}
