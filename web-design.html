<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="src/style.css" />

    <!-- Sortable.js for drag and drop -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.0/Sortable.min.js"></script>

    <!-- font awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
      integrity="sha512-..."
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />

    <title>Serv5</title>

    <!-- Custom styles for drag and drop -->
    <style>
      /* Drag and drop specific styles */
      .sortable-ghost {
        opacity: 0.5;
        transform: rotate(2deg);
      }

      .sortable-chosen {
        box-shadow: 0 0 20px rgba(243, 156, 18, 0.5);
      }

      /* Drop zone drag over effect */
      #dropZone.drag-over {
        border-color: #10b981 !important;
        background-color: rgba(16, 185, 129, 0.1) !important;
        transform: scale(1.02);
      }

      /* Selected items styling */
      #selectedFeatures li {
        position: relative;
      }

      #selectedFeatures li:hover::before {
        content: "👆 انقر مرتين للحذف";
        position: absolute;
        top: -35px;
        right: 50%;
        transform: translateX(50%);
        background: #2c3e50;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 1000;
        pointer-events: none;
      }

      /* Cursor styles for draggable items */
      li[data-id]:active {
        cursor: grabbing !important;
      }

      /* Mobile responsive adjustments */
      @media (max-width: 768px) {
        #selectedFeatures li:hover::before {
          font-size: 10px;
          padding: 6px 8px;
          top: -30px;
        }

        #dropZone {
          min-height: 300px !important;
        }

        li[data-id] {
          padding: 12px 16px !important;
          font-size: 14px !important;
        }
      }

      /* Enhanced Design Yourself Section Styles */
      .design-yourself-section {
        min-height: 60vh;
        position: relative;
        overflow: hidden;
      }

      .design-images-container {
        perspective: 1000px;
      }

      .design-images-container img {
        transform-style: preserve-3d;
        transition: all 0.3s ease;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
      }

      .design-main-text {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        line-height: 1.2;
      }

      .design-cta-button {
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, #2458a7 0%, #1e4a8c 100%);
        border: none;
        box-shadow: 0 8px 25px rgba(36, 88, 167, 0.3);
      }

      .design-cta-button::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .design-cta-button:hover::before {
        left: 100%;
      }

      /* Responsive adjustments for design section */
      @media (max-width: 1024px) {
        .design-yourself-section {
          min-height: 50vh;
          padding: 2rem 0;
        }

        .design-floating-line {
          display: none !important;
        }
      }

      @media (max-width: 768px) {
        .design-yourself-section {
          min-height: auto;
          padding: 1.5rem 0;
        }

        .design-images-container {
          gap: 0.5rem;
          margin-bottom: 2rem;
        }

        .design-main-text {
          transform: skewY(-3deg) !important;
          font-size: 1.5rem !important;
          line-height: 1.3;
        }

        .design-floating-circle-right,
        .design-floating-circle-left {
          opacity: 0.1 !important;
        }
      }

      @media (max-width: 640px) {
        .design-main-text {
          font-size: 1.25rem !important;
        }

        .design-cta-button {
          padding: 0.75rem 1.5rem !important;
          font-size: 0.9rem;
        }
      }
    </style>
  </head>

  <body
    class="flex flex-col gap-4 md:gap-8 lg:gap-12 2xl:gap-16 overflow-x-hidden"
  >
    <!-- start of the header -->
    <header class="bg-main-blue relative">
      <!-- start of the  logo -->
      <div class="container flex justify-between h-[127px] items-center">
        <svg
          width="101"
          height="98"
          class="size-[98px] header-logo"
          viewBox="0 0 101 98"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <!-- Outer circle (first to animate) -->
          <path
            id="logo-outer-circle"
            d="M84.9157 80.1711C76.8332 87.5147 65.6703 92.0596 53.3487 92.0596C28.7056 92.0596 8.69727 73.8802 8.69727 51.4897C8.69727 29.0993 28.7056 10.9199 53.3487 10.9199"
            stroke="#DE8545"
            stroke-width="4.6875"
            stroke-linecap="round"
            fill="none"
          />
          <!-- Inner circle (second to animate) -->
          <path
            id="logo-inner-circle"
            d="M53.3486 10.9199C77.9917 10.9199 98 29.0993 98 51.4897C98 73.8802 77.9917 92.0596 53.3486 92.0596C48.5334 92.0596 43.8953 91.3655 39.5488 90.0816"
            stroke="#DE8545"
            stroke-width="4.6875"
            stroke-linecap="round"
            fill="none"
          />
          <!-- Base rectangle (third to animate) -->
          <path
            id="logo-base-rectangle"
            d="M52.2359 98.0003H11.1552C5.01991 98.0003 0 93.4395 0 87.865C0 82.2905 5.01991 77.7295 11.1552 77.7295H52.2359C58.3713 77.7295 63.3911 82.2905 63.3911 87.865C63.3911 93.4395 58.3713 98.0003 52.2359 98.0003Z"
            fill="#15205C"
            stroke="#DE8545"
            stroke-width="0"
          />
          <!-- Text/Icons (fourth to animate) -->
          <g id="logo-text-icons">
            <path
              class="logo-letter"
              d="M14.4084 85.3265C14.1143 84.3605 13.5939 83.2712 12.1689 83.2712C10.9021 83.2712 10.3027 84.0831 10.3027 84.9051C10.3027 85.9635 11.0831 86.4774 12.4629 87.0837C13.8994 87.7208 15.3923 88.4299 15.3923 90.0845C15.3923 91.6978 13.8767 92.9824 11.5694 92.9824C10.8681 92.9824 10.3027 92.8694 9.87296 92.7358C9.44313 92.6124 9.20561 92.4891 9.03603 92.4171C8.92287 92.1191 8.67414 90.7524 8.57227 90.0227L9.08116 89.8891C9.3639 90.8449 10.1896 92.4171 11.9202 92.4171C13.1529 92.4171 13.8768 91.7286 13.8768 90.6495C13.8768 89.5603 12.972 89.0465 11.6826 88.4299C10.5063 87.8956 8.88894 87.1454 8.88894 85.4908C8.88894 83.9905 10.2235 82.7471 12.429 82.7471C13.2321 82.7471 13.9898 82.9218 14.6232 83.1068C14.691 83.6411 14.7702 84.2577 14.9172 85.2442L14.4084 85.3265Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M25.1869 90.3099C25.0738 90.8751 24.7457 92.2625 24.5987 92.7454H16.2969V92.3138C17.8238 92.2111 17.9708 92.0672 17.9708 90.834V84.9456C17.9708 83.5685 17.8238 83.5275 16.5118 83.4247V82.9727H24.1802C24.2028 83.363 24.2706 84.3599 24.3386 85.2025L23.8184 85.2643C23.66 84.6373 23.4789 84.288 23.2641 83.9591C23.0152 83.6303 22.5515 83.5275 21.3866 83.5275H20.131C19.5768 83.5275 19.543 83.5685 19.543 84.0207V87.3913H21.0925C22.608 87.3913 22.7211 87.2682 22.936 86.2507H23.445V89.2001H22.936C22.6986 88.1004 22.5741 88.0183 21.0925 88.0183H19.543V90.7826C19.543 91.4917 19.6335 91.8308 19.9501 92.0158C20.2781 92.1905 20.9115 92.1905 21.6806 92.1905C22.9134 92.1905 23.4223 92.0877 23.8069 91.6972C24.101 91.3787 24.4176 90.8545 24.6665 90.2482L25.1869 90.3099Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M36.2622 92.879C36.0586 92.879 35.8664 92.8688 35.6628 92.8482C34.2942 92.7865 33.593 92.4577 32.8126 91.4712C32.2358 90.7415 31.6816 89.8269 31.1726 89.1076C30.8672 88.6759 30.5732 88.491 29.7022 88.491H29.2273V90.834C29.2273 92.0979 29.3856 92.2111 30.7428 92.3138V92.7454H26.1621V92.3138C27.5307 92.2111 27.689 92.0979 27.689 90.834V84.8737C27.689 83.5891 27.542 83.5069 26.23 83.4247V82.9727H30.5618C31.8399 82.9727 32.6656 83.1062 33.3102 83.4966C34.0001 83.8872 34.43 84.5552 34.43 85.4697C34.43 86.7748 33.5026 87.5558 32.2923 88.0183C32.5864 88.5115 33.2763 89.4981 33.7853 90.1866C34.396 90.9779 34.7579 91.3992 35.1538 91.7692C35.5836 92.2111 35.9568 92.3755 36.3414 92.4577L36.2622 92.879ZM29.985 87.9874C30.8333 87.9874 31.3987 87.864 31.8286 87.5558C32.4732 87.1037 32.7335 86.5179 32.7335 85.7061C32.7335 84.0926 31.5685 83.4966 30.3129 83.4966C29.804 83.4966 29.5326 83.5584 29.4082 83.6405C29.295 83.7228 29.2273 83.8974 29.2273 84.288V87.9874H29.985Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M47.2054 83.4247C46.0518 83.5377 45.8141 83.7021 45.1468 85.0792C44.683 86.0555 42.9187 89.7961 41.6066 92.9098H41.0298C39.8875 90.1558 38.4511 86.9188 37.5011 84.7812C36.992 83.6612 36.7093 83.5069 35.6914 83.435V82.9727H40.0007V83.4247C38.7678 83.5377 38.7566 83.6713 39.0167 84.3496C39.4804 85.4286 40.7472 88.3573 41.8216 90.7723H41.8441C42.7716 88.7377 43.9593 85.9937 44.4795 84.6065C44.8188 83.7227 44.7283 83.5685 43.3484 83.4247V82.9727H47.2054V83.4247Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M49.5919 87.3093C50.2818 87.0729 51.1075 86.8365 51.5485 86.8365C53.6974 86.8365 54.8173 88.1211 54.8173 89.3645C54.8173 90.2791 54.3309 91.1115 53.2791 91.8822C52.3629 92.5193 51.2206 92.9202 50.3157 92.9202C49.558 92.9202 48.8453 92.6221 48.574 92.3962C48.3251 92.1803 48.2346 92.057 48.2686 91.8616C48.2686 91.6973 48.4382 91.4609 48.6532 91.3068C48.8115 91.204 48.9472 91.204 49.1168 91.3274C49.49 91.6253 50.2026 92.1494 51.2545 92.1494C52.623 92.1494 53.2791 91.0807 53.2791 89.9914C53.2791 88.6555 52.3629 87.7306 50.5646 87.7306C49.8407 87.7306 49.1961 87.9155 48.8002 88.0286L49.4788 83.4248H54.3987L54.5005 83.5378L54.0368 84.6168H50.033L49.5919 87.3093Z"
              fill="#DE8545"
            />
            <path
              class="logo-icon"
              d="M55.4247 0H56.7964C56.8458 0.0246814 56.8921 0.0596832 56.945 0.0727212C59.5635 0.716344 60.7644 2.86669 59.7677 5.14651C59.2144 6.41208 58.6382 7.66938 58.0845 8.93474C57.8685 9.42846 57.6858 9.93419 57.4878 10.4344C57.407 10.5157 57.2931 10.5848 57.2509 10.6801C56.9786 11.2952 56.7216 11.9157 56.4603 12.5348C56.0419 13.5264 55.6241 14.5184 55.2061 15.5101C55.1242 15.6648 55.0325 15.8159 54.9619 15.9746C53.9279 18.2971 52.8966 20.6206 51.8647 22.9438C50.8327 25.2672 49.8065 27.5928 48.767 29.9134C48.0078 31.6082 46.1724 32.5722 44.3537 32.2499C41.7707 31.7922 40.3793 29.507 41.3334 27.2672C41.8675 26.0133 42.4286 24.7688 42.9671 23.544C42.8816 23.1901 42.8147 22.8671 42.7252 22.5494C42.2066 20.7099 41.4807 18.9575 40.1214 17.4737C39.5633 16.9424 39.0463 16.368 38.4381 15.8892C37.2331 14.9402 35.8555 14.2739 34.2565 14.0419C31.5012 13.5398 28.9524 13.9091 26.7367 15.5686C25.3568 16.6021 24.4801 17.8941 24.4541 19.5752C24.4129 19.7185 24.3246 19.8657 24.3389 20.0042C24.3947 20.5443 24.4363 21.0913 24.5686 21.618C24.8788 22.8525 25.4523 23.9791 26.391 24.934C26.5235 25.1351 26.629 25.3567 26.7927 25.5338C27.7984 26.6223 28.9601 27.5432 30.286 28.3095C31.9011 29.2429 33.4741 30.2363 35.0684 31.1997C36.1384 31.8463 37.2158 32.4827 38.2861 33.129C41.3919 35.004 44.5448 36.833 47.2642 39.168C48.9088 40.58 50.4845 42.0582 51.7042 43.8387C52.6133 45.1656 53.3738 46.5389 53.9229 48.0006C54.8725 50.5281 55.15 53.1352 54.8573 55.7992C54.4484 59.5217 53.0273 62.8719 50.2761 65.6639C44.3354 71.6926 36.7972 73.6354 28.1724 72.5071C26.3798 72.2726 24.6425 71.6955 22.8748 71.2943C22.1074 71.1202 21.3274 70.9917 20.6339 70.858C20.0577 71.2993 19.7734 71.7973 19.6408 72.4982C18.4475 74.6642 17.0417 75.3768 14.7209 74.9923C14.5521 74.919 14.3852 74.8417 14.2142 74.773C12.8143 74.2108 12.0251 73.218 11.8234 71.8615C11.7541 71.395 11.8539 70.9078 11.8764 70.4299C12.8938 68.1477 13.9111 65.8655 14.9285 63.5832C15.8547 61.5287 16.7885 59.477 17.7052 57.419C19.0576 54.3825 20.3931 51.3396 21.7453 48.303C22.3909 46.8529 23.5788 45.9991 25.3014 45.929C28.1445 45.8131 30.2687 48.261 29.1576 50.8921C27.7091 54.3216 26.1434 57.7101 24.6285 61.1165C24.3521 61.7382 24.0772 62.3605 23.7801 63.0314C24.2092 63.3795 24.5757 63.7598 25.0242 64.0258C26.6954 65.017 28.4817 65.7478 30.506 65.9144C34.0947 66.2098 37.2482 65.4815 39.5056 62.7212C41.5034 60.2783 41.8772 57.6559 40.4039 54.8734C39.5932 53.3425 38.4358 52.0404 37.1359 50.8381C34.6009 48.4935 31.6556 46.6094 28.7373 44.6965C25.5967 42.6377 22.4437 40.5943 19.316 38.5195C18.4452 37.9419 17.6447 37.2771 16.812 36.6522C16.2078 36.0812 15.5446 35.5518 15.0138 34.9293C14.2637 34.0495 13.6115 33.1009 12.9174 32.1817C12.9045 32.1137 12.9069 32.0394 12.8765 31.9786C11.8476 29.9147 11.4662 27.7315 11.4016 25.4855C11.3013 21.9913 12.0287 18.6743 14.0017 15.6403C16.244 12.1918 19.4768 9.77352 23.4851 8.1489C26.5217 6.91818 29.7003 6.28437 33.0012 6.13835C37.615 5.93425 42.0943 6.61149 46.4615 7.94561C47.4594 8.25054 48.4478 8.58132 49.4626 8.90727C50.3527 6.90236 51.28 4.95216 52.0829 2.9605C52.6895 1.45553 53.6803 0.399987 55.4247 0Z"
              fill="#2458A7"
            />
            <path
              class="logo-icon"
              d="M61.4293 17.3108H66.2065C68.3308 17.3197 70.4552 17.3357 72.5795 17.3357C74.7416 17.3356 76.9038 17.3196 79.0659 17.3105C80.1093 17.3105 81.1528 17.3105 82.2397 17.3105C82.4814 16.6209 82.7241 15.9357 82.9617 15.2491C83.6356 13.3025 85.8093 12.2428 87.9217 12.8328C90.0475 13.4266 91.2142 15.4589 90.548 17.4087C89.7385 19.778 88.9285 22.1472 88.1187 24.5164C87.5134 26.2267 86.908 27.937 86.3031 29.6473C86.2169 29.8911 86.1333 30.1356 86.0485 30.3797C86.0028 30.4334 85.9527 30.4845 85.9121 30.5412C84.7456 32.1723 82.6243 32.7534 80.7813 31.9456C78.9182 31.1292 78.0841 29.2351 78.762 27.384C79.0378 26.6307 79.2925 25.8709 79.5526 25.1128C79.6461 24.8404 79.7269 24.5645 79.8383 24.2116H66.8508C65.473 27.6652 64.0931 31.1242 62.6844 34.6553C62.9697 34.6116 63.1348 34.5891 63.2987 34.5608C67.7437 33.7909 72.1577 33.8927 76.5196 35.0112C82.5933 36.5687 87.6532 39.4903 91.2743 44.269C92.121 45.3864 92.8161 46.5986 93.581 47.7673C95.0354 50.8007 96.0776 53.9422 95.9567 57.2763C95.6538 65.6337 90.9819 72.7107 83.0579 77.1835C82.8326 77.3107 83.2826 77.0557 83.0579 77.1835C83.0075 77.2147 83.1082 77.1523 83.0579 77.1835C83.0179 77.2229 83.0979 77.1443 83.0579 77.1835C82.7679 77.285 83.3335 77.0565 83.0579 77.1835C79.4099 78.8651 74.6148 80.1694 70.5251 80.0128C62.0075 79.6865 55.0993 76.5363 50.0989 70.157C50.0774 70.1297 50.0675 70.0948 50.0293 70.0163C51.519 68.5236 53.0239 67.0157 54.5414 65.4952C55.1319 65.5267 55.7173 65.5579 56.1404 65.5805C57.2816 66.7397 58.2374 67.901 59.3971 68.8584C69.2744 77.0136 83.4219 72.2073 87.0103 62.1986C90.2255 53.2312 83.6993 43.3419 73.3653 41.5232C69.705 40.879 66.0924 41.1678 62.5053 41.92C60.9152 42.2534 59.353 42.6967 57.7781 43.0898C57.5969 43.1351 57.4151 43.1785 57.2096 43.2286C55.6099 41.9478 54.0152 40.6708 52.4036 39.3805C53.1658 37.4712 53.9153 35.5908 54.667 33.7109C56.4433 29.2687 58.2394 24.833 59.9756 20.378"
              fill="#DE8545"
            />
          </g>

          <!-- Gradient definition for shine effect -->
          <defs>
            <linearGradient
              id="shineGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              <stop offset="0%" style="stop-color: white; stop-opacity: 0" />
              <stop offset="50%" style="stop-color: white; stop-opacity: 0.2" />
              <stop offset="100%" style="stop-color: white; stop-opacity: 0" />
            </linearGradient>
          </defs>

          <!-- Shine effect overlay (hidden initially) -->
          <rect
            id="logo-shine"
            x="-20"
            y="0"
            width="20"
            height="98"
            fill="url(#shineGradient)"
            opacity="0"
          />
        </svg>

        <!-- end of the  logo -->

        <!-- links -->
        <ul class="hidden lg:flex items-center gap-10 text-white font-light">
          <!-- main -->
          <li
            class="nav-item active text-primary-yellow flex gap-1.5 items-center relative font-semibold transition-all duration-300"
          >
            <a href="/" class="flex gap-2 items-center">
              <i class="fa-solid fa-house"></i>
              <span data-nav="home">الرئيسية</span>
            </a>
            <!-- floating arrow -->
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[60px] h-[20px] absolute -bottom-5 left-1/2 animate-scale-x opacity-100"
            />
          </li>
          <!--نبذة عنا  -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-5"></i>
              <span data-nav="about">نبذة عنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- خدماتنا   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-plus"></i>
              <span data-nav="services">خدماتنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- أعمالــنا   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-briefcase"></i>
              <span data-nav="portfolio">أعمالــنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- المدونــة   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-regular fa-rectangle-list"></i>
              <span data-nav="blog">المدونــة</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- الكتيب التعريفي   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-file-pdf"></i>
              <span data-nav="brochure">الكتيب التعريفي</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
        </ul>
        <!-- Desktop Navigation -->
        <div class="hidden lg:flex gap-2 items-center">
          <!-- button to switch lang  -->
          <button
            id="langToggle"
            class="rounded-md text-primary-blue font-bold bg-white p-4 hover:bg-gray-100 transition-all duration-300 header-button"
          >
            EN
          </button>
          <!-- company profile button  -->
          <button
            class="rounded-md flex items-center gap-2 text-white border border-white font-bold bg-transparent p-4 hover:bg-white hover:text-primary-blue transition-all duration-300 header-button header-profile-btn"
          >
            <i class="fa-solid fa-book"></i>
            <span data-nav="profile">بروفايل الشركة</span>
          </button>

          <!-- our vision -->
          <img
            src="/public/shared/header/our-vision.webp"
            alt="Serv5 future vision for 2030"
            class="w-[125px] h-[84px] ms-8 header-vision"
          />
        </div>

        <!-- Mobile Navigation -->
        <div class="lg:hidden flex items-center gap-4">
          <!-- Mobile Language Toggle -->
          <button
            id="mobileLangToggle"
            class="rounded-md text-primary-blue font-bold bg-white p-3 hover:bg-gray-100 transition-all duration-300 text-sm"
          >
            EN
          </button>

          <!-- Burger Menu Button -->
          <button
            id="burgerMenuBtn"
            class="relative w-8 h-8 flex flex-col justify-center items-center space-y-1 focus:outline-none burger-menu-btn"
            aria-label="Toggle mobile menu"
          >
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
          </button>
        </div>
      </div>

      <!-- Mobile Menu Overlay -->
      <div
        id="mobileMenuOverlay"
        class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden opacity-0 pointer-events-none transition-opacity duration-300"
      ></div>

      <!-- Mobile Menu -->
      <div
        id="mobileMenu"
        class="fixed top-0 w-80 h-full bg-main-blue z-50 lg:hidden transform transition-transform duration-300 ease-in-out mobile-menu"
      >
        <!-- Mobile Menu Header -->
        <div
          class="flex items-center justify-between p-6 border-b border-gray-700"
        >
          <div class="flex items-center gap-3">
            <svg
              width="101"
              height="98"
              class="size-[98px] header-logo"
              viewBox="0 0 101 98"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <!-- Outer circle (first to animate) -->
              <path
                id="logo-outer-circle"
                d="M84.9157 80.1711C76.8332 87.5147 65.6703 92.0596 53.3487 92.0596C28.7056 92.0596 8.69727 73.8802 8.69727 51.4897C8.69727 29.0993 28.7056 10.9199 53.3487 10.9199"
                stroke="#DE8545"
                stroke-width="4.6875"
                stroke-linecap="round"
                fill="none"
              />
              <!-- Inner circle (second to animate) -->
              <path
                id="logo-inner-circle"
                d="M53.3486 10.9199C77.9917 10.9199 98 29.0993 98 51.4897C98 73.8802 77.9917 92.0596 53.3486 92.0596C48.5334 92.0596 43.8953 91.3655 39.5488 90.0816"
                stroke="#DE8545"
                stroke-width="4.6875"
                stroke-linecap="round"
                fill="none"
              />
              <!-- Base rectangle (third to animate) -->
              <path
                id="logo-base-rectangle"
                d="M52.2359 98.0003H11.1552C5.01991 98.0003 0 93.4395 0 87.865C0 82.2905 5.01991 77.7295 11.1552 77.7295H52.2359C58.3713 77.7295 63.3911 82.2905 63.3911 87.865C63.3911 93.4395 58.3713 98.0003 52.2359 98.0003Z"
                fill="#15205C"
                stroke="#DE8545"
                stroke-width="0"
              />
              <!-- Text/Icons (fourth to animate) -->
              <g id="logo-text-icons">
                <path
                  class="logo-letter"
                  d="M14.4084 85.3265C14.1143 84.3605 13.5939 83.2712 12.1689 83.2712C10.9021 83.2712 10.3027 84.0831 10.3027 84.9051C10.3027 85.9635 11.0831 86.4774 12.4629 87.0837C13.8994 87.7208 15.3923 88.4299 15.3923 90.0845C15.3923 91.6978 13.8767 92.9824 11.5694 92.9824C10.8681 92.9824 10.3027 92.8694 9.87296 92.7358C9.44313 92.6124 9.20561 92.4891 9.03603 92.4171C8.92287 92.1191 8.67414 90.7524 8.57227 90.0227L9.08116 89.8891C9.3639 90.8449 10.1896 92.4171 11.9202 92.4171C13.1529 92.4171 13.8768 91.7286 13.8768 90.6495C13.8768 89.5603 12.972 89.0465 11.6826 88.4299C10.5063 87.8956 8.88894 87.1454 8.88894 85.4908C8.88894 83.9905 10.2235 82.7471 12.429 82.7471C13.2321 82.7471 13.9898 82.9218 14.6232 83.1068C14.691 83.6411 14.7702 84.2577 14.9172 85.2442L14.4084 85.3265Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M25.1869 90.3099C25.0738 90.8751 24.7457 92.2625 24.5987 92.7454H16.2969V92.3138C17.8238 92.2111 17.9708 92.0672 17.9708 90.834V84.9456C17.9708 83.5685 17.8238 83.5275 16.5118 83.4247V82.9727H24.1802C24.2028 83.363 24.2706 84.3599 24.3386 85.2025L23.8184 85.2643C23.66 84.6373 23.4789 84.288 23.2641 83.9591C23.0152 83.6303 22.5515 83.5275 21.3866 83.5275H20.131C19.5768 83.5275 19.543 83.5685 19.543 84.0207V87.3913H21.0925C22.608 87.3913 22.7211 87.2682 22.936 86.2507H23.445V89.2001H22.936C22.6986 88.1004 22.5741 88.0183 21.0925 88.0183H19.543V90.7826C19.543 91.4917 19.6335 91.8308 19.9501 92.0158C20.2781 92.1905 20.9115 92.1905 21.6806 92.1905C22.9134 92.1905 23.4223 92.0877 23.8069 91.6972C24.101 91.3787 24.4176 90.8545 24.6665 90.2482L25.1869 90.3099Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M36.2622 92.879C36.0586 92.879 35.8664 92.8688 35.6628 92.8482C34.2942 92.7865 33.593 92.4577 32.8126 91.4712C32.2358 90.7415 31.6816 89.8269 31.1726 89.1076C30.8672 88.6759 30.5732 88.491 29.7022 88.491H29.2273V90.834C29.2273 92.0979 29.3856 92.2111 30.7428 92.3138V92.7454H26.1621V92.3138C27.5307 92.2111 27.689 92.0979 27.689 90.834V84.8737C27.689 83.5891 27.542 83.5069 26.23 83.4247V82.9727H30.5618C31.8399 82.9727 32.6656 83.1062 33.3102 83.4966C34.0001 83.8872 34.43 84.5552 34.43 85.4697C34.43 86.7748 33.5026 87.5558 32.2923 88.0183C32.5864 88.5115 33.2763 89.4981 33.7853 90.1866C34.396 90.9779 34.7579 91.3992 35.1538 91.7692C35.5836 92.2111 35.9568 92.3755 36.3414 92.4577L36.2622 92.879ZM29.985 87.9874C30.8333 87.9874 31.3987 87.864 31.8286 87.5558C32.4732 87.1037 32.7335 86.5179 32.7335 85.7061C32.7335 84.0926 31.5685 83.4966 30.3129 83.4966C29.804 83.4966 29.5326 83.5584 29.4082 83.6405C29.295 83.7228 29.2273 83.8974 29.2273 84.288V87.9874H29.985Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M47.2054 83.4247C46.0518 83.5377 45.8141 83.7021 45.1468 85.0792C44.683 86.0555 42.9187 89.7961 41.6066 92.9098H41.0298C39.8875 90.1558 38.4511 86.9188 37.5011 84.7812C36.992 83.6612 36.7093 83.5069 35.6914 83.435V82.9727H40.0007V83.4247C38.7678 83.5377 38.7566 83.6713 39.0167 84.3496C39.4804 85.4286 40.7472 88.3573 41.8216 90.7723H41.8441C42.7716 88.7377 43.9593 85.9937 44.4795 84.6065C44.8188 83.7227 44.7283 83.5685 43.3484 83.4247V82.9727H47.2054V83.4247Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M49.5919 87.3093C50.2818 87.0729 51.1075 86.8365 51.5485 86.8365C53.6974 86.8365 54.8173 88.1211 54.8173 89.3645C54.8173 90.2791 54.3309 91.1115 53.2791 91.8822C52.3629 92.5193 51.2206 92.9202 50.3157 92.9202C49.558 92.9202 48.8453 92.6221 48.574 92.3962C48.3251 92.1803 48.2346 92.057 48.2686 91.8616C48.2686 91.6973 48.4382 91.4609 48.6532 91.3068C48.8115 91.204 48.9472 91.204 49.1168 91.3274C49.49 91.6253 50.2026 92.1494 51.2545 92.1494C52.623 92.1494 53.2791 91.0807 53.2791 89.9914C53.2791 88.6555 52.3629 87.7306 50.5646 87.7306C49.8407 87.7306 49.1961 87.9155 48.8002 88.0286L49.4788 83.4248H54.3987L54.5005 83.5378L54.0368 84.6168H50.033L49.5919 87.3093Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-icon"
                  d="M55.4247 0H56.7964C56.8458 0.0246814 56.8921 0.0596832 56.945 0.0727212C59.5635 0.716344 60.7644 2.86669 59.7677 5.14651C59.2144 6.41208 58.6382 7.66938 58.0845 8.93474C57.8685 9.42846 57.6858 9.93419 57.4878 10.4344C57.407 10.5157 57.2931 10.5848 57.2509 10.6801C56.9786 11.2952 56.7216 11.9157 56.4603 12.5348C56.0419 13.5264 55.6241 14.5184 55.2061 15.5101C55.1242 15.6648 55.0325 15.8159 54.9619 15.9746C53.9279 18.2971 52.8966 20.6206 51.8647 22.9438C50.8327 25.2672 49.8065 27.5928 48.767 29.9134C48.0078 31.6082 46.1724 32.5722 44.3537 32.2499C41.7707 31.7922 40.3793 29.507 41.3334 27.2672C41.8675 26.0133 42.4286 24.7688 42.9671 23.544C42.8816 23.1901 42.8147 22.8671 42.7252 22.5494C42.2066 20.7099 41.4807 18.9575 40.1214 17.4737C39.5633 16.9424 39.0463 16.368 38.4381 15.8892C37.2331 14.9402 35.8555 14.2739 34.2565 14.0419C31.5012 13.5398 28.9524 13.9091 26.7367 15.5686C25.3568 16.6021 24.4801 17.8941 24.4541 19.5752C24.4129 19.7185 24.3246 19.8657 24.3389 20.0042C24.3947 20.5443 24.4363 21.0913 24.5686 21.618C24.8788 22.8525 25.4523 23.9791 26.391 24.934C26.5235 25.1351 26.629 25.3567 26.7927 25.5338C27.7984 26.6223 28.9601 27.5432 30.286 28.3095C31.9011 29.2429 33.4741 30.2363 35.0684 31.1997C36.1384 31.8463 37.2158 32.4827 38.2861 33.129C41.3919 35.004 44.5448 36.833 47.2642 39.168C48.9088 40.58 50.4845 42.0582 51.7042 43.8387C52.6133 45.1656 53.3738 46.5389 53.9229 48.0006C54.8725 50.5281 55.15 53.1352 54.8573 55.7992C54.4484 59.5217 53.0273 62.8719 50.2761 65.6639C44.3354 71.6926 36.7972 73.6354 28.1724 72.5071C26.3798 72.2726 24.6425 71.6955 22.8748 71.2943C22.1074 71.1202 21.3274 70.9917 20.6339 70.858C20.0577 71.2993 19.7734 71.7973 19.6408 72.4982C18.4475 74.6642 17.0417 75.3768 14.7209 74.9923C14.5521 74.919 14.3852 74.8417 14.2142 74.773C12.8143 74.2108 12.0251 73.218 11.8234 71.8615C11.7541 71.395 11.8539 70.9078 11.8764 70.4299C12.8938 68.1477 13.9111 65.8655 14.9285 63.5832C15.8547 61.5287 16.7885 59.477 17.7052 57.419C19.0576 54.3825 20.3931 51.3396 21.7453 48.303C22.3909 46.8529 23.5788 45.9991 25.3014 45.929C28.1445 45.8131 30.2687 48.261 29.1576 50.8921C27.7091 54.3216 26.1434 57.7101 24.6285 61.1165C24.3521 61.7382 24.0772 62.3605 23.7801 63.0314C24.2092 63.3795 24.5757 63.7598 25.0242 64.0258C26.6954 65.017 28.4817 65.7478 30.506 65.9144C34.0947 66.2098 37.2482 65.4815 39.5056 62.7212C41.5034 60.2783 41.8772 57.6559 40.4039 54.8734C39.5932 53.3425 38.4358 52.0404 37.1359 50.8381C34.6009 48.4935 31.6556 46.6094 28.7373 44.6965C25.5967 42.6377 22.4437 40.5943 19.316 38.5195C18.4452 37.9419 17.6447 37.2771 16.812 36.6522C16.2078 36.0812 15.5446 35.5518 15.0138 34.9293C14.2637 34.0495 13.6115 33.1009 12.9174 32.1817C12.9045 32.1137 12.9069 32.0394 12.8765 31.9786C11.8476 29.9147 11.4662 27.7315 11.4016 25.4855C11.3013 21.9913 12.0287 18.6743 14.0017 15.6403C16.244 12.1918 19.4768 9.77352 23.4851 8.1489C26.5217 6.91818 29.7003 6.28437 33.0012 6.13835C37.615 5.93425 42.0943 6.61149 46.4615 7.94561C47.4594 8.25054 48.4478 8.58132 49.4626 8.90727C50.3527 6.90236 51.28 4.95216 52.0829 2.9605C52.6895 1.45553 53.6803 0.399987 55.4247 0Z"
                  fill="#2458A7"
                />
                <path
                  class="logo-icon"
                  d="M61.4293 17.3108H66.2065C68.3308 17.3197 70.4552 17.3357 72.5795 17.3357C74.7416 17.3356 76.9038 17.3196 79.0659 17.3105C80.1093 17.3105 81.1528 17.3105 82.2397 17.3105C82.4814 16.6209 82.7241 15.9357 82.9617 15.2491C83.6356 13.3025 85.8093 12.2428 87.9217 12.8328C90.0475 13.4266 91.2142 15.4589 90.548 17.4087C89.7385 19.778 88.9285 22.1472 88.1187 24.5164C87.5134 26.2267 86.908 27.937 86.3031 29.6473C86.2169 29.8911 86.1333 30.1356 86.0485 30.3797C86.0028 30.4334 85.9527 30.4845 85.9121 30.5412C84.7456 32.1723 82.6243 32.7534 80.7813 31.9456C78.9182 31.1292 78.0841 29.2351 78.762 27.384C79.0378 26.6307 79.2925 25.8709 79.5526 25.1128C79.6461 24.8404 79.7269 24.5645 79.8383 24.2116H66.8508C65.473 27.6652 64.0931 31.1242 62.6844 34.6553C62.9697 34.6116 63.1348 34.5891 63.2987 34.5608C67.7437 33.7909 72.1577 33.8927 76.5196 35.0112C82.5933 36.5687 87.6532 39.4903 91.2743 44.269C92.121 45.3864 92.8161 46.5986 93.581 47.7673C95.0354 50.8007 96.0776 53.9422 95.9567 57.2763C95.6538 65.6337 90.9819 72.7107 83.0579 77.1835C82.8326 77.3107 83.2826 77.0557 83.0579 77.1835C83.0075 77.2147 83.1082 77.1523 83.0579 77.1835C83.0179 77.2229 83.0979 77.1443 83.0579 77.1835C82.7679 77.285 83.3335 77.0565 83.0579 77.1835C79.4099 78.8651 74.6148 80.1694 70.5251 80.0128C62.0075 79.6865 55.0993 76.5363 50.0989 70.157C50.0774 70.1297 50.0675 70.0948 50.0293 70.0163C51.519 68.5236 53.0239 67.0157 54.5414 65.4952C55.1319 65.5267 55.7173 65.5579 56.1404 65.5805C57.2816 66.7397 58.2374 67.901 59.3971 68.8584C69.2744 77.0136 83.4219 72.2073 87.0103 62.1986C90.2255 53.2312 83.6993 43.3419 73.3653 41.5232C69.705 40.879 66.0924 41.1678 62.5053 41.92C60.9152 42.2534 59.353 42.6967 57.7781 43.0898C57.5969 43.1351 57.4151 43.1785 57.2096 43.2286C55.6099 41.9478 54.0152 40.6708 52.4036 39.3805C53.1658 37.4712 53.9153 35.5908 54.667 33.7109C56.4433 29.2687 58.2394 24.833 59.9756 20.378"
                  fill="#DE8545"
                />
              </g>

              <!-- Gradient definition for shine effect -->
              <defs>
                <linearGradient
                  id="shineGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="0%"
                >
                  <stop
                    offset="0%"
                    style="stop-color: white; stop-opacity: 0"
                  />
                  <stop
                    offset="50%"
                    style="stop-color: white; stop-opacity: 0.2"
                  />
                  <stop
                    offset="100%"
                    style="stop-color: white; stop-opacity: 0"
                  />
                </linearGradient>
              </defs>

              <!-- Shine effect overlay (hidden initially) -->
              <rect
                id="logo-shine"
                x="-20"
                y="0"
                width="20"
                height="98"
                fill="url(#shineGradient)"
                opacity="0"
              />
            </svg>
            <span class="text-white font-bold text-lg">Serv5</span>
          </div>
          <button
            id="closeMobileMenu"
            class="text-white hover:text-primary-yellow transition-colors duration-300 p-2"
            aria-label="Close mobile menu"
          >
            <i class="fa-solid fa-times text-xl"></i>
          </button>
        </div>

        <!-- Mobile Menu Navigation -->
        <nav class="p-6">
          <ul class="space-y-4 mobile-nav-items">
            <!-- Home -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-house text-lg"></i>
                <span data-nav="home" class="font-medium">الرئيسية</span>
              </a>
            </li>
            <!-- About -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-5 text-lg"></i>
                <span data-nav="about" class="font-medium">نبذة عنا</span>
              </a>
            </li>
            <!-- Services -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-plus text-lg"></i>
                <span data-nav="services" class="font-medium">خدماتنا</span>
              </a>
            </li>
            <!-- Portfolio -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-briefcase text-lg"></i>
                <span data-nav="portfolio" class="font-medium">أعمالــنا</span>
              </a>
            </li>
            <!-- Blog -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-regular fa-rectangle-list text-lg"></i>
                <span data-nav="blog" class="font-medium">المدونــة</span>
              </a>
            </li>
            <!-- Brochure -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-file-pdf text-lg"></i>
                <span data-nav="brochure" class="font-medium"
                  >الكتيب التعريفي</span
                >
              </a>
            </li>
          </ul>

          <!-- Mobile Menu Actions -->
          <div class="mt-8 space-y-4 mobile-menu-actions">
            <button
              class="w-full rounded-md flex items-center justify-center gap-2 text-white border border-white font-bold bg-transparent p-4 hover:bg-white hover:text-primary-blue transition-all duration-300"
            >
              <i class="fa-solid fa-book"></i>
              <span data-nav="profile">بروفايل الشركة</span>
            </button>

            <!-- Mobile Vision Image -->
            <div class="flex justify-center mt-6">
              <img
                src="/public/shared/header/our-vision.webp"
                alt="Serv5 future vision for 2030"
                class="w-[100px] h-[67px]"
              />
            </div>
          </div>
        </nav>
      </div>
    </header>
    <!-- end of the header -->

    <!-- start of breadcrumbs -->
    <section
      class="breadcrumbs-section bottom-banner bg-light-blue lg:h-[251px] rounded-md container py-10 relative overflow-hidden"
    >
      <!-- flying icons start -->
      <div
        class="absolute flex top-1/2 -translate-y-1/2 left-1/2 lg:start-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="/public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="/public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-20 lg:end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-5 lg:bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>
        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute hidden lg:inline top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="/public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>
      <!-- flying icons end -->
      <div
        class="absolute top-1/2 -translate-y-1/2 hidden lg:flex end-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="/public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="/public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>

        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="/public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>

      <div class="flex flex-col">
        <!-- start of navigations -->
        <ul class="flex gap-2 text-white self-start text-sm items-center">
          <li>
            <a href="#" class="hover:text-primary-yellow cursor-pointer"
              >الرئيسية</a
            >
          </li>
          <li>
            <img
              src="/public/shared/cross-pages/serv5-small-logo.webp"
              alt="Serv5 logo"
              class="size-[12px]"
            />
          </li>
          <li>
            <a href="#" class="hover:text-primary-yellow cursor-pointer"
              >أعمالــنا</a
            >
          </li>
        </ul>
        <!-- end of navigations -->
        <p class="banner-text text-white font-bold mt-10 text-2xl lg:text-4xl">
          أعمالنا
        </p>
      </div>
    </section>
    <!-- end of breadcrumbs -->

    <!-- start of sub header -->
    <section
      class="sub-header-section flex flex-col lg:flex-row gap-5 items-center justify-between container px-8 py-6 rounded-md bg-secondary-blue text-white"
    >
      <p class="font-semibold">تصميم مواقع</p>

      <ul
        class="flex items-center flex-wrap justify-center gap-y-2 gap-x-4 xl:gap-[50px]"
      >
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#"> نظرة عامة </a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">المميزات</a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">أحدث أعمالنا</a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">حدد موقعك بنفسك</a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">أعمال متعلقة بالخدمة</a>
        </li>
        <li class="cursor-pointer hover:text-primary-yellow">
          <a href="#">مقالات مشابهة</a>
        </li>
      </ul>

      <button
        class="bg-white rounded-md text-light-blue font-bold p-3 flex justify-between items-center gap-1 hover:text-white hover:bg-light-blue transition-all"
      >
        <i class="fa-solid fa-arrow-right"></i>
        طلب الخدمة
      </button>
    </section>
    <!-- end of sub header -->

    <!-- start of best company for desgning websites section -->
    <div class="shadow-xl">
      <section class="best-company-section container">
        <div class="text-center mb-16">
          <h2
            class="section-title text-primary-blue font-bold text-3xl md:text-5xl relative w-fit mx-auto"
          >
            <span
              class="absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
            ></span>
            أفضل شركة تصميم مواقع
          </h2>
          <p class="section-subtitle text-gray mt-5">
            قم بكتابة بياناتك و سيتم الرد عليك خلال 24 ساعة كحد أقصي
          </p>
        </div>
        <!-- main container -->
        <div class="flex flex-col lg:flex-row lg:gap-20">
          <!-- images -->
          <div class="flex-1 hidden xl:block">
            <img
              src="/public/pages/web-design/man-drawing.webp"
              alt="Man Drawing"
              class="w-full h-[100px] lg:h-[181px] rounded-xl overflow-hidden object-cover"
            />
            <img
              src="/public/pages/web-design/man-looking-at-designes.webp"
              alt="Man Drawing"
              class="w-[80%] h-[250px] lg:h-[328px] my-2 rounded-xl overflow-hidden object-cover"
            />
            <div class="-translate-y-20">
              <img
                src="/public/pages/web-design/working-on-laptop.webp"
                alt="Man working on laptop-screen"
                class="w-[329px] h-[180px] lg:h-[281px] object-cover rounded-xl overflow-hidden border-8 border-white -translate-y-20 block ms-auto"
              />
            </div>
          </div>
          <!-- content -->
          <div
            class="flex-1 flex flex-col gap-6 lg:gap-12 justify-center bg-section-gray p-4 lg:p-14"
          >
            <h2
              class="text-primary-blue font-bold text-xl sm:text-3xl md:text-5xl"
            >
              صمم موقعك بشكل إحترافي
            </h2>

            <p class="text-gray leading-7">
              معنا سوف تضاعف نسبة أرباحك؛ كل ما عليك فعله هو الاتصال بنا وطلب
              خدمة تصميم متجر الكتروني.. ان فكرة امتلاك متجر الكتروني تعد من
              الأفكار السديدة التي من خلالها سوف تجني أضعاف الأرباح التى من
              الممكن الحصول عليها من خلال المتجر التقليدي؛ وذلك لما يتسم به
              المتجر الالكتروني من مرونة وشمولية الجانب الجغرافي فمن خصائصه
              التوسع والعرض على قاعدة جماهيرية لا تتقيد بالظروف المكانية او
              المناخية ولا بالحدود الدولية مما يتيح لك عدد عملاء أكبر وبالتالي
              تحقيق مبيعات أكبر؛ ومضاعفة الأرباح.
            </p>
            <!-- seprator -->
            <div class="w-full h-[1px] bg-gray"></div>

            <!-- features -->
            <div class="flex flex-wrap gap-x-10 gap-y-5">
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
              <!-- first feature -->
              <div class="flex gap-3 items-center">
                <!-- svg -->
                <svg
                  width="29"
                  height="32"
                  viewBox="0 0 29 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    fill="white"
                  />
                  <path
                    d="M25.1366 21.7275L14.3639 27.5912L3.59118 21.7275L0.727539 23.5003L14.3639 31.0003L28.0003 23.5003L25.1366 21.7275Z"
                    fill="#CCE7FF"
                  />
                  <path
                    d="M28.0003 23.5L14.3639 31L0.727539 23.5V8.5L14.3639 1L28.0003 8.5V23.5Z"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M6.86328 16.0004L12.9996 21.455L21.8633 10.5459"
                    stroke="#1078FF"
                    stroke-miterlimit="10"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <!-- the text -->
                <p class="text-secondary-blue font-medium">متوافق مع الهاتف</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
    <!-- end of best company for desgning websites section -->

    <!-- start of many features section -->
    <section
      class="features-section container flex flex-col items-center justify-center lg:flex-row gap-5 lg:gap-10 mt-5"
    >
      <!-- laptop model -->
      <div class="basis-[30%]">
        <!-- section header -->
        <h2
          class="features-title text-primary-blue font-bold text-3xl md:text-5xl relative"
        >
          <span
            class="absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
          ></span>
          مميزات متعددة
        </h2>
        <div id="lottie-animation" class=""></div>
      </div>
      <!-- the actual features -->
      <div
        class="w-full py-5 lg:py-10 md:py-[102px] bg-cover bg-center bg-no-repeat px-4 flex-1"
      >
        <div class="space-y-5">
          <ul class="grid md:grid-cols-2 gap-4">
            <!-- first feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">دعم فني متواصل</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- second feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="public/pages/home-page/serv5-features/key-settings.webp"
                  alt="Key Settings"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">إعدادات متقدمة</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- third feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="public/pages/home-page/serv5-features/search-web.webp"
                  alt="Search Web"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">بحث متقدم</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- fourth feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="public/pages/home-page/serv5-features/target.webp"
                  alt="Target"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">استهداف دقيق</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- fifth feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">تحليل البيانات</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
            <!-- sixth feature -->
            <li
              class="feature-item p-4 group flex items-center gap-2 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white"
              >
                <img
                  src="/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-10"
                />
              </div>
              <p class="transition-all duration-300">دعم فني متواصل</p>
              <div class="feature-hand absolute end-0 inset-y-0 w-[151px]">
                <img
                  src="/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                />
              </div>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <!-- end of many features section -->

    <!-- start of our newest works -->
    <section class="newest-works-section container">
      <div class="text-center mb-16">
        <h2
          class="section-title text-primary-blue font-bold text-3xl md:text-5xl relative w-fit mx-auto"
        >
          <span
            class="absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
          ></span>
          أحدث أعمالنا في تصميم المواقع
        </h2>
        <p class="section-subtitle text-gray mt-5">
          قم بكتابة بياناتك و سيتم الرد عليك خلال 24 ساعة كحد أقصي
        </p>
      </div>

      <!-- tabs -->
      <div class="flex flex-wrap items-center justify-center gap-6 mb-12">
        <button
          class="work-tab hover:text-white cursor-pointer roumded-md hover:bg-secondary-blue bg-white text-secondary-blue outline outline-secondary-blue transition-all py-2 px-4 sm:py-4 sm:px-7 rounded-md font-semibold text-sm"
          data-category="featured"
        >
          أعمال مميزة
        </button>
        <button
          class="work-tab hover:text-white cursor-pointer roumded-md hover:bg-secondary-blue bg-white text-secondary-blue outline outline-secondary-blue transition-all py-2 px-4 sm:py-4 sm:px-7 rounded-md font-semibold text-sm"
          data-category="motion"
        >
          موشن جرافيك
        </button>
        <button
          class="work-tab hover:text-white cursor-pointer roumded-md hover:bg-secondary-blue bg-white text-secondary-blue outline outline-secondary-blue transition-all py-2 px-4 sm:py-4 sm:px-7 rounded-md font-semibold text-sm"
          data-category="websites"
        >
          تصميم مواقع
        </button>
        <button
          class="work-tab hover:text-white cursor-pointer roumded-md hover:bg-secondary-blue bg-white text-secondary-blue outline outline-secondary-blue transition-all py-2 px-4 sm:py-4 sm:px-7 rounded-md font-semibold text-sm"
          data-category="ecommerce"
        >
          المتاجر الإلكترونية
        </button>
      </div>

      <!-- content -->
      <div class="flex items-center justify-between">
        <!-- the image -->
        <div class="flex-1 hidden xl:block">
          <img
            src="/public/pages/web-design/capital-tour.webp"
            alt="Capital Tour page"
            class="w-full h-[585px]"
          />
        </div>
        <!-- the content -->
        <div
          class="xl:basis-[60%] translate-x-10 flex justify-between rounded-xl bg-white shadow-xl flex-col px-3 lg:px-12 py-4 lg:py-14 gap-4"
        >
          <h3 class="font-semibold text-2xl">موقع سياحي</h3>
          <p class="leading-7 text-gray">
            تصميم و برمجة متجر الكتروني امر مربح للغاية , حيث يعتبر اليوم المتجر
            الالكتروني من اكثر النشاطات الشائعة والتي يجني من خلالها العديد من
            الافراد والمؤسسات التجارية الكثير من الاموال؛ برغم قلة تكلفة تصميم
            متجر الكترونية مقارنة بالمتاجر التقليدية الا انه يعود بعائد مادي
            مضاعف وذلك لأنه يعرض على شبكة الانترنت والتي تعد بمثابة نافذة تطل
            على كافة انحاء العالم مما يحرر متجرك من القيود المكانية التي قد تجعل
            منه مقيد بعدد عملاء محدد وفقاً لمنطقة جغرافية محدودة ، وكلما كان
            تصميم المتجر مميز وسهل الاستخدام كلما زاد التفاعل عليه وزادت نسب
            الأرباح.
          </p>

          <div class="hero-buttons flex gap-2.5 items-center">
            <button
              class="rounded-md text-white font-bold bg-primary-blue p-4 hover:bg-white hover:text-primary-blue text-sm transition-all duration-300 hover:outline-primary-blue outline"
            >
              <i class="fa-solid fa-arrow-right me-2"></i>
              طلب عرض سعر
            </button>
            <button
              class="rounded-md bg-white font-bold text-primary-blue p-4 hover:text-white hover:bg-primary-blue outline outline-primary-blue text-sm transition-all duration-300"
            >
              <i class="fa-solid fa-arrow-right me-2"></i>
              عرض أعمالنا
            </button>
          </div>
        </div>
      </div>
    </section>
    <!-- end of our newest works -->

    <!-- start of choose feautres section -->
    <section
      class="choose-features-section container grid grid-cols-1 md:grid-cols-3 gap-8 items-start"
    >
      <!-- first col -->
      <ul id="leftColumn" class="flex items-center flex-col gap-8">
        <!-- item -->
        <li
          data-id="1"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i
            class="fa-solid fa-file-lines text-primary-yellow w-6 text-center"
          ></i>
          تقارير مالية يومية \ أسبوعية \ شهرية
        </li>
        <!-- item -->
        <li
          data-id="2"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i class="fa-solid fa-bell text-primary-yellow w-6 text-center"></i>
          تنبيهات علي البريد
        </li>
        <!-- item -->
        <li
          data-id="3"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i
            class="fa-solid fa-magnifying-glass text-primary-yellow w-6 text-center"
          ></i>
          محرك بحث
        </li>
        <!-- item -->
        <li
          data-id="4"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i class="fa-solid fa-otter text-primary-yellow w-6 text-center"></i>
          اقتراحات باستخدام الذكاء الاصطناعي
        </li>
        <!-- item -->
        <li
          data-id="5"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i
            class="fa-solid fa-file-lines text-primary-yellow w-6 text-center"
          ></i>
          تقارير مالية تفصيلية
        </li>
      </ul>
      <!-- second col - Drop Zone -->
      <div
        id="dropZone"
        class="rounded-md border-dashed border-2 py-10 border-primary-yellow bg-white/10 backdrop-blur-sm min-h-[500px] flex flex-col transition-all duration-300"
      >
        <h2 class="text-primary-blue text-2xl font-medium text-center mb-5">
          حدد بنفسك الخصائص التي تريدها في موقعك / البرمجية
        </h2>

        <div class="bg-white/90 rounded-lg p-4 text-center mb-5 mx-4">
          <span id="selectedCount" class="text-2xl font-bold text-primary-blue"
            >0</span
          >
          <span class="text-lg text-gray-600"> خاصية مختارة</span>
        </div>

        <ul id="selectedFeatures" class="flex flex-col gap-4 flex-grow px-4">
          <div
            id="emptyState"
            class="text-center text-gray-500 text-lg mt-12 opacity-80"
          >
            <i class="fa-solid fa-hand-pointer text-4xl mb-5 block"></i>
            اسحب الخصائص التي تريدها من الأعمدة الجانبية
          </div>
        </ul>

        <div class="flex gap-4 mt-5 justify-center px-4">
          <button
            onclick="submitSelection()"
            class="bg-primary-blue text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-all duration-300 hover:-translate-y-1"
          >
            <i class="fa-solid fa-check ml-2"></i> تأكيد الاختيار
          </button>
          <button
            onclick="clearSelection()"
            class="bg-gray-500 text-primary-blue px-6 py-3 rounded-lg font-semibold hover:bg-gray-600 transition-all duration-300 hover:-translate-y-1"
          >
            <i class="fa-solid fa-trash ml-2"></i> مسح الكل
          </button>
        </div>
      </div>
      <!-- third col -->
      <ul id="rightColumn" class="flex items-center flex-col gap-8">
        <!-- item -->
        <li
          data-id="6"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i
            class="fa-solid fa-user-plus text-primary-yellow w-6 text-center"
          ></i>
          نظام لتسجيل الأعضاء
        </li>
        <!-- item -->
        <li
          data-id="7"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i
            class="fa-brands fa-cc-mastercard text-primary-yellow w-6 text-center"
          ></i>
          أنظمة مدفوعات رقمية
        </li>
        <!-- item -->
        <li
          data-id="8"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i
            class="fa-solid fa-chart-column text-primary-yellow w-6 text-center"
          ></i>
          إحصائيات وتقارير في شكل تفاعلي
        </li>
        <!-- item -->
        <li
          data-id="9"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i class="fa-solid fa-otter text-primary-yellow w-6 text-center"></i>
          اقتراحات باستخدام الذكاء الاصطناعي
        </li>
        <!-- item -->
        <li
          data-id="10"
          class="bg-white w-full shadow-xl flex items-center px-12 py-9 text-lg gap-5 rounded-md cursor-grab hover:shadow-2xl hover:-translate-y-1 transition-all duration-300 hover:border-2 hover:border-primary-yellow"
        >
          <i
            class="fa-solid fa-building-shield text-primary-yellow w-6 text-center"
          ></i>
          التحكم في صلاحيات المدراء
        </li>
      </ul>
    </section>
    <!-- end of choose feautres section -->

    <!-- start of section works related to service  -->
    <section class="related-works-section container">
      <!-- start of section headers -->
      <div class="text-center mb-16">
        <h2
          class="section-title text-primary-blue font-bold text-3xl md:text-5xl relative w-fit mx-auto"
        >
          <span
            class="absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
          ></span>
          أعمال متعلقة بالخدمة
        </h2>
        <p class="section-subtitle text-gray mt-5">
          قم بكتابة بياناتك و سيتم الرد عليك خلال 24 ساعة كحد أقصي
        </p>
      </div>
      <!-- end of section headers -->

      <!-- start of our products -->
      <div class="templates-carousel-container embla_templates">
        <div class="embla__viewport overflow-hidden">
          <div class="embla__container flex gap-8 lg:gap-12">
            <!-- Template Card 1 - Shopgrade Website -->
            <div
              class="template-card embla__slide flex-none w-full md:w-1/2 lg:w-1/3 bg-white rounded-2xl shadow-lg overflow-hidden p-3"
              data-category="ecommerce"
            >
              <div
                class="template-image relative overflow-hidden bg-[#F6FAFF] p-4"
              >
                <div class="laptop-container">
                  <!-- empty labtop -->
                  <img
                    src="public/pages/home-page/our-websites/labtop.webp"
                    alt="موقع"
                    class="object-contain block w-full max-w-[400px]"
                  />

                  <!-- The actual website overlay -->
                  <div class="laptop-screen-overlay">
                    <img
                      src="public/pages/home-page/our-websites/shegrade-full-screen.webp"
                      alt="Website"
                      class="laptop-website"
                    />
                  </div>
                </div>

                <div
                  class="absolute top-4 start-4 bg-[#EBFFF1] px-2 py-1 rounded text-xs flex items-center"
                >
                  خصم
                  <span class="text-[#2CA450]"> 12% </span>
                </div>
              </div>
              <div class="template-content p-6 flex-col flex gap-4">
                <h3
                  class="template-title text-2xl font-bold text-primary-blue mb-3"
                >
                  موقع Shopgrade
                </h3>
                <p
                  class="template-description text-[#15205C] text-sm leading-relaxed mb-4"
                >
                  موقع تجارة إلكترونية متكامل مع نظام إدارة المنتجات والطلبات.
                  يتميز بتصميم عصري وواجهة مستخدم سهلة الاستخدام.
                </p>
                <!-- seprator -->
                <div class="w-[90%] mx-auto h-[1px] bg-[#DDEAFC] my-5"></div>

                <!-- Technologies -->
                <div class="template-tech flex flex-wrap gap-2 mb-4">
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/ai-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="ILLUSTRATOR"
                      />
                    </div>
                    <p class="text-sm">ILLUSTRATOR</p>
                  </div>
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/angular-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="Angular"
                      />
                    </div>
                    <p class="text-sm">Angular</p>
                  </div>
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/photo-shop-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="ILLUSTRATOR"
                      />
                    </div>
                    <p class="text-sm">Photoshop Logo</p>
                  </div>
                </div>

                <!-- selected things, the blue bg, with text idk -->
                <div class="flex items-center gap-1">
                  <!-- first button -->
                  <button
                    class="items-center flex gap-2 px-4 py-2 bg-[#F0F6FF] text-[#2458A7] rounded-xl text-sm"
                  >
                    #تصميم مواقع
                    <i class="fa-solid fa-x text-[#B2C9EC]"></i>
                  </button>
                  <!-- first button -->
                  <button
                    class="items-center flex gap-2 px-4 py-2 bg-[#F0F6FF] text-[#2458A7] rounded-xl text-sm"
                  >
                    #موقع محاماة
                    <i class="fa-solid fa-x text-[#B2C9EC]"></i>
                  </button>
                </div>

                <button
                  class="btn-purchase template-actions flex-1 hover:text-white w-fit text-primary-blue py-2 px-4 rounded-lg border-primary-blue border text-sm font-medium"
                >
                  <i class="fa-solid fa-arrow-right"></i>
                  شراء الآن
                </button>
              </div>
            </div>

            <!-- Template Card 2 - Mobile App -->
            <div
              class="template-card embla__slide flex-none w-full md:w-1/2 lg:w-1/3 bg-white rounded-2xl shadow-lg overflow-hidden p-3"
              data-category="corporate"
            >
              <div
                class="template-image relative overflow-hidden bg-[#F6FAFF] p-4"
              >
                <div class="laptop-container">
                  <!-- empty labtop -->
                  <img
                    src="public/pages/home-page/our-websites/labtop.webp"
                    alt="موقع"
                    class="object-contain block w-full max-w-[400px]"
                  />

                  <!-- The actual website overlay -->
                  <div class="laptop-screen-overlay">
                    <img
                      src="public/pages/home-page/our-websites/lawyer-full-screen.webp"
                      alt="Website"
                      class="laptop-website"
                    />
                  </div>
                </div>

                <div
                  class="absolute top-4 start-4 bg-[#EBFFF1] px-2 py-1 rounded text-xs flex items-center"
                >
                  خصم
                  <span class="text-[#2CA450]"> 12% </span>
                </div>
              </div>
              <div class="template-content p-6 flex-col flex gap-4">
                <h3
                  class="template-title text-2xl font-bold text-primary-blue mb-3"
                >
                  موقع Shopgrade
                </h3>
                <p
                  class="template-description text-[#15205C] text-sm leading-relaxed mb-4"
                >
                  موقع تجارة إلكترونية متكامل مع نظام إدارة المنتجات والطلبات.
                  يتميز بتصميم عصري وواجهة مستخدم سهلة الاستخدام.
                </p>
                <!-- seprator -->
                <div class="w-[90%] mx-auto h-[1px] bg-[#DDEAFC] my-5"></div>

                <!-- Technologies -->
                <div class="template-tech flex flex-wrap gap-2 mb-4">
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/ai-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="ILLUSTRATOR"
                      />
                    </div>
                    <p class="text-sm">ILLUSTRATOR</p>
                  </div>
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/angular-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="Angular"
                      />
                    </div>
                    <p class="text-sm">Angular</p>
                  </div>
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/photo-shop-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="ILLUSTRATOR"
                      />
                    </div>
                    <p class="text-sm">Photoshop Logo</p>
                  </div>
                </div>

                <!-- selected things, the blue bg, with text idk -->
                <div class="flex items-center gap-1">
                  <!-- first button -->
                  <button
                    class="items-center flex gap-2 px-4 py-2 bg-[#F0F6FF] text-[#2458A7] rounded-xl text-sm"
                  >
                    #تصميم مواقع
                    <i class="fa-solid fa-x text-[#B2C9EC]"></i>
                  </button>
                  <!-- first button -->
                  <button
                    class="items-center flex gap-2 px-4 py-2 bg-[#F0F6FF] text-[#2458A7] rounded-xl text-sm"
                  >
                    #موقع محاماة
                    <i class="fa-solid fa-x text-[#B2C9EC]"></i>
                  </button>
                </div>

                <button
                  class="btn-purchase template-actions flex-1 hover:text-white w-fit text-primary-blue py-2 px-4 rounded-lg border-primary-blue border text-sm font-medium"
                >
                  <i class="fa-solid fa-arrow-right"></i>
                  شراء الآن
                </button>
              </div>
            </div>

            <!-- Template Card 3 - Dashboard -->
            <div
              class="template-card embla__slide flex-none w-full md:w-1/2 lg:w-1/3 bg-white rounded-2xl shadow-lg overflow-hidden p-3"
              data-category="mobile"
            >
              <div
                class="template-image relative overflow-hidden bg-[#F6FAFF] p-4"
              >
                <div class="laptop-container">
                  <!-- empty labtop -->
                  <img
                    src="public/pages/home-page/our-websites/labtop.webp"
                    alt="موقع"
                    class="object-contain block w-full max-w-[400px]"
                  />

                  <!-- The actual website overlay -->
                  <div class="laptop-screen-overlay">
                    <img
                      src="public/pages/home-page/our-websites/travell-full-screen.webp"
                      alt="Website"
                      class="laptop-website"
                    />
                  </div>
                </div>

                <div
                  class="absolute top-4 start-4 bg-[#EBFFF1] px-2 py-1 rounded text-xs flex items-center"
                >
                  خصم
                  <span class="text-[#2CA450]"> 12% </span>
                </div>
              </div>
              <div class="template-content p-6 flex-col flex gap-4">
                <h3
                  class="template-title text-2xl font-bold text-primary-blue mb-3"
                >
                  موقع Shopgrade
                </h3>
                <p
                  class="template-description text-[#15205C] text-sm leading-relaxed mb-4"
                >
                  موقع تجارة إلكترونية متكامل مع نظام إدارة المنتجات والطلبات.
                  يتميز بتصميم عصري وواجهة مستخدم سهلة الاستخدام.
                </p>
                <!-- seprator -->
                <div class="w-[90%] mx-auto h-[1px] bg-[#DDEAFC] my-5"></div>

                <!-- Technologies -->
                <div class="template-tech flex flex-wrap gap-2 mb-4">
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/ai-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="ILLUSTRATOR"
                      />
                    </div>
                    <p class="text-sm">ILLUSTRATOR</p>
                  </div>
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/angular-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="Angular"
                      />
                    </div>
                    <p class="text-sm">Angular</p>
                  </div>
                  <!-- first technology -->
                  <div class="flex gap-2 items-center justify-center">
                    <div
                      class="size-[30px] bg-[#FFF9F9] rounded-full flex items-center justify-center"
                    >
                      <img
                        src="public/pages/home-page/our-websites/photo-shop-logo.webp"
                        class="w-[12px] h-[18px] object-contain"
                        alt="ILLUSTRATOR"
                      />
                    </div>
                    <p class="text-sm">Photoshop Logo</p>
                  </div>
                </div>

                <!-- selected things, the blue bg, with text idk -->
                <div class="flex items-center gap-1">
                  <!-- first button -->
                  <button
                    class="items-center flex gap-2 px-4 py-2 bg-[#F0F6FF] text-[#2458A7] rounded-xl text-sm"
                  >
                    #تصميم مواقع
                    <i class="fa-solid fa-x text-[#B2C9EC]"></i>
                  </button>
                  <!-- first button -->
                  <button
                    class="items-center flex gap-2 px-4 py-2 bg-[#F0F6FF] text-[#2458A7] rounded-xl text-sm"
                  >
                    #موقع محاماة
                    <i class="fa-solid fa-x text-[#B2C9EC]"></i>
                  </button>
                </div>

                <button
                  class="btn-purchase template-actions flex-1 hover:text-white w-fit text-primary-blue py-2 px-4 rounded-lg border-primary-blue border text-sm font-medium"
                >
                  <i class="fa-solid fa-arrow-right"></i>
                  شراء الآن
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="testimonial-nav flex gap-3 mt-5">
        <button
          class="nav-btn next-btn bg-blue-700 text-white w-10 h-10 flex items-center justify-center hover:bg-blue-500 transition-colors duration-300"
        >
          <i class="fa-solid fa-arrow-right"></i>
        </button>
        <button
          class="nav-btn prev-btn bg-primary-blue text-white w-10 h-10 flex items-center justify-center hover:bg-blue-600 transition-colors duration-300"
        >
          <i class="fa-solid fa-arrow-left"></i>
        </button>
      </div>
    </section>
    <!-- end of section works related to service  -->

    <!-- start of  section desgin your website by yourself -->
    <section
      class="design-yourself-section flex container bg-[#F6FAFF] flex-col lg:flex-row items-center justify-between relative py-8 md:py-12 lg:py-16 xl:py-20 overflow-hidden"
    >
      <!-- floating stuff -->
      <div class="absolute inset-4 md:inset-6 lg:inset-10 pointer-events-none">
        <!-- flying envloap -->
        <svg
          width="60"
          height="56"
          viewBox="0 0 60 56"
          fill="none"
          class="design-floating-envelope absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 md:w-12 md:h-12 lg:w-[60px] lg:h-[56px]"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M59.0861 35.9555L17.789 55.5306C17.2212 55.7948 16.5442 55.5526 16.2603 54.9801L0.907639 22.0613C0.645574 21.4888 0.8858 20.8061 1.45361 20.5199L42.7507 0.944648C43.3185 0.680415 43.9955 0.922628 44.2794 1.49513L59.632 34.4141C59.9159 35.0086 59.6757 35.6912 59.0861 35.9555ZM17.8764 52.9544L57.0987 34.3701L42.707 3.54298L3.4846 22.1273L17.8764 52.9544Z"
            fill="#F26453"
          />
          <path
            d="M30.7832 29.2833C30.5648 29.3934 30.3028 29.4154 30.0407 29.3713L1.71583 22.6995C1.10434 22.5454 0.711247 21.9288 0.864119 21.3123C1.01699 20.6957 1.62849 20.2994 2.23997 20.4535L29.6694 26.9272L42.2267 1.49485C42.5106 0.922349 43.2095 0.702178 43.7554 0.96641C44.3232 1.25266 44.5635 1.93529 44.2796 2.50779L31.3073 28.7548C31.1981 28.997 31.0016 29.1732 30.7832 29.2833Z"
            fill="#F26453"
          />
        </svg>

        <!-- flying gray circle at the end  -->
        <svg
          width="282"
          height="282"
          viewBox="0 0 282 282"
          fill="none"
          class="design-floating-circle-right top-2 md:top-5 end-4 md:end-6 lg:end-10 absolute w-32 h-32 md:w-48 md:h-48 lg:w-[282px] lg:h-[282px] opacity-30 lg:opacity-100"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            opacity="0.07"
            cx="140.904"
            cy="140.699"
            r="128.5"
            transform="rotate(-170.158 140.904 140.699)"
            stroke="url(#paint0_linear_547_8616)"
            stroke-width="24"
          />
          <defs>
            <linearGradient
              id="paint0_linear_547_8616"
              x1="241.904"
              y1="98.6993"
              x2="278.904"
              y2="137.199"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#2C5BBA" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
          </defs>
        </svg>

        <!-- dotted line -->
        <img
          src="/public/pages/web-design/dashed-line.svg"
          class="design-floating-line hidden lg:block w-[600px] xl:w-[800px] h-[120px] xl:h-[177px] absolute end-0 top-2 xl:top-5 opacity-60"
          alt="Dashed line"
        />

        <!-- flying gray circle at the start  -->
        <svg
          width="282"
          height="282"
          viewBox="0 0 282 282"
          fill="none"
          class="design-floating-circle-left top-2 md:top-5 start-4 md:start-6 lg:start-10 absolute w-24 h-24 md:w-36 md:h-36 lg:w-[282px] lg:h-[282px] opacity-20 lg:opacity-100"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            opacity="0.07"
            cx="140.904"
            cy="140.699"
            r="128.5"
            transform="rotate(-170.158 140.904 140.699)"
            stroke="url(#paint0_linear_547_8617)"
            stroke-width="24"
          />
          <defs>
            <linearGradient
              id="paint0_linear_547_8617"
              x1="241.904"
              y1="98.6993"
              x2="278.904"
              y2="137.199"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#2C5BBA" />
              <stop offset="1" stop-color="white" />
            </linearGradient>
          </defs>
        </svg>

        <!-- Additional floating decorative elements -->
        <div
          class="design-floating-dots absolute top-1/4 start-1/4 w-2 h-2 md:w-3 md:h-3 bg-primary-yellow rounded-full opacity-60"
        ></div>
        <div
          class="design-floating-dots absolute bottom-1/4 end-1/4 w-1.5 h-1.5 md:w-2 md:h-2 bg-primary-blue rounded-full opacity-40"
        ></div>
        <div
          class="design-floating-dots absolute top-3/4 start-1/3 w-1 h-1 md:w-1.5 md:h-1.5 bg-[#F26453] rounded-full opacity-50"
        ></div>
      </div>

      <!-- the images -->
      <div
        class="design-images-container flex-1 flex gap-2 md:gap-3 items-center justify-center px-4 lg:px-0 mb-8 lg:mb-0"
      >
        <img
          src="/public/pages/web-design/design-concept.webp"
          alt="Web design concept"
          class="design-image-1 w-[120px] h-[156px] sm:w-[140px] sm:h-[182px] md:w-[160px] md:h-[208px] lg:w-[188px] lg:h-[244px] object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
        />
        <img
          src="/public/pages/web-design/sace-website.webp"
          alt="Web design concept"
          class="design-image-2 w-[130px] h-[260px] sm:w-[150px] sm:h-[300px] md:w-[170px] md:h-[340px] lg:w-[203px] lg:h-[406px] object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
        />
        <img
          src="/public/pages/web-design/capital-tour-website.webp"
          alt="Web design concept"
          class="design-image-3 w-[116px] h-[160px] sm:w-[136px] sm:h-[188px] md:w-[156px] md:h-[216px] lg:w-[182px] lg:h-[250px] object-cover rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300"
        />
      </div>

      <!-- the content -->
      <div class="design-content-container flex-1 px-4 lg:px-8">
        <div class="text-center lg:text-start">
          <p
            class="design-main-text text-primary-blue font-medium text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl leading-tight lg:leading-relaxed transform -skew-y-6 lg:-skew-y-12"
          >
            <span class="design-text-line-1 block">صمّم موقعك</span>
            <span
              class="design-text-line-2 block text-primary-yellow mt-2 lg:mt-4"
              >بنفسك</span
            >
            <span class="design-text-line-3 block mt-2 lg:mt-4">بكل سهولة</span>
          </p>

          <!-- Additional content for better engagement -->
          <div class="design-cta-container mt-6 lg:mt-8 opacity-0">
            <button
              class="design-cta-button bg-primary-blue hover:bg-primary-yellow text-white hover:text-primary-blue font-bold py-3 px-6 md:py-4 md:px-8 rounded-lg transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-lg hover:shadow-xl"
            >
              <i class="fa-solid fa-rocket ml-2"></i>
              ابدأ التصميم الآن
            </button>
          </div>
        </div>
      </div>
    </section>
    <!-- end of  section desgin your website by yourself -->

    <!-- start of blogs section -->
    <section class="container py-16">
      <div class="text-center mb-16">
        <h2
          class="section-title text-primary-blue font-bold text-3xl md:text-5xl relative w-fit mx-auto"
        >
          <span
            class="absolute -bottom-3 start-0 bg-primary-yellow h-1 w-16"
          ></span>
          مقالات مشابهة
        </h2>
        <p class="section-subtitle text-gray mt-5">
          قم بكتابة بياناتك و سيتم الرد عليك خلال 24 ساعة كحد أقصي
        </p>
      </div>

      <!-- Blogs Carousel -->
      <div class="blogs-carousel-container mx-auto embla_blogs">
        <!-- Carousel Viewport -->
        <div class="overflow-hidden embla__viewport">
          <div class="flex gap-4 embla__container">
            <!-- first blog -->
            <div
              class="embla__slide flex-none w-full sm:w-1/2 lg:w-1/3 xl:w-1/4"
            >
              <div
                class="blog-card rounded-lg overflow-hidden bg-white relative h-fit group shadow-md hover:shadow-xl transition-all duration-300 mx-2"
              >
                <!-- Image -->
                <img
                  src="public/pages/home-page/blogs/blog-1.webp"
                  alt=""
                  class="w-full h-[180px] sm:h-[200px] lg:h-[220px] object-cover group-hover:scale-105 transition-transform duration-300"
                />

                <!-- Content of the blog -->
                <div
                  class="flex flex-col px-3 sm:px-4 py-3 sm:py-4 gap-3 sm:gap-4"
                >
                  <a href="#" class="hover:underline hover:text-primary-blue">
                    <h3 class="font-medium text-xl">عنوان المقالة</h3>
                  </a>

                  <p class="text-light-blue text-sm">
                    معنا سوف تحصل على اقوي استضافة مواقع تتيح لك العديد...
                  </p>

                  <!-- author -->
                  <div class="flex items-center justify-between">
                    <div class="flex gap-2 items-center">
                      <img
                        src="public/pages/home-page/blogs/blog-author.webp"
                        alt="Author of the blog"
                        class="size-10 rounded-full"
                      />
                      <p class="text-sm">
                        بواسطة
                        <span class="font-medium"> : فهد العمري </span>
                      </p>
                    </div>

                    <p class="text-sm">15 jun 2024</p>
                  </div>
                </div>
              </div>
            </div>
            <!-- second blog -->
            <div
              class="embla__slide flex-none w-full sm:w-1/2 lg:w-1/3 xl:w-1/4"
            >
              <div
                class="blog-card rounded-lg overflow-hidden bg-white relative h-fit group shadow-md hover:shadow-xl transition-all duration-300 mx-2"
              >
                <!-- Image -->
                <img
                  src="public/pages/home-page/blogs/blog-2.webp"
                  alt=""
                  class="w-full h-[180px] sm:h-[200px] lg:h-[220px] object-cover group-hover:scale-105 transition-transform duration-300"
                />

                <!-- Content of the blog -->
                <div
                  class="flex flex-col px-3 sm:px-4 py-3 sm:py-4 gap-3 sm:gap-4"
                >
                  <a href="#" class="hover:underline hover:text-primary-blue">
                    <h3 class="font-medium text-xl">تطوير المواقع الحديثة</h3>
                  </a>

                  <p class="text-light-blue text-sm">
                    تعرف على أحدث تقنيات تطوير المواقع والأدوات المستخدمة...
                  </p>

                  <!-- author -->
                  <div class="flex items-center justify-between">
                    <div class="flex gap-2 items-center">
                      <img
                        src="public/pages/home-page/blogs/blog-author.webp"
                        alt="Author of the blog"
                        class="size-10 rounded-full"
                      />
                      <p class="text-sm">
                        بواسطة
                        <span class="font-medium"> : أحمد محمد </span>
                      </p>
                    </div>

                    <p class="text-sm">12 jun 2024</p>
                  </div>
                </div>
              </div>
            </div>
            <!-- third blog -->
            <div
              class="embla__slide flex-none w-full sm:w-1/2 lg:w-1/3 xl:w-1/4"
            >
              <div
                class="blog-card rounded-lg overflow-hidden bg-white relative h-fit group shadow-md hover:shadow-xl transition-all duration-300 mx-2"
              >
                <!-- Image -->
                <img
                  src="public/pages/home-page/blogs/blog-3.webp"
                  alt=""
                  class="w-full h-[180px] sm:h-[200px] lg:h-[220px] object-cover group-hover:scale-105 transition-transform duration-300"
                />

                <!-- Content of the blog -->
                <div
                  class="flex flex-col px-3 sm:px-4 py-3 sm:py-4 gap-3 sm:gap-4"
                >
                  <a href="#" class="hover:underline hover:text-primary-blue">
                    <h3 class="font-medium text-xl">أساسيات التصميم</h3>
                  </a>

                  <p class="text-light-blue text-sm">
                    تعلم أساسيات التصميم الجرافيكي وتطبيقها في المشاريع...
                  </p>

                  <!-- author -->
                  <div class="flex items-center justify-between">
                    <div class="flex gap-2 items-center">
                      <img
                        src="public/pages/home-page/blogs/blog-author.webp"
                        alt="Author of the blog"
                        class="size-10 rounded-full"
                      />
                      <p class="text-sm">
                        بواسطة
                        <span class="font-medium"> : سارة أحمد </span>
                      </p>
                    </div>

                    <p class="text-sm">10 jun 2024</p>
                  </div>
                </div>
              </div>
            </div>
            <!-- fourth blog -->
            <div
              class="embla__slide flex-none w-full sm:w-1/2 lg:w-1/3 xl:w-1/4"
            >
              <div
                class="blog-card rounded-lg overflow-hidden bg-white relative h-fit group shadow-md hover:shadow-xl transition-all duration-300 mx-2"
              >
                <!-- Image -->
                <img
                  src="public/pages/home-page/blogs/blog-4.webp"
                  alt=""
                  class="w-full h-[180px] sm:h-[200px] lg:h-[220px] object-cover group-hover:scale-105 transition-transform duration-300"
                />

                <!-- Content of the blog -->
                <div
                  class="flex flex-col px-3 sm:px-4 py-3 sm:py-4 gap-3 sm:gap-4"
                >
                  <a href="#" class="hover:underline hover:text-primary-blue">
                    <h3 class="font-medium text-xl">التسويق الرقمي</h3>
                  </a>

                  <p class="text-light-blue text-sm">
                    استراتيجيات التسويق الرقمي الفعالة لنمو أعمالك...
                  </p>

                  <!-- author -->
                  <div class="flex items-center justify-between">
                    <div class="flex gap-2 items-center">
                      <img
                        src="public/pages/home-page/blogs/blog-author.webp"
                        alt="Author of the blog"
                        class="size-10 rounded-full"
                      />
                      <p class="text-sm">
                        بواسطة
                        <span class="font-medium"> : محمد علي </span>
                      </p>
                    </div>

                    <p class="text-sm">08 jun 2024</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- fifth blog -->
            <div
              class="embla__slide flex-none w-full sm:w-1/2 lg:w-1/3 xl:w-1/4"
            >
              <div
                class="blog-card rounded-lg overflow-hidden bg-white relative h-fit group shadow-md hover:shadow-xl transition-all duration-300 mx-2"
              >
                <!-- Image -->
                <img
                  src="public/pages/home-page/blogs/blog-1.webp"
                  alt=""
                  class="w-full h-[180px] sm:h-[200px] lg:h-[220px] object-cover group-hover:scale-105 transition-transform duration-300"
                />

                <!-- Content of the blog -->
                <div
                  class="flex flex-col px-3 sm:px-4 py-3 sm:py-4 gap-3 sm:gap-4"
                >
                  <a href="#" class="hover:underline hover:text-primary-blue">
                    <h3 class="font-medium text-xl">أمان المواقع</h3>
                  </a>

                  <p class="text-light-blue text-sm">
                    كيفية حماية موقعك من التهديدات الأمنية المختلفة...
                  </p>

                  <!-- author -->
                  <div class="flex items-center justify-between">
                    <div class="flex gap-2 items-center">
                      <img
                        src="public/pages/home-page/blogs/blog-author.webp"
                        alt="Author of the blog"
                        class="size-10 rounded-full"
                      />
                      <p class="text-sm">
                        بواسطة
                        <span class="font-medium"> : عبدالله أحمد </span>
                      </p>
                    </div>

                    <p class="text-sm">05 jun 2024</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- sixth blog -->
            <div
              class="embla__slide flex-none w-full sm:w-1/2 lg:w-1/3 xl:w-1/4"
            >
              <div
                class="blog-card rounded-lg overflow-hidden bg-white relative h-fit group shadow-md hover:shadow-xl transition-all duration-300 mx-2"
              >
                <!-- Image -->
                <img
                  src="public/pages/home-page/blogs/blog-2.webp"
                  alt=""
                  class="w-full h-[180px] sm:h-[200px] lg:h-[220px] object-cover group-hover:scale-105 transition-transform duration-300"
                />

                <!-- Content of the blog -->
                <div
                  class="flex flex-col px-3 sm:px-4 py-3 sm:py-4 gap-3 sm:gap-4"
                >
                  <a href="#" class="hover:underline hover:text-primary-blue">
                    <h3 class="font-medium text-xl">تحسين الأداء</h3>
                  </a>

                  <p class="text-light-blue text-sm">
                    نصائح لتحسين أداء موقعك وسرعة التحميل...
                  </p>

                  <!-- author -->
                  <div class="flex items-center justify-between">
                    <div class="flex gap-2 items-center">
                      <img
                        src="public/pages/home-page/blogs/blog-author.webp"
                        alt="Author of the blog"
                        class="size-10 rounded-full"
                      />
                      <p class="text-sm">
                        بواسطة
                        <span class="font-medium"> : نور الدين </span>
                      </p>
                    </div>

                    <p class="text-sm">02 jun 2024</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Dots Navigation -->
        <div class="embla__dots"></div>
      </div>
    </section>
    <!-- end of blogs section -->

    <!-- start of footer -->
    <footer class="bg-primary-blue text-white">
      <!-- first part -->
      <div
        class="grid grid-cols-2 sm:grid-cols-3 gap-x-2 gap-y-5 lg:grid-cols-4 xl:grid-cols-7 lg:gap-4 xl:gap-3 container pt-[93px] pb-12"
      >
        <!-- first col -->
        <div class="space-y-5 col-span-2 xl:col-span-2">
          <div class="flex items-center gap-6">
            <img
              src="/public/pages/home-page/footer/serv5-logo.svg"
              alt="Serv5 Logo"
              class="size-[98px]"
            />
            <p
              class="font-bold text-xl border-b border-b-primary-yellow pb-2 max-w-[300px]"
            >
              عن سيرف
            </p>
          </div>

          <p class="text-sm leading-7 max-2-[410px]">
            لقد وصلت لوجهتك الصحيحة؛ ما تبحث عنه امامك معنا سوف تحصل على خدمة
            تصميم مواقع ، برمجة مواقع ، برمجة تطبيقات جوال ، خدمات تسويقية
            متميزة.
          </p>
          <!-- the images of our projects  -->
          <div class="flex gap-2 items-center">
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/blmgan.webp"
                alt="Blmgan app"
                class="object-contain w-[52px]"
              />
            </div>
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/makok.webp"
                alt="Makok Website"
                class="object-contain w-[52px]"
              />
            </div>
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/needbots.webp"
                alt="Chat app neat bot"
                class="object-contain w-[52px]"
              />
            </div>
          </div>
        </div>
        <!-- second col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            روابط هامة
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- third col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            الخدمات
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- fourth col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            تواصل معنا
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-envelope text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-phone text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-location-pin-lock text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- fifth col -->
        <div class="flex flex-col gap-4 col-span-1 xl:col-span-2">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            إشترك معنا
          </p>

          <p class="mt-4 lg:mt-10 flex flex-col gap-3">
            قم بكتابة بريدك الإلكتروني وإشترك في النشرة البريدية
          </p>

          <!-- Newsletter Subscription Form -->
          <form id="newsletter-form" class="md:mt-6 max-w-[350px]">
            <div class="newsletter-input-group relative">
              <i
                class="fa-solid fa-arrow-right absolute text-primary-yellow top-1/2 -translate-y-1/2"
              ></i>
              <input
                type="email"
                id="newsletter-email"
                name="email"
                placeholder="أدخل بريدك الإلكتروني"
                class="w-full bg-transparent text-white placeholder-gray-300 py-3 border-0 border-b border-gray-400 ps-5 placeholder:text-sm focus:border-primary-yellow focus:outline-none transition-colors duration-300"
                required
              />
              <div
                id="email-error"
                class="text-red-400 text-xs mt-1 hidden"
              ></div>
            </div>

            <div
              id="newsletter-success"
              class="text-green-400 text-sm mt-2 hidden"
            >
              تم الاشتراك بنجاح! شكراً لك.
            </div>
          </form>
        </div>
      </div>
      <!-- second part -->
      <div
        class="container flex flex-col gap-5 sm:flex-row justify-between items-center py-5 w-full border-t border-t-gray"
      >
        <!-- go up and terms and condtions links -->
        <div class="flex gap-4 items-center">
          <button
            id="goToTopBtn"
            class="size-12 p-5 text-white bg-primary-yellow hover:scale-110 transition-colors cursor-pointer flex items-center justify-center"
          >
            <i class="fa-solid fa-arrow-up text-xl"></i>
          </button>

          <a href="#" class="hover:text-primary-yellow">سياسة الخصوصية </a>
          <div class="w-[1px] h-6 bg-white my-auto"></div>
          <a href="#" class="hover:text-primary-yellow">الشروط والأحكــام</a>
        </div>

        <!-- all right reserved text -->
        <p class="text-sm text-center">
          © 2025 جميع الحقوق محفوظة لشركة سيرف. جميع الحقوق محفوظة.
        </p>
      </div>
    </footer>
    <!-- end of footer -->

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
