import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Achievements Interactive Hover Effect
export function initAchievementsHoverEffect() {
  // Register ScrollTrigger
  gsap.registerPlugin(ScrollTrigger);

  // Get all achievement items and their content divs
  const achievementItems = document.querySelectorAll('.achievement-item');
  const contentDivs = document.querySelectorAll('.achievement-content');
  const images = document.querySelectorAll('.achievement-image');

  // Get center logo and circle elements
  const centerCircle = document.querySelector('img[alt="circle"]');
  const centerLogo = document.querySelector('img[alt="Serv 5 logo"]');



  if (achievementItems.length === 0) {
    return;
  }

  // Check if we're on 2xl screens (hover behavior) or smaller (stagger behavior)
  function is2XLScreen() {
    return window.innerWidth >= 1536; // 2xl breakpoint
  }

  // Initialize based on screen size
  function initializeAchievements() {
    if (is2XLScreen()) {
      init2XLHoverEffect();
    } else {
      initStaggerAnimation();
    }
  }

  // All screens below 2xl: Show all content divs with stagger animation
  function initStaggerAnimation() {
    // Get all achievement items for stagger animation
    const achievementItems = document.querySelectorAll('.achievement-item');

    // Set initial states for all achievement items
    achievementItems.forEach((item) => {
      gsap.set(item, {
        autoAlpha: 0,
        y: 60,
        scale: 0.85,
        skewY: 3,
        filter: "blur(8px)",
        transformOrigin: "center center"
      });
    });

    // Set initial states for all content divs and images
    contentDivs.forEach((div) => {
      gsap.set(div, {
        autoAlpha: 0,
        y: 50,
        scale: 0.9,
        transformOrigin: "center center"
      });
    });

    images.forEach((img) => {
      gsap.set(img, {
        autoAlpha: 0,
        y: 30,
        scale: 0.95,
        transformOrigin: "center center"
      });
    });

    // Create scroll-triggered stagger animation for achievement items
    const timeline = gsap.timeline({
      scrollTrigger: {
        trigger: ".achievement-item",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse",
      }
    });

    // First animate the achievement items with stagger
    timeline.to(achievementItems, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: {
        amount: 0.6,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    });

    // Then animate images with stagger
    timeline.to(images, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      duration: 0.5,
      stagger: 0.15,
      ease: "power2.out"
    }, "-=0.4");

    // Finally animate content divs with stagger
    timeline.to(contentDivs, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      duration: 0.6,
      stagger: 0.2,
      ease: "back.out(1.7)"
    }, "-=0.5");

  }

  // 2XL screens only: Hover effect with only one content visible at a time
  function init2XLHoverEffect() {
    // Get all achievement items for stagger animation
    const achievementItems = document.querySelectorAll('.achievement-item');

    // Set initial states for achievement items
    achievementItems.forEach((item) => {
      gsap.set(item, {
        autoAlpha: 0,
        y: 60,
        scale: 0.85,
        skewY: 3,
        filter: "blur(8px)",
        transformOrigin: "center center"
      });
    });

    // Create scroll-triggered stagger animation for achievement items on 2XL
    gsap.timeline({
      scrollTrigger: {
        trigger: ".achievement-item",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse",
      }
    }).to(achievementItems, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: {
        amount: 0.6,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    });

    // Set initial states - hide all content except first
    contentDivs.forEach((div, index) => {
      if (index === 0) {
        // First div visible
        gsap.set(div, {
          autoAlpha: 1,
          scale: 1,
          transformOrigin: "center center"
        });
      } else {
        // Others hidden - start from image position
        const correspondingImage = images[index];
        if (correspondingImage) {
          const imageRect = correspondingImage.getBoundingClientRect();
          const divRect = div.getBoundingClientRect();

          // Calculate relative position from image to div
          const xOffset = imageRect.left - divRect.left;
          const yOffset = imageRect.top - divRect.top;

          gsap.set(div, {
            autoAlpha: 0,
            scale: 0.1,
            x: xOffset,
            y: yOffset,
            transformOrigin: "center center"
          });
        } else {
          gsap.set(div, {
            autoAlpha: 0,
            scale: 0.1,
            transformOrigin: "center center"
          });
        }
      }
    });

    // Set images to visible state
    images.forEach((img) => {
      gsap.set(img, {
        autoAlpha: 1,
        scale: 1,
        transformOrigin: "center center"
      });
    });

    // Track currently active achievement and animation state
    let currentActive = 0;
    let isAnimating = false;
    let currentTimeline = null;

    // Wild cinematic effect for center logo and circle
    function triggerCinematicEffect(achievementIndex) {
      if (!centerCircle || !centerLogo) return;

      // Create dramatic timeline
      const cinematicTimeline = gsap.timeline();

      // All achievements use the same elegant effect - smooth rotation and scale
      const effects = [
        // Achievement 1: Smooth rotation and scale
        () => {
          cinematicTimeline
            .to(centerCircle, {
              rotation: "+=360",
              scale: 1.2,
              duration: 0.8,
              ease: "power2.out"
            })
            .to(centerLogo, {
              rotation: "+=360",
              scale: 1.2,
              duration: 0.6,
              ease: "back.out(2)"
            }, "-=0.6")
            .to([centerCircle, centerLogo], {
              scale: 1,
              duration: 0.5,
              ease: "elastic.out(1, 0.5)"
            });
        },

        // Achievement 2: Same elegant effect
        () => {
          cinematicTimeline
            .to(centerCircle, {
              rotation: "+=360",
              scale: 1.2,
              duration: 0.8,
              ease: "power2.out"
            })
            .to(centerLogo, {
              rotation: "+=360",
              scale: 1.2,
              duration: 0.6,
              ease: "back.out(2)"
            }, "-=0.6")
            .to([centerCircle, centerLogo], {
              scale: 1,
              duration: 0.5,
              ease: "elastic.out(1, 0.5)"
            });
        },

        // Achievement 3: Same elegant effect
        () => {
          cinematicTimeline
            .to(centerCircle, {
              rotation: "+=360",
              scale: 1.2,
              duration: 0.8,
              ease: "power2.out"
            })
            .to(centerLogo, {
              rotation: "+=360",
              scale: 1.2,
              duration: 0.6,
              ease: "back.out(2)"
            }, "-=0.6")
            .to([centerCircle, centerLogo], {
              scale: 1,
              duration: 0.5,
              ease: "elastic.out(1, 0.5)"
            });
        },

        // Achievement 4: Same elegant effect
        () => {
          cinematicTimeline
            .to(centerCircle, {
              rotation: "+=360",
              scale: 1.2,
              duration: 0.8,
              ease: "power2.out"
            })
            .to(centerLogo, {
              rotation: "+=360",
              scale: 1.2,
              duration: 0.6,
              ease: "back.out(2)"
            }, "-=0.6")
            .to([centerCircle, centerLogo], {
              scale: 1,
              duration: 0.5,
              ease: "elastic.out(1, 0.5)"
            });
        }
      ];

      // Execute the effect for the current achievement
      if (effects[achievementIndex]) {
        effects[achievementIndex]();
      }

      return cinematicTimeline;
    }

    // Function to show specific achievement content
    function showAchievement(index) {
      if (index === currentActive) return; // Already active

      // Kill any existing animation to prevent overlaps
      if (currentTimeline) {
        currentTimeline.kill();
      }

      // Set animating flag
      isAnimating = true;

      // Immediately hide ALL content divs except the target
      contentDivs.forEach((div, i) => {
        if (i !== index) {
          gsap.set(div, { autoAlpha: 0 });
        }
      });

      const newContentDiv = contentDivs[index];
      const correspondingImage = images[index];

      // Create new timeline
      currentTimeline = gsap.timeline({
        onComplete: () => {
          isAnimating = false;
          currentTimeline = null;
        }
      });

      // Set initial state for new content (at image position, small scale)
      if (newContentDiv && correspondingImage) {
        const imageRect = correspondingImage.getBoundingClientRect();
        const divRect = newContentDiv.getBoundingClientRect();
        const xOffset = imageRect.left - divRect.left;
        const yOffset = imageRect.top - divRect.top;

        gsap.set(newContentDiv, {
          autoAlpha: 0,
          scale: 0.1,
          x: xOffset,
          y: yOffset,
          transformOrigin: "center center"
        });

        // Animate to full size and position
        currentTimeline.to(newContentDiv, {
          autoAlpha: 1,
          scale: 1,
          x: 0,
          y: 0,
          duration: 0.5,
          ease: "back.out(1.4)"
        });

        // Trigger wild cinematic effect for center logo/circle
        currentTimeline.add(() => {
          triggerCinematicEffect(index);
        }, "-=0.3");
      }

      currentActive = index;
    }

    // Add hover effects to images
    images.forEach((image, index) => {
      // Image hover effects
      image.addEventListener('mouseenter', () => {
        // Only proceed if not currently animating or if different from current
        if (!isAnimating || index !== currentActive) {
          showAchievement(index);
        }

        // Image scale effect
        gsap.to(image, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out"
        });
      });

      image.addEventListener('mouseleave', () => {
        // Reset image scale
        gsap.to(image, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      });
    });

    // Add hover effects to content divs themselves
    contentDivs.forEach((div, index) => {
      div.addEventListener('mouseenter', () => {
        showAchievement(index);
      });
    });

  }

  // Initialize achievements based on screen size
  initializeAchievements();

  // Handle window resize to switch between mobile/desktop behavior
  let resizeTimeout;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      // Clear existing animations
      gsap.killTweensOf(contentDivs);
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());

      // Reinitialize based on new screen size
      initializeAchievements();
    }, 250);
  });

}
