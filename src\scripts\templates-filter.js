import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Templates Filter and Animation
export function initTemplatesFilter() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);


  // Get DOM elements
  const templateTabs = document.querySelectorAll('.template-tab');
  const templateCards = document.querySelectorAll('.template-card');
  const templatesGrid = document.querySelector('.templates-grid');
  const templatesSection = document.querySelector('.websites-templates-section');

  if (!templateTabs.length || !templateCards.length) {
    return;
  }

  let currentFilter = 'all';
  let isAnimating = false;

  // Filter templates by category
  function filterTemplates(category) {
    if (isAnimating || category === currentFilter) return;

    isAnimating = true;
    currentFilter = category;

    // Update active tab
    templateTabs.forEach(tab => {
      tab.classList.remove('active');
      tab.classList.remove('text-primary-blue', 'border-b-primary-yellow');
      tab.classList.add('text-gray', 'border-transparent');
    });

    const activeTab = document.querySelector(`[data-category="${category}"]`);
    if (activeTab) {
      activeTab.classList.add('active');
      activeTab.classList.remove('text-gray', 'border-transparent');
      activeTab.classList.add('text-primary-blue', 'border-b-primary-yellow');
    }

    // Animate cards out
    const timeline = gsap.timeline({
      onComplete: () => {
        // Show filtered cards
        templateCards.forEach((card, index) => {
          const cardCategory = card.getAttribute('data-category');
          const shouldShow = category === 'all' || cardCategory === category;

          if (shouldShow) {
            card.style.display = 'block';
            gsap.fromTo(card,
              {
                autoAlpha: 0,
                y: 50,
                scale: 0.9
              },
              {
                autoAlpha: 1,
                y: 0,
                scale: 1,
                duration: 0.6,
                delay: index * 0.1,
                ease: "back.out(1.4)"
              }
            );
          } else {
            card.style.display = 'none';
          }
        });

        isAnimating = false;
      }
    });

    // Animate visible cards out
    const visibleCards = Array.from(templateCards).filter(card =>
      card.style.display !== 'none'
    );

    timeline.to(visibleCards, {
      autoAlpha: 0,
      y: -30,
      scale: 0.95,
      duration: 0.4,
      stagger: 0.05,
      ease: "power2.in"
    });
  }

  // Add click event listeners to tabs
  templateTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const category = tab.getAttribute('data-category');
      filterTemplates(category);
    });

    // Add hover effects
    tab.addEventListener('mouseenter', () => {
      if (!tab.classList.contains('active')) {
        gsap.to(tab, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });

    tab.addEventListener('mouseleave', () => {
      if (!tab.classList.contains('active')) {
        gsap.to(tab, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  });

  // Template cards hover effects removed

  // Add click effects to action buttons
  const previewButtons = document.querySelectorAll('.btn-preview');
  const purchaseButtons = document.querySelectorAll('.btn-purchase');

  [...previewButtons, ...purchaseButtons].forEach(button => {
    button.addEventListener('click', (e) => {
      e.preventDefault();

      // Create ripple effect
      const ripple = document.createElement('span');
      const rect = button.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = e.clientX - rect.left - size / 2;
      const y = e.clientY - rect.top - size / 2;

      ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        transform: scale(0);
        pointer-events: none;
      `;

      button.style.position = 'relative';
      button.style.overflow = 'hidden';
      button.appendChild(ripple);

      gsap.to(ripple, {
        scale: 2,
        autoAlpha: 0,
        duration: 0.6,
        ease: "power2.out",
        onComplete: () => {
          ripple.remove();
        }
      });

      // Button animation
      gsap.to(button, {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });
    });
  });

  // Initial animation for the entire section
  if (templatesSection) {
    gsap.set([templatesSection.querySelector('h2'), templatesSection.querySelector('p')], {
      autoAlpha: 0,
      y: 50
    });

    gsap.set(templateTabs, {
      autoAlpha: 0,
      y: 30
    });

    gsap.set(templateCards, {
      autoAlpha: 0,
      y: 80,
      scale: 0.9
    });

    // Scroll-triggered animation
    const sectionTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: templatesSection,
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    sectionTimeline
      .to([templatesSection.querySelector('h2'), templatesSection.querySelector('p')], {
        autoAlpha: 1,
        y: 0,
        duration: 0.8,
        stagger: 0.2,
        ease: "back.out(1.4)"
      })
      .to(templateTabs, {
        autoAlpha: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: "back.out(1.4)"
      }, "-=0.4")
      .to(templateCards, {
        autoAlpha: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: 0.15,
        ease: "back.out(1.4)"
      }, "-=0.3");
  }

  // View All Button animation
  const viewAllBtn = document.querySelector('.view-all-btn');
  if (viewAllBtn) {
    viewAllBtn.addEventListener('click', (e) => {
      e.preventDefault();

      gsap.to(viewAllBtn, {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });

      // Add loading animation
      const originalText = viewAllBtn.textContent;
      viewAllBtn.textContent = 'جاري التحميل...';

      setTimeout(() => {
        viewAllBtn.textContent = originalText;
      }, 1500);
    });

    // Floating animation for view all button
    gsap.to(viewAllBtn, {
      y: -5,
      duration: 2,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });
  }

}
