import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Professional Blog Section GSAP Animation
export function initBlogGSAP() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Get blog elements
  const blogCards = document.querySelectorAll('.blog-card');
  const blogCardLarge = document.querySelector('.blog-card-large');



  // Create blog section timeline for header
  const headerTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".blog-section",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse"
    }
  });

  // Set initial states for header elements
  gsap.set(".blog-title", {
    autoAlpha: 0,
    y: 80,
    scale: 0.8,
    skewY: 6,
    filter: "blur(10px)"
  });

  gsap.set(".blog-subtitle", {
    autoAlpha: 0,
    y: 50,
    x: -40,
    filter: "blur(6px)"
  });

  // Set initial state for the yellow underline
  gsap.set(".blog-title span", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: "left center"
  });

  // Set initial states for blog cards
  if (blogCards.length > 0) {
    gsap.set(blogCards, {
      autoAlpha: 0,
      y: 60,
      scale: 0.9,
      rotationY: 15,
      filter: "blur(8px)"
    });
  }

  if (blogCardLarge) {
    gsap.set(blogCardLarge, {
      autoAlpha: 0,
      y: 80,
      scale: 0.85,
      rotationX: 15,
      filter: "blur(10px)"
    });
  }

  // Header animation sequence
  headerTimeline
    // 1. Title animation with professional effects
    .to(".blog-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    })

    // 2. Yellow underline animation
    .to(".blog-title span", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")

    // 3. Subtitle slide from left
    .to(".blog-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.4");

  // Blog cards animation timeline
  const cardsTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".blog-section",
      start: "top 60%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });

  // Animate small blog cards with stagger
  if (blogCards.length > 0) {
    cardsTimeline.to(blogCards, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.7,
      stagger: 0.15,
      ease: "back.out(1.4)"
    });
  }

  // Animate large blog card
  if (blogCardLarge) {
    cardsTimeline.to(blogCardLarge, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    }, "-=0.5");
  }

  return { headerTimeline, cardsTimeline };
}
