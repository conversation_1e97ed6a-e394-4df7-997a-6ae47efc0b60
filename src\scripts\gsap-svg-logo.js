import { gsap } from "gsap";
import { DrawSVGPlugin } from "gsap/DrawSVGPlugin";

// SVG Logo Animation with stroke-draw effect
export function initSVGLogoAnimation() {
  // Register the DrawSVG plugin
  gsap.registerPlugin(DrawSVGPlugin);

  // Create main timeline for the logo animation
  const logoTimeline = gsap.timeline();

  // Get all the logo elements
  const outerCircle = document.querySelector("#logo-outer-circle");
  const innerCircle = document.querySelector("#logo-inner-circle");
  const baseRectangle = document.querySelector("#logo-base-rectangle");
  const textIcons = document.querySelector("#logo-text-icons");
  const logoLetters = document.querySelectorAll(".logo-letter");
  const logoIcons = document.querySelectorAll(".logo-icon");
  const logoShine = document.querySelector("#logo-shine");
  const logoContainer = document.querySelector(".header-logo");

  // Set initial states
  gsap.set([outerCircle, innerCircle], {
    drawSVG: "0%",
    stroke: "#DE8545"
  });

  gsap.set(baseRectangle, {
    drawSVG: "0%",
    stroke: "#DE8545",
    strokeWidth: "2",
    fill: "transparent"
  });

  gsap.set([textIcons, logoShine], {
    autoAlpha: 0
  });

  gsap.set(logoShine, {
    x: -20 // Initial shine position
  });

  gsap.set(logoLetters, {
    autoAlpha: 0,
    scale: 0.8,
    transformOrigin: "center center"
  });

  gsap.set(logoIcons, {
    autoAlpha: 0,
    scale: 0.8,
    transformOrigin: "center center"
  });

  gsap.set(logoContainer, {
    autoAlpha: 0,
    scale: 0.9,
    transformOrigin: "center center"
  });

  // Animation sequence - Total duration: 1.5 seconds
  logoTimeline
    // 1. Show logo container
    .to(logoContainer, {
      autoAlpha: 1,
      duration: 0.05
    })

    // 2. Draw outer circle with rotation and color transition
    .to(outerCircle, {
      drawSVG: "100%",
      rotation: 360,
      transformOrigin: "center center",
      duration: 0.4,
      ease: "power2.out"
    })
    .to(outerCircle, {
      stroke: "#16215C",
      duration: 0.15,
      ease: "power2.out"
    }, "-=0.15")

    // 3. Draw inner circle with color transition
    .to(innerCircle, {
      drawSVG: "100%",
      duration: 0.3,
      ease: "power2.out"
    }, "-=0.1")
    .to(innerCircle, {
      stroke: "#16215C",
      duration: 0.15,
      ease: "power2.out"
    }, "-=0.15")

    // 4. Draw base rectangle with color transition
    .to(baseRectangle, {
      drawSVG: "100%",
      duration: 0.25,
      ease: "power2.out"
    }, "-=0.05")
    .to(baseRectangle, {
      stroke: "#16215C",
      fill: "#15205C",
      strokeWidth: "0",
      duration: 0.15,
      ease: "power2.out"
    }, "-=0.1")

    // 5. Show text/icons container
    .to(textIcons, {
      autoAlpha: 1,
      duration: 0.05
    })

    // 6. Animate letters with staggered pop-in effect
    .to(logoLetters, {
      autoAlpha: 1,
      scale: 1,
      duration: 0.2,
      stagger: {
        amount: 0.3,
        from: "start",
        ease: "back.out(1.7)"
      },
      ease: "back.out(1.7)"
    }, "-=0.05")

    // 7. Animate icons with staggered pop-in effect
    .to(logoIcons, {
      autoAlpha: 1,
      scale: 1,
      duration: 0.2,
      stagger: {
        amount: 0.2,
        from: "start",
        ease: "back.out(1.7)"
      },
      ease: "back.out(1.7)"
    }, "-=0.2")

    // 8. Scale logo from 0.9 to 1 with elastic easing
    .to(logoContainer, {
      scale: 1,
      duration: 0.4,
      ease: "elastic.out(1, 0.5)"
    }, "-=0.15")

    // 9. Shine effect sweep from left to right
    .to(logoShine, {
      autoAlpha: 1,
      duration: 0.05
    }, "-=0.1")
    .to(logoShine, {
      x: 141, // Move across the entire logo width + some extra
      duration: 0.3,
      ease: "power2.out"
    })
    .to(logoShine, {
      autoAlpha: 0,
      duration: 0.05
    });

  return logoTimeline;
}

// Function to replay the SVG logo animation
export function replaySVGLogoAnimation() {
  // Get all the logo elements
  const outerCircle = document.querySelector("#logo-outer-circle");
  const innerCircle = document.querySelector("#logo-inner-circle");
  const baseRectangle = document.querySelector("#logo-base-rectangle");
  const textIcons = document.querySelector("#logo-text-icons");
  const logoLetters = document.querySelectorAll(".logo-letter");
  const logoIcons = document.querySelectorAll(".logo-icon");
  const logoShine = document.querySelector("#logo-shine");
  const logoContainer = document.querySelector(".header-logo");

  // Create a new timeline for replay
  const replayTimeline = gsap.timeline();

  // Reset to initial states
  gsap.set([outerCircle, innerCircle], {
    drawSVG: "0%",
    stroke: "#DE8545"
  });

  gsap.set(baseRectangle, {
    drawSVG: "0%",
    stroke: "#DE8545",
    strokeWidth: "2",
    fill: "transparent"
  });

  gsap.set([textIcons, logoShine], {
    autoAlpha: 0
  });

  gsap.set(logoShine, {
    x: -20 // Reset shine position
  });

  gsap.set(logoLetters, {
    autoAlpha: 0,
    scale: 0.8
  });

  gsap.set(logoIcons, {
    autoAlpha: 0,
    scale: 0.8
  });

  gsap.set(logoContainer, {
    scale: 0.9
  });

  // Replay the same animation sequence
  replayTimeline
    .to(outerCircle, {
      drawSVG: "100%",
      rotation: "+=360",
      transformOrigin: "center center",
      duration: 0.4,
      ease: "power2.out"
    })
    .to(outerCircle, {
      stroke: "#16215C",
      duration: 0.15,
      ease: "power2.out"
    }, "-=0.15")
    .to(innerCircle, {
      drawSVG: "100%",
      duration: 0.3,
      ease: "power2.out"
    }, "-=0.1")
    .to(innerCircle, {
      stroke: "#16215C",
      duration: 0.15,
      ease: "power2.out"
    }, "-=0.15")
    .to(baseRectangle, {
      drawSVG: "100%",
      duration: 0.25,
      ease: "power2.out"
    }, "-=0.05")
    .to(baseRectangle, {
      stroke: "#16215C",
      fill: "#15205C",
      strokeWidth: "0",
      duration: 0.15,
      ease: "power2.out"
    }, "-=0.1")
    .to(textIcons, {
      autoAlpha: 1,
      duration: 0.05
    })
    .to(logoLetters, {
      autoAlpha: 1,
      scale: 1,
      duration: 0.2,
      stagger: {
        amount: 0.3,
        from: "start",
        ease: "back.out(1.7)"
      },
      ease: "back.out(1.7)"
    }, "-=0.05")
    .to(logoIcons, {
      autoAlpha: 1,
      scale: 1,
      duration: 0.2,
      stagger: {
        amount: 0.2,
        from: "start",
        ease: "back.out(1.7)"
      },
      ease: "back.out(1.7)"
    }, "-=0.2")
    .to(logoContainer, {
      scale: 1,
      duration: 0.4,
      ease: "elastic.out(1, 0.5)"
    }, "-=0.15")
    .to(logoShine, {
      autoAlpha: 1,
      duration: 0.05
    }, "-=0.1")
    .to(logoShine, {
      x: 141,
      duration: 0.3,
      ease: "power2.out"
    })
    .to(logoShine, {
      autoAlpha: 0,
      duration: 0.05
    });

  return replayTimeline;
}

// Enhanced logo hover effect for SVG - triggers full animation
export function initSVGLogoHoverEffect() {
  const logoContainer = document.querySelector(".header-logo");
  let isAnimating = false;

  if (logoContainer) {
    // Mouse enter - trigger full animation
    logoContainer.addEventListener("mouseenter", () => {
      if (!isAnimating) {
        isAnimating = true;
        const hoverAnimation = replaySVGLogoAnimation();

        hoverAnimation.eventCallback("onComplete", () => {
          isAnimating = false;
        });
      }
    });
  }
}
