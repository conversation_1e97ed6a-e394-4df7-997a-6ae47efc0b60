// Drag and Drop Features Functionality
export function initDragDropFeatures() {
  // Check if Sortable is available
  if (typeof Sortable === 'undefined') {
    console.warn('Sortable.js library not found. Drag and drop functionality will not work.');
    return;
  }

  // Check if we're on the page with the drag-drop features
  const leftColumn = document.getElementById('leftColumn');
  const rightColumn = document.getElementById('rightColumn');
  const selectedFeatures = document.getElementById('selectedFeatures');
  const dropZone = document.getElementById('dropZone');
  const emptyState = document.getElementById('emptyState');
  const selectedCount = document.getElementById('selectedCount');

  // Exit if elements don't exist
  if (!leftColumn || !rightColumn || !selectedFeatures || !dropZone) {
    return;
  }

  // Create sortable instances
  const sortableLeft = new Sortable(leftColumn, {
    group: {
      name: 'features',
      pull: 'clone',
      put: true
    },
    animation: 150,
    ghostClass: 'sortable-ghost',
    chosenClass: 'sortable-chosen',
    onEnd: function (evt) {
      // Return item to original position if not dropped in center
      if (evt.to !== selectedFeatures) {
        evt.item.remove();
      }
    }
  });

  const sortableRight = new Sortable(rightColumn, {
    group: {
      name: 'features',
      pull: 'clone',
      put: true
    },
    animation: 150,
    ghostClass: 'sortable-ghost',
    chosenClass: 'sortable-chosen',
    onEnd: function (evt) {
      // Return item to original position if not dropped in center
      if (evt.to !== selectedFeatures) {
        evt.item.remove();
      }
    }
  });

  const sortableCenter = new Sortable(selectedFeatures, {
    group: {
      name: 'features',
      pull: false,
      put: true
    },
    animation: 150,
    ghostClass: 'sortable-ghost',
    chosenClass: 'sortable-chosen',
    onAdd: function (evt) {
      updateCounter();
      toggleEmptyState();

      // Add selected styling and remove button functionality
      const item = evt.item;
      item.classList.add('bg-white/95', 'border-2', 'border-green-500', 'relative');

      // Add checkmark
      const checkmark = document.createElement('div');
      checkmark.className = 'absolute top-2 left-2 bg-green-500 text-white w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold';
      checkmark.innerHTML = '✓';
      item.appendChild(checkmark);

      // Add double-click to remove functionality
      item.addEventListener('dblclick', function () {
        removeItem(this);
      });

      // Change cursor to pointer for selected items
      item.classList.remove('cursor-grab');
      item.classList.add('cursor-pointer');
    },
    onUpdate: function (evt) {
      updateCounter();
    }
  });

  // Add drag over effects
  dropZone.addEventListener('dragover', function (e) {
    e.preventDefault();
    this.classList.add('border-green-500', 'bg-green-100/20', 'scale-105');
    this.classList.remove('border-primary-yellow');
  });

  dropZone.addEventListener('dragleave', function (e) {
    if (!this.contains(e.relatedTarget)) {
      this.classList.remove('border-green-500', 'bg-green-100/20', 'scale-105');
      this.classList.add('border-primary-yellow');
    }
  });

  dropZone.addEventListener('drop', function (e) {
    this.classList.remove('border-green-500', 'bg-green-100/20', 'scale-105');
    this.classList.add('border-primary-yellow');
  });

  // Helper functions
  function updateCounter() {
    // Count only the li elements, not the empty state div
    const count = selectedFeatures.querySelectorAll('li').length;
    if (selectedCount) {
      selectedCount.textContent = count;
    }
  }

  function toggleEmptyState() {
    if (!emptyState) return;

    const hasItems = selectedFeatures.children.length > 1 ||
      (selectedFeatures.children.length === 1 && selectedFeatures.children[0] !== emptyState);

    emptyState.style.display = hasItems ? 'none' : 'block';
  }

  function removeItem(item) {
    item.remove();
    updateCounter();
    toggleEmptyState();
  }

  // Global functions for buttons
  window.clearSelection = function () {
    const items = selectedFeatures.querySelectorAll('li');
    items.forEach(item => item.remove());
    updateCounter();
    toggleEmptyState();
  };

  window.submitSelection = function () {
    const items = selectedFeatures.querySelectorAll('li');
    const selectedItems = [];

    items.forEach(item => {
      const text = item.textContent.trim().replace('✓', '').trim();
      const icon = item.querySelector('i').className;
      selectedItems.push({
        text: text,
        icon: icon,
        id: item.dataset.id
      });
    });

    if (selectedItems.length === 0) {
      alert('الرجاء اختيار خاصية واحدة على الأقل');
      return;
    }

    // Here you can send the data to your server
    console.log('Selected features:', selectedItems);

    // Show success message
    alert(`تم اختيار ${selectedItems.length} خاصية بنجاح!\nيمكنك رؤية التفاصيل في console المتصفح`);
  };

  // Initialize
  updateCounter();
  toggleEmptyState();

  // Styles are already defined in the HTML file
}
