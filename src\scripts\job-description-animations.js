import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// Job Description Page Animations
export function initJobDescriptionAnimations() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Get direction for RTL support
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Initialize breadcrumb section animations
  initBreadcrumbAnimations();

  // Initialize job description section animations
  initJobSectionAnimations();

  // Initialize floating icons animations
  initFloatingIconsAnimations();

  // Initialize form animations
  initFormAnimations();

  // Initialize text animations
  initJobDescriptionTextAnimations();

  // Initialize parallax effects
  initJobDescriptionParallax();
}

// Breadcrumb Section Animations
function initBreadcrumbAnimations() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states
  gsap.set(".bottom-banner .banner-text", {
    autoAlpha: 0,
    y: 50,
    scale: 0.9,
    skewY: 3,
    filter: "blur(8px)"
  });

  gsap.set(".bottom-banner ul", {
    autoAlpha: 0,
    y: -30,
    x: 30 * xDir,
    filter: "blur(5px)"
  });

  // Create breadcrumb animation timeline
  const breadcrumbTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".bottom-banner",
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });

  breadcrumbTimeline
    // 1. Breadcrumb navigation slides in from top
    .to(".bottom-banner ul", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    })

    // 2. Main title appears with dramatic effect
    .to(".bottom-banner .banner-text", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.4)"
    }, "-=0.4");
}

// Job Description Section Animations
function initJobSectionAnimations() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states for job description elements
  gsap.set(".container > div:first-child .flex.gap-16 > div", {
    autoAlpha: 0,
    y: 30,
    scale: 0.95,
    filter: "blur(5px)"
  });

  gsap.set(".container > div:first-child h1", {
    autoAlpha: 0,
    y: 50,
    scale: 0.9,
    skewY: 2,
    filter: "blur(8px)"
  });

  gsap.set(".container > div:first-child .bg-\\[\\#F6FAFF\\]", {
    autoAlpha: 0,
    x: -50 * xDir,
    scale: 0.9,
    filter: "blur(5px)"
  });

  gsap.set(".container > div:first-child p", {
    autoAlpha: 0,
    y: 40,
    filter: "blur(6px)"
  });

  // Special styling for contact email
  gsap.set(".container > div:first-child p.font-semibold", {
    autoAlpha: 0,
    scale: 0.9,
    y: 30,
    filter: "blur(5px)"
  });

  gsap.set(".container > div:last-child img", {
    autoAlpha: 0,
    scale: 0.8,
    y: 60,
    rotationY: 15,
    filter: "blur(10px)"
  });

  // Create job section animation timeline
  const jobTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".container.grid.lg\\:grid-cols-2",
      start: "top 70%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });

  jobTimeline
    // 1. Location and availability info appear first
    .to(".container > div:first-child .flex.gap-16 > div", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: {
        amount: 0.3,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.7)"
    })

    // 2. Job title appears with dramatic effect
    .to(".container > div:first-child h1", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.4)"
    }, "-=0.4")

    // 3. Part-time badge slides in
    .to(".container > div:first-child .bg-\\[\\#F6FAFF\\]", {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.7,
      ease: "back.out(1.7)"
    }, "-=0.6")

    // 4. Job description text appears with stagger
    .to(".container > div:first-child p", {
      autoAlpha: 1,
      y: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: {
        amount: 0.5,
        from: "start",
        ease: "power2.out"
      },
      ease: "power2.out"
    }, "-=0.3")

    // 5. Job image appears with 3D effect
    .to(".container > div:last-child img", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.5)"
    }, "-=0.8")

    // 6. Contact email appears with special effect
    .to(".container > div:first-child p.font-semibold", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    }, "-=0.4");
}

// Floating Icons Animations
function initFloatingIconsAnimations() {
  // Apply floating animations to all floating icons
  const floatingIcons = document.querySelectorAll('.floating-icon');

  floatingIcons.forEach((icon, index) => {
    // Initial entrance animation
    gsap.set(icon, {
      autoAlpha: 0,
      scale: 0.5,
      rotation: 45
    });

    gsap.to(icon, {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      duration: 0.8,
      delay: index * 0.1,
      ease: "back.out(1.7)",
      scrollTrigger: {
        trigger: ".bottom-banner",
        start: "top 80%",
        toggleActions: "play none none reverse"
      }
    });

    // Continuous floating animation
    gsap.to(icon, {
      y: "random(-15, 15)",
      x: "random(-10, 10)",
      rotation: "random(-10, 10)",
      duration: "random(3, 6)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: index * 0.2
    });

    // Scale pulsing for dots
    if (icon.classList.contains('rounded-full')) {
      gsap.to(icon, {
        scale: "random(0.8, 1.2)",
        duration: "random(2, 4)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: index * 0.3
      });
    }
  });
}

// Form Animations
function initFormAnimations() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states for form elements
  gsap.set(".lg\\:px-4 h2", {
    autoAlpha: 0,
    y: 30,
    scale: 0.9,
    filter: "blur(5px)"
  });

  gsap.set(".space-y-6 > div", {
    autoAlpha: 0,
    y: 40,
    x: 20 * xDir,
    filter: "blur(6px)"
  });

  gsap.set(".pt-4 button", {
    autoAlpha: 0,
    scale: 0.8,
    y: 30,
    filter: "blur(5px)"
  });

  // Create form animation timeline
  const formTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".lg\\:px-4",
      start: "top 70%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });

  formTimeline
    // 1. Form title appears
    .to(".lg\\:px-4 h2", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    })

    // 2. Form fields appear with stagger
    .to(".space-y-6 > div", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      filter: "blur(0px)",
      duration: 0.7,
      stagger: {
        amount: 0.6,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    }, "-=0.4")

    // 3. Submit button appears
    .to(".pt-4 button", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "back.out(1.7)"
    }, "-=0.3");
}

// Add hover effects for interactive elements
export function initJobDescriptionHoverEffects() {
  // Job title hover effect
  const jobTitle = document.querySelector('.container h1');
  if (jobTitle) {
    jobTitle.addEventListener('mouseenter', () => {
      gsap.to(jobTitle, {
        scale: 1.02,
        y: -2,
        duration: 0.3,
        ease: "power2.out"
      });
    });

    jobTitle.addEventListener('mouseleave', () => {
      gsap.to(jobTitle, {
        scale: 1,
        y: 0,
        duration: 0.3,
        ease: "power2.out"
      });
    });
  }

  // Form inputs hover effects
  const formInputs = document.querySelectorAll('input, select, textarea');
  formInputs.forEach(input => {
    input.addEventListener('focus', () => {
      gsap.to(input, {
        scale: 1.02,
        boxShadow: "0 0 20px rgba(21, 32, 92, 0.2)",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    input.addEventListener('blur', () => {
      gsap.to(input, {
        scale: 1,
        boxShadow: "0 0 0px rgba(21, 32, 92, 0)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  });

  // Submit button hover effect
  const submitButton = document.querySelector('.pt-4 button');
  if (submitButton) {
    submitButton.addEventListener('mouseenter', () => {
      gsap.to(submitButton, {
        scale: 1.05,
        y: -2,
        boxShadow: "0 10px 25px rgba(21, 32, 92, 0.3)",
        duration: 0.3,
        ease: "back.out(1.7)"
      });
    });

    submitButton.addEventListener('mouseleave', () => {
      gsap.to(submitButton, {
        scale: 1,
        y: 0,
        boxShadow: "0 0 0px rgba(21, 32, 92, 0)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  }

  // Location and availability icons hover effects
  const iconContainers = document.querySelectorAll('.flex.gap-4.items-center');
  iconContainers.forEach(container => {
    const icon = container.querySelector('i');
    const text = container.querySelector('p');

    if (icon && text) {
      container.addEventListener('mouseenter', () => {
        gsap.to(icon, {
          scale: 1.2,
          rotation: 10,
          duration: 0.3,
          ease: "back.out(1.7)"
        });

        gsap.to(text, {
          x: 5,
          duration: 0.3,
          ease: "power2.out"
        });
      });

      container.addEventListener('mouseleave', () => {
        gsap.to(icon, {
          scale: 1,
          rotation: 0,
          duration: 0.3,
          ease: "power2.out"
        });

        gsap.to(text, {
          x: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      });
    }
  });

  // Part-time badge hover effect
  const partTimeBadge = document.querySelector('.bg-\\[\\#F6FAFF\\]');
  if (partTimeBadge) {
    partTimeBadge.addEventListener('mouseenter', () => {
      gsap.to(partTimeBadge, {
        scale: 1.05,
        backgroundColor: "#E8F4FD",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    partTimeBadge.addEventListener('mouseleave', () => {
      gsap.to(partTimeBadge, {
        scale: 1,
        backgroundColor: "#F6FAFF",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  }

  // Job image hover effect
  const jobImage = document.querySelector('.container > div:last-child img');
  if (jobImage) {
    jobImage.addEventListener('mouseenter', () => {
      gsap.to(jobImage, {
        scale: 1.02,
        y: -5,
        rotationY: 2,
        filter: "brightness(1.1) contrast(1.1)",
        duration: 0.4,
        ease: "power2.out"
      });
    });

    jobImage.addEventListener('mouseleave', () => {
      gsap.to(jobImage, {
        scale: 1,
        y: 0,
        rotationY: 0,
        filter: "brightness(1) contrast(1)",
        duration: 0.4,
        ease: "power2.out"
      });
    });
  }

  // Contact email hover effect
  const contactEmail = document.querySelector('.container p.font-semibold');
  if (contactEmail) {
    contactEmail.addEventListener('mouseenter', () => {
      gsap.to(contactEmail, {
        scale: 1.05,
        y: -3,
        color: "#DE8545",
        textShadow: "0 2px 10px rgba(222, 133, 69, 0.3)",
        duration: 0.3,
        ease: "back.out(1.7)"
      });
    });

    contactEmail.addEventListener('mouseleave', () => {
      gsap.to(contactEmail, {
        scale: 1,
        y: 0,
        color: "#184385",
        textShadow: "0 0 0px rgba(222, 133, 69, 0)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  }
}

// Add scroll-triggered text reveal animations
export function initJobDescriptionTextAnimations() {
  // Split long text into spans for word-by-word animation
  const jobDescription = document.querySelector('.text-\\[\\#818181\\].text-lg.leading-10');
  if (jobDescription) {
    const spans = jobDescription.querySelectorAll('span');

    if (spans.length > 0) {
      // Set initial state for text spans
      gsap.set(spans, {
        autoAlpha: 0,
        y: 20,
        filter: "blur(3px)"
      });

      // Create staggered text reveal animation
      gsap.to(spans, {
        autoAlpha: 1,
        y: 0,
        filter: "blur(0px)",
        duration: 0.6,
        stagger: {
          amount: 1.5,
          from: "start",
          ease: "power2.out"
        },
        ease: "power2.out",
        scrollTrigger: {
          trigger: jobDescription,
          start: "top 80%",
          end: "bottom 20%",
          toggleActions: "play none none reverse"
        }
      });
    }
  }
}

// Add parallax effect to background elements
export function initJobDescriptionParallax() {
  // Parallax effect for floating icons
  const floatingIcons = document.querySelectorAll('.floating-icon');

  floatingIcons.forEach((icon, index) => {
    const speed = 0.5 + (index % 3) * 0.2; // Different speeds for variety

    gsap.to(icon, {
      y: `${-50 * speed}px`,
      ease: "none",
      scrollTrigger: {
        trigger: ".bottom-banner",
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });
  });

  // Parallax effect for the dotted line
  const dottedLine = document.querySelector('.bottom-banner img[alt="Dotted line"]');
  if (dottedLine) {
    gsap.to(dottedLine, {
      x: "20px",
      rotation: 2,
      ease: "none",
      scrollTrigger: {
        trigger: ".bottom-banner",
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });
  }
}
