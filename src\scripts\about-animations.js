import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// About Page Animations
export function initAboutAnimations() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Initialize all section animations
  initBreadcrumbsAnimation();
  initSubHeaderAnimation();
  initBusinessEasierAnimation();
  initBusinessLinksAnimation();
  initFeaturesAnimation();
  initPinHoleAnimation();
  initVisionCardTiltAnimation();
  initRocketAnimation();
  initEnvelopeAnimation();
  initFutureVisionAnimation();
  // Add more section initializations as needed
}

// 1. Breadcrumbs Animation
function initBreadcrumbsAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states
  gsap.set(".breadcrumbs-section ul", {
    autoAlpha: 0,
    x: -30 * xDir,
    filter: "blur(4px)"
  });

  gsap.set(".breadcrumbs-section p", {
    autoAlpha: 0,
    y: 30,
    scale: 0.9,
    skewY: 5,
    filter: "blur(6px)"
  });

  gsap.set(".breadcrumbs-section .floating-icon", {
    autoAlpha: 0,
    scale: 0.6,
    filter: "blur(4px)"
  });

  // Animation timeline
  const breadcrumbsTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".breadcrumbs-section",
      start: "top 80%",
      toggleActions: "play none none reverse"
    }
  });

  breadcrumbsTl
    .to(".breadcrumbs-section ul", {
      autoAlpha: 1,
      x: 0,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "power2.out"
    })
    .to(".breadcrumbs-section p", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    }, "-=0.3")
    .to(".breadcrumbs-section .floating-icon", {
      autoAlpha: 1,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: 0.1,
      ease: "back.out(1.4)"
    }, "-=0.6");

  // Start floating animations after entrance
  setTimeout(() => {
    const floatingIcons = document.querySelectorAll('.breadcrumbs-section .floating-icon');

    floatingIcons.forEach((icon, index) => {
      gsap.to(icon, {
        y: "random(-20, 20)",
        x: "random(-15, 15)",
        rotation: "random(-10, 10)",
        duration: "random(2, 4)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: index * 0.2
      });
    });
  }, 2000);
}

// 2. Sub Header Animation with enhanced effects
function initSubHeaderAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Add a class to the sub header for easier targeting
  const subHeader = document.querySelector('section.flex.flex-col.lg\\:flex-row.gap-5.items-center.justify-between.container.px-8.py-6.rounded-md.bg-secondary-blue.text-white');
  if (subHeader) {
    subHeader.classList.add('about-sub-header');
  }

  // Set initial states
  gsap.set(".about-sub-header p", {
    autoAlpha: 0,
    x: -50 * xDir,
    scale: 0.9,
    filter: "blur(4px)"
  });

  gsap.set(".about-sub-header ul li", {
    autoAlpha: 0,
    y: 20,
    scale: 0.8,
    filter: "blur(3px)"
  });

  gsap.set(".about-sub-header button", {
    autoAlpha: 0,
    x: 50 * xDir,
    scale: 0.8,
    filter: "blur(4px)"
  });

  // Animation timeline
  const subHeaderTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".about-sub-header",
      start: "top 80%",
      toggleActions: "play none none reverse"
    }
  });

  subHeaderTl
    .to(".about-sub-header p", {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "back.out(1.4)"
    })
    .to(".about-sub-header ul li", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.4,
      stagger: 0.1,
      ease: "power2.out"
    }, "-=0.3")
    .to(".about-sub-header button", {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "back.out(1.4)"
    }, "-=0.2");
}

// 3. Business Easier Section Animation
function initBusinessEasierAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states for heading
  gsap.set(".business-heading", {
    autoAlpha: 0,
    y: 50,
    x: -30 * xDir,
    scale: 0.9,
    skewY: 3,
    filter: "blur(6px)"
  });

  // Set initial states for description
  gsap.set(".business-description", {
    autoAlpha: 0,
    y: 40,
    scale: 0.95,
    filter: "blur(4px)"
  });

  // Animation timeline
  const businessEasierTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".business-heading",
      start: "top 80%",
      toggleActions: "play none none reverse"
    }
  });

  businessEasierTl
    .to(".business-heading", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    })
    .to(".business-description", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4");
}

// 4. Business Links Animation
function initBusinessLinksAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Get the business links list and items
  const businessLinksList = document.querySelector('.business-links-list');
  const businessLinkItems = document.querySelectorAll('.business-link-item');

  if (!businessLinksList || businessLinkItems.length === 0) return;

  // Set initial states for the list items
  gsap.set(businessLinkItems, {
    autoAlpha: 0,
    x: 50 * xDir,
    y: 20,
    scale: 0.9,
    skewX: 2 * skewXDir,
    filter: "blur(4px)"
  });

  // Create a staggered entrance animation
  const businessLinksTl = gsap.timeline({
    scrollTrigger: {
      trigger: businessLinksList,
      start: "top 85%",
      toggleActions: "play none none reverse"
    }
  });

  businessLinksTl.to(businessLinkItems, {
    autoAlpha: 1,
    x: 0,
    y: 0,
    scale: 1,
    skewX: 0,
    filter: "blur(0px)",
    duration: 0.6,
    stagger: 0.12, // Staggered timing for each item
    ease: "back.out(1.4)"
  });
}

// 5. Features Section Animation
function initFeaturesAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Get elements
  const featuresTitle = document.querySelector('.features-title');
  const featuresSubtitle = document.querySelector('.features-subtitle');
  const featureItems = document.querySelectorAll('.feature-item');

  if (!featuresTitle || !featuresSubtitle || featureItems.length === 0) return;

  // Set initial states for title
  gsap.set(featuresTitle, {
    autoAlpha: 0,
    y: 60,
    x: -40 * xDir,
    scale: 0.9,
    skewY: 4,
    filter: "blur(8px)"
  });

  // Set initial states for subtitle
  gsap.set(featuresSubtitle, {
    autoAlpha: 0,
    y: 30,
    scale: 0.95,
    filter: "blur(4px)"
  });

  // Set initial states for feature items
  gsap.set(featureItems, {
    autoAlpha: 0,
    y: 40,
    x: 30 * xDir,
    scale: 0.9,
    skewX: 3 * skewXDir,
    filter: "blur(6px)"
  });

  // Animation timeline
  const featuresTl = gsap.timeline({
    scrollTrigger: {
      trigger: featuresTitle,
      start: "top 80%",
      toggleActions: "play none none reverse"
    }
  });

  featuresTl
    .to(featuresTitle, {
      autoAlpha: 1,
      y: 0,
      x: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    })
    .to(featuresSubtitle, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")
    .to(featureItems, {
      autoAlpha: 1,
      y: 0,
      x: 0,
      scale: 1,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.7,
      stagger: 0.1,
      ease: "back.out(1.2)"
    }, "-=0.3");
}

// 6. Pin Hole Animation
function initPinHoleAnimation() {
  const pinContainers = document.querySelectorAll('.pin-container');

  pinContainers.forEach(container => {
    const pinImage = container.querySelector('.pin-image');
    const pinHole = container.querySelector('.pin-hole');

    if (!pinImage || !pinHole) return;

    // Set initial states
    gsap.set(pinImage, {
      autoAlpha: 0,
      y: -20,
      scale: 0.8,
      rotation: -10
    });

    gsap.set(pinHole, {
      autoAlpha: 0,
      scale: 0.5
    });

    // Create entrance animation
    const pinTl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    });

    pinTl
      .to(pinHole, {
        autoAlpha: 1,
        scale: 1,
        duration: 0.4,
        ease: "power2.out"
      })
      .to(pinImage, {
        autoAlpha: 1,
        y: 0,
        scale: 1,
        rotation: 0,
        duration: 0.6,
        ease: "back.out(1.7)"
      }, "-=0.2");

    // Add hover interactions
    let hoverTl = null;

    container.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(pinImage, {
          y: -8,
          rotation: 5,
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(pinHole, {
          scale: 1.1,
          boxShadow: "inset 0 0 15px rgba(0, 0, 0, 0.9), 0 0 8px rgba(0, 0, 0, 0.4)",
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });

    container.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(pinImage, {
          y: 0,
          rotation: 0,
          scale: 1,
          duration: 0.4,
          ease: "power2.out"
        })
        .to(pinHole, {
          scale: 1,
          boxShadow: "inset 0 0 10px rgba(0, 0, 0, 0.8), 0 0 5px rgba(0, 0, 0, 0.3)",
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3");
    });

    // Add subtle floating animation
    gsap.to(pinImage, {
      y: "random(-2, 2)",
      rotation: "random(-1, 1)",
      duration: "random(3, 5)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: Math.random() * 2
    });
  });
}

// 7. Vision Card Tilt Animation
function initVisionCardTiltAnimation() {
  const visionCards = document.querySelectorAll('.vision-card');

  visionCards.forEach((card, index) => {
    // Set transform origin to top center
    gsap.set(card, {
      transformOrigin: "top center"
    });

    // Create continuous tilting animation
    const tiltTl = gsap.timeline({ repeat: -1 });

    tiltTl
      .to(card, {
        rotationZ: -3, // Left corner up, right corner down
        duration: 2,
        ease: "sine.inOut"
      })
      .to(card, {
        rotationZ: 3, // Right corner up, left corner down
        duration: 2,
        ease: "sine.inOut"
      })
      .to(card, {
        rotationZ: 0, // Back to center
        duration: 2,
        ease: "sine.inOut"
      });

    // Add slight delay for each card to create variation
    tiltTl.delay(index * 0.5);

    // Pause animation on hover for better UX
    let isPaused = false;

    card.addEventListener('mouseenter', () => {
      if (!isPaused) {
        tiltTl.pause();
        isPaused = true;

        // Smoothly return to center position on hover
        gsap.to(card, {
          rotationZ: 0,
          duration: 0.5,
          ease: "power2.out"
        });
      }
    });

    card.addEventListener('mouseleave', () => {
      if (isPaused) {
        tiltTl.resume();
        isPaused = false;
      }
    });

    // Add entrance animation
    gsap.set(card, {
      autoAlpha: 0,
      y: 30,
      scale: 0.95
    });

    gsap.to(card, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      duration: 0.8,
      ease: "back.out(1.4)",
      delay: index * 0.2,
      scrollTrigger: {
        trigger: card,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    });
  });
}

// 8. Rocket Animation
function initRocketAnimation() {
  const rocketContainers = document.querySelectorAll('.rocket-container');

  rocketContainers.forEach((container, index) => {
    const rocketSvg = container.querySelector('.rocket-svg');
    const rocketNose = container.querySelector('.rocket-nose');
    const rocketBody = container.querySelector('.rocket-body');
    const rocketWindow = container.querySelector('.rocket-window-outer');
    const rocketFlame1 = container.querySelector('.rocket-flame-1');
    const rocketFlame2 = container.querySelector('.rocket-flame-2');
    const rocketExhaustCloud = container.querySelector('.rocket-exhaust-cloud');
    const rocketFins = container.querySelectorAll('[class*="rocket-fin"]');

    if (!rocketSvg) return;

    // Set initial states
    gsap.set(rocketSvg, {
      autoAlpha: 0,
      y: 50,
      x: -20,
      scale: 0.8,
      rotation: -15,
      filter: "blur(4px)"
    });

    // Set initial states for individual parts
    gsap.set([rocketFlame1, rocketFlame2], {
      scaleY: 0,
      transformOrigin: "top center",
      autoAlpha: 0
    });

    gsap.set(rocketExhaustCloud, {
      scaleX: 0,
      transformOrigin: "left center",
      autoAlpha: 0
    });

    gsap.set(rocketWindow, {
      fill: "#ffffff"
    });

    // Create entrance animation
    const rocketEntranceTl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    });

    rocketEntranceTl
      .to(rocketSvg, {
        autoAlpha: 1,
        y: 0,
        x: 0,
        scale: 1,
        rotation: 0,
        filter: "blur(0px)",
        duration: 1.2,
        ease: "back.out(1.4)"
      })
      .to([rocketFlame1, rocketFlame2], {
        autoAlpha: 1,
        scaleY: 1,
        duration: 0.6,
        ease: "power2.out",
        stagger: 0.1
      }, "-=0.8")
      .to(rocketExhaustCloud, {
        autoAlpha: 1,
        scaleX: 1,
        duration: 0.8,
        ease: "power2.out"
      }, "-=0.6")
      .to(rocketWindow, {
        fill: "#87CEEB",
        duration: 0.3,
        ease: "power2.out"
      }, "-=0.4");

    // Start continuous animations after entrance
    setTimeout(() => {
      // Floating rocket animation
      gsap.to(rocketSvg, {
        y: "random(-8, 8)",
        x: "random(-5, 5)",
        rotation: "random(-3, 3)",
        duration: "random(3, 5)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: index * 0.5
      });

      // Flickering flames animation
      gsap.to([rocketFlame1, rocketFlame2], {
        scaleY: "random(0.8, 1.2)",
        scaleX: "random(0.9, 1.1)",
        duration: "random(0.3, 0.8)",
        repeat: -1,
        yoyo: true,
        ease: "power2.inOut",
        stagger: 0.1
      });

      // Pulsing exhaust cloud
      gsap.to(rocketExhaustCloud, {
        scaleX: "random(0.9, 1.1)",
        scaleY: "random(0.95, 1.05)",
        autoAlpha: "random(0.7, 1)",
        duration: "random(1, 2)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

      // Window glow effect
      gsap.to(rocketWindow, {
        fill: "#ffffff",
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

    }, 2000 + (index * 500));

    // Hover effects
    let hoverTl = null;

    container.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(rocketSvg, {
          scale: 1.1,
          y: -10,
          rotation: 5,
          duration: 0.4,
          ease: "power2.out"
        })
        .to([rocketFlame1, rocketFlame2], {
          scaleY: 1.5,
          fill: "#ff4444",
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to(rocketExhaustCloud, {
          scaleX: 1.3,
          autoAlpha: 0.9,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to(rocketWindow, {
          fill: "#ffff00",
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });

    container.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(rocketSvg, {
          scale: 1,
          y: 0,
          rotation: 0,
          duration: 0.5,
          ease: "power2.out"
        })
        .to([rocketFlame1, rocketFlame2], {
          scaleY: 1,
          fill: "#FF7F6E",
          duration: 0.4,
          ease: "power2.out"
        }, "-=0.4")
        .to(rocketExhaustCloud, {
          scaleX: 1,
          autoAlpha: 1,
          duration: 0.4,
          ease: "power2.out"
        }, "-=0.4")
        .to(rocketWindow, {
          fill: "#87CEEB",
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3");
    });
  });
}

// 9. Envelope Animation
function initEnvelopeAnimation() {
  const envelopeContainers = document.querySelectorAll('.envelope-container');

  envelopeContainers.forEach((container, index) => {
    const envelopeSvg = container.querySelector('.envelope-svg');
    const envelopeBackground = container.querySelector('.envelope-background');
    const envelopeBody = container.querySelector('.envelope-body');
    const envelopeFlapTop = container.querySelector('.envelope-flap-top');
    const envelopeFlapLeft = container.querySelector('.envelope-flap-left');
    const envelopeFlapRight = container.querySelector('.envelope-flap-right');
    const envelopeStamp = container.querySelector('.envelope-stamp');

    if (!envelopeSvg) return;

    // Set initial states
    gsap.set(envelopeSvg, {
      autoAlpha: 0,
      y: 30,
      x: 20,
      scale: 0.9,
      rotation: 5,
      filter: "blur(3px)"
    });

    // Set initial states for envelope parts
    gsap.set(envelopeBackground, {
      scale: 0.8,
      autoAlpha: 0.5
    });

    gsap.set(envelopeBody, {
      autoAlpha: 0.8
    });

    gsap.set([envelopeFlapLeft, envelopeFlapRight], {
      transformOrigin: "center bottom",
      scaleY: 0.3,
      autoAlpha: 0.7
    });

    gsap.set(envelopeFlapTop, {
      transformOrigin: "center top",
      rotationX: -45,
      autoAlpha: 0.8
    });

    gsap.set(envelopeStamp, {
      scale: 0,
      rotation: -15,
      autoAlpha: 0
    });

    // Create entrance animation
    const envelopeEntranceTl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    });

    envelopeEntranceTl
      .to(envelopeSvg, {
        autoAlpha: 1,
        y: 0,
        x: 0,
        scale: 1,
        rotation: 0,
        filter: "blur(0px)",
        duration: 1.0,
        ease: "back.out(1.4)"
      })
      .to(envelopeBackground, {
        scale: 1,
        autoAlpha: 1,
        duration: 0.6,
        ease: "power2.out"
      }, "-=0.8")
      .to(envelopeBody, {
        autoAlpha: 1,
        duration: 0.4,
        ease: "power2.out"
      }, "-=0.6")
      .to([envelopeFlapLeft, envelopeFlapRight], {
        scaleY: 1,
        autoAlpha: 1,
        duration: 0.5,
        stagger: 0.1,
        ease: "back.out(1.4)"
      }, "-=0.4")
      .to(envelopeFlapTop, {
        rotationX: 0,
        autoAlpha: 1,
        duration: 0.6,
        ease: "back.out(1.4)"
      }, "-=0.3")
      .to(envelopeStamp, {
        scale: 1,
        rotation: 0,
        autoAlpha: 1,
        duration: 0.4,
        ease: "back.out(1.7)"
      }, "-=0.2");

    // Start continuous animations after entrance
    setTimeout(() => {
      // Floating envelope animation
      gsap.to(envelopeSvg, {
        y: "random(-6, 6)",
        x: "random(-4, 4)",
        rotation: "random(-2, 2)",
        duration: "random(4, 6)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: index * 0.3
      });

      // Flap subtle movement
      gsap.to(envelopeFlapTop, {
        rotationX: "random(-5, 5)",
        duration: "random(3, 5)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

      // Stamp subtle rotation
      gsap.to(envelopeStamp, {
        rotation: "random(-3, 3)",
        scale: "random(0.98, 1.02)",
        duration: "random(2, 4)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

    }, 2500 + (index * 300));

    // Hover effects
    let hoverTl = null;

    container.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(envelopeSvg, {
          scale: 1.05,
          y: -8,
          rotation: -3,
          duration: 0.4,
          ease: "power2.out"
        })
        .to(envelopeFlapTop, {
          rotationX: -20,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to([envelopeFlapLeft, envelopeFlapRight], {
          scaleY: 0.9,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2")
        .to(envelopeStamp, {
          scale: 1.1,
          rotation: 5,
          duration: 0.2,
          ease: "back.out(1.7)"
        }, "-=0.2");
    });

    container.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(envelopeSvg, {
          scale: 1,
          y: 0,
          rotation: 0,
          duration: 0.5,
          ease: "power2.out"
        })
        .to(envelopeFlapTop, {
          rotationX: 0,
          duration: 0.4,
          ease: "power2.out"
        }, "-=0.4")
        .to([envelopeFlapLeft, envelopeFlapRight], {
          scaleY: 1,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to(envelopeStamp, {
          scale: 1,
          rotation: 0,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3");
    });
  });
}

// Add hover effects for the sub header and business links
export function initAboutHoverEffects() {
  // Sub header navigation items hover effect
  const navItems = document.querySelectorAll('.about-sub-header ul li');

  navItems.forEach(item => {
    let hoverTl = null;

    item.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(item, {
          y: -3,
          scale: 1.05,
          color: "#DE8545", // primary-yellow color
          duration: 0.3,
          ease: "power2.out"
        });
    });

    item.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(item, {
          y: 0,
          scale: 1,
          color: "", // Reset to default
          duration: 0.3,
          ease: "power2.out"
        });
    });
  });

  // Button hover effect
  const button = document.querySelector('.about-sub-header button');

  if (button) {
    let hoverTl = null;

    button.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(button, {
          scale: 1.05,
          duration: 0.3,
          ease: "back.out(1.7)"
        })
        .to(button.querySelector('i'), {
          x: -5, // Move left in RTL
          scale: 1.2,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });

    button.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(button, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(button.querySelector('i'), {
          x: 0,
          scale: 1,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });
  }

  // Business links hover effects
  const businessLinkItems = document.querySelectorAll('.business-link-item');

  businessLinkItems.forEach(item => {
    let hoverTl = null;
    const linkIcon = item.querySelector('.link-icon');
    const linkText = item.querySelector('.link-text');
    const iconElement = item.querySelector('i');

    item.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(item, {
          backgroundColor: "rgba(21, 32, 92, 0.05)",
          borderColor: "#15205C",
          boxShadow: "0 8px 20px rgba(21, 32, 92, 0.15)",
          x: 5,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(linkText, {
          color: "#15205C",
          fontWeight: "600",
          x: 5,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2")
        .to(linkIcon, {
          backgroundColor: "#DE8545",
          scale: 1.1,
          duration: 0.2,
          ease: "back.out(1.7)"
        }, "-=0.2")
        .to(iconElement, {
          rotation: -90,
          duration: 0.3,
          ease: "power1.out"
        }, "-=0.2");
    });

    item.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(item, {
          backgroundColor: "transparent",
          borderColor: "#15205C",
          boxShadow: "none",
          x: 0,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(linkText, {
          color: "#191C1F",
          fontWeight: "400",
          x: 0,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2")
        .to(linkIcon, {
          backgroundColor: "#15205C",
          scale: 1,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2")
        .to(iconElement, {
          rotation: -45,
          duration: 0.3,
          ease: "power1.out"
        }, "-=0.2");
    });
  });
}

// 10. Future Vision Animation
function initFutureVisionAnimation() {
  const futureVisionContainers = document.querySelectorAll('.future-vision-container');

  futureVisionContainers.forEach((container, index) => {
    const futureVisionSvg = container.querySelector('.future-vision-svg');
    const personFace = container.querySelector('.person-face');
    const personHair = container.querySelector('.person-hair');
    const visionBubbleOuter = container.querySelector('.vision-bubble-outer');
    const visionBubbleInner = container.querySelector('.vision-bubble-inner');
    const visionEye = container.querySelector('.vision-eye');

    if (!futureVisionSvg) return;

    // Set initial states
    gsap.set(futureVisionSvg, {
      autoAlpha: 0,
      y: 40,
      x: -15,
      scale: 0.85,
      rotation: 8,
      filter: "blur(5px)"
    });

    // Set initial states for person parts
    gsap.set(personFace, {
      autoAlpha: 0.7,
      scale: 0.9
    });

    gsap.set(personHair, {
      autoAlpha: 0.8,
      y: -5
    });

    // Set initial states for vision bubble
    gsap.set(visionBubbleOuter, {
      autoAlpha: 0,
      scale: 0.3,
      transformOrigin: "center center"
    });

    gsap.set(visionBubbleInner, {
      autoAlpha: 0,
      scale: 0.5,
      transformOrigin: "center center"
    });

    gsap.set(visionEye, {
      autoAlpha: 0,
      scale: 0,
      rotation: 180
    });

    // Create entrance animation
    const futureVisionEntranceTl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    });

    futureVisionEntranceTl
      .to(futureVisionSvg, {
        autoAlpha: 1,
        y: 0,
        x: 0,
        scale: 1,
        rotation: 0,
        filter: "blur(0px)",
        duration: 1.2,
        ease: "back.out(1.4)"
      })
      .to(personFace, {
        autoAlpha: 1,
        scale: 1,
        duration: 0.6,
        ease: "power2.out"
      }, "-=0.8")
      .to(personHair, {
        autoAlpha: 1,
        y: 0,
        duration: 0.5,
        ease: "power2.out"
      }, "-=0.6")
      .to(visionBubbleOuter, {
        autoAlpha: 1,
        scale: 1,
        duration: 0.8,
        ease: "back.out(1.7)"
      }, "-=0.4")
      .to(visionBubbleInner, {
        autoAlpha: 1,
        scale: 1,
        duration: 0.6,
        ease: "back.out(1.4)"
      }, "-=0.5")
      .to(visionEye, {
        autoAlpha: 1,
        scale: 1,
        rotation: 0,
        duration: 0.5,
        ease: "back.out(1.7)"
      }, "-=0.3");

    // Start continuous animations after entrance
    setTimeout(() => {
      // Floating person animation
      gsap.to(futureVisionSvg, {
        y: "random(-6, 6)",
        x: "random(-4, 4)",
        rotation: "random(-2, 2)",
        duration: "random(4, 6)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: index * 0.4
      });

      // Vision bubble pulsing
      gsap.to(visionBubbleOuter, {
        scale: "random(0.98, 1.02)",
        duration: "random(2, 3)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

      gsap.to(visionBubbleInner, {
        scale: "random(0.95, 1.05)",
        duration: "random(1.5, 2.5)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

      // Eye blinking/glowing effect
      gsap.to(visionEye, {
        scale: "random(0.9, 1.1)",
        rotation: "random(-5, 5)",
        duration: "random(1, 2)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

      // Hair subtle movement
      gsap.to(personHair, {
        y: "random(-2, 2)",
        duration: "random(3, 5)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut"
      });

    }, 3000 + (index * 400));

    // Hover effects
    let hoverTl = null;

    container.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(futureVisionSvg, {
          scale: 1.08,
          y: -10,
          rotation: -2,
          duration: 0.4,
          ease: "power2.out"
        })
        .to(visionBubbleOuter, {
          scale: 1.15,
          duration: 0.3,
          ease: "back.out(1.7)"
        }, "-=0.3")
        .to(visionBubbleInner, {
          scale: 1.2,
          duration: 0.3,
          ease: "back.out(1.7)"
        }, "-=0.2")
        .to(visionEye, {
          scale: 1.3,
          fill: "#FFD700",
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2")
        .to(personFace, {
          fill: "#FFD0B6",
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });

    container.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(futureVisionSvg, {
          scale: 1,
          y: 0,
          rotation: 0,
          duration: 0.5,
          ease: "power2.out"
        })
        .to(visionBubbleOuter, {
          scale: 1,
          duration: 0.4,
          ease: "power2.out"
        }, "-=0.4")
        .to(visionBubbleInner, {
          scale: 1,
          duration: 0.4,
          ease: "power2.out"
        }, "-=0.3")
        .to(visionEye, {
          scale: 1,
          fill: "#FFA800",
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to(personFace, {
          fill: "#FFC5A6",
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3");
    });
  });
}
