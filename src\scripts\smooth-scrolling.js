// Function to initialize smooth scrolling
export function initSmoothScrolling() {
    // Get all links that have hash (#) in their href
    const links = document.querySelectorAll('a[href^="#"]');

    links.forEach((link) => {
        link.addEventListener("click", function (e) {
            // Only prevent default if the href is not just "#"
            if (this.getAttribute("href") !== "#") {
                e.preventDefault();

                const targetId = this.getAttribute("href");
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    // Close mobile menu if it's open
                    const mobileMenu = document.getElementById("mobileMenu");
                    const mobileMenuOverlay = document.getElementById("mobileMenuOverlay");
                    const burgerBtn = document.getElementById("burgerMenuBtn");

                    if (mobileMenu && mobileMenuOverlay) {
                        // Check if menu is open by checking overlay opacity
                        const overlayOpacity = window.getComputedStyle(mobileMenuOverlay).opacity;
                        if (overlayOpacity !== "0") {
                            // Trigger close menu by clicking the burger button
                            if (burgerBtn) {
                                // Simulate close by dispatching a custom event
                                const closeEvent = new CustomEvent('closeMobileMenu');
                                document.dispatchEvent(closeEvent);
                            }
                        }
                    }

                    // Scroll to the target element
                    targetElement.scrollIntoView({
                        behavior: "smooth",
                        block: "start",
                    });

                    // Update URL hash without jumping
                    history.pushState(null, null, targetId);
                }
            }
        });
    });
}
