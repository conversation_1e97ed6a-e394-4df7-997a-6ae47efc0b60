import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Footer Animations with ScrollTrigger + GSAP
export function initFooterGSAP() {
  // Feature flag to disable footer animations if needed
  const ENABLE_FOOTER_ANIMATIONS = true;

  if (!ENABLE_FOOTER_ANIMATIONS) {
    return;
  }

  // Register ScrollTrigger
  gsap.registerPlugin(ScrollTrigger);

  // Get footer elements
  const footer = document.querySelector('footer');

  if (!footer) {
    return;
  }

  // Get all animatable elements in footer with safer selectors
  const logoSection = footer.querySelector('.space-y-5');
  const footerColumns = footer.querySelectorAll('.flex.flex-col');
  const projectImages = footer.querySelectorAll('img[class*="w-[70px]"]');
  const socialIcons = footer.querySelectorAll('a[class*="hover:scale-105"], button[class*="hover:scale-105"]');



  // Set initial states for all footer elements with error handling
  // Use only opacity and scale to avoid any space/overflow issues
  try {
    gsap.set(footer, {
      autoAlpha: 0,
      scale: 0.95
    });

    if (logoSection) {
      gsap.set(logoSection, {
        autoAlpha: 0,
        scale: 0.9
      });
    }

    if (footerColumns.length > 0) {
      gsap.set(footerColumns, {
        autoAlpha: 0,
        scale: 0.95
      });
    }

    if (projectImages.length > 0) {
      gsap.set(projectImages, {
        autoAlpha: 0,
        scale: 0.8,
        rotation: -10
      });
    }
  } catch (error) {
    console.warn('Footer animation setup error:', error);
    return; // Exit if there's an error setting up animations
  }

  // ScrollTrigger for footer animations with error handling
  try {
    // Main footer animation with ScrollTrigger
    if (footer) {
      gsap.to(footer, {
        autoAlpha: 1,
        scale: 1,
        duration: 0.8,
        ease: "power2.out",
        scrollTrigger: {
          trigger: footer,
          start: "top 85%",
          toggleActions: "play none none reverse"
        }
      });
    }

    // Logo section animation
    if (logoSection) {
      gsap.to(logoSection, {
        autoAlpha: 1,
        scale: 1,
        duration: 0.8,
        ease: "back.out(1.7)",
        scrollTrigger: {
          trigger: footer,
          start: "top 80%",
          toggleActions: "play none none reverse"
        }
      });
    }

    // Staggered column animations
    if (footerColumns.length > 0) {
      gsap.to(footerColumns, {
        autoAlpha: 1,
        scale: 1,
        duration: 0.6,
        stagger: 0.15,
        ease: "power2.out",
        scrollTrigger: {
          trigger: footer,
          start: "top 75%",
          toggleActions: "play none none reverse"
        }
      });
    }

    // Project images animation
    if (projectImages.length > 0) {
      gsap.to(projectImages, {
        autoAlpha: 1,
        scale: 1,
        rotation: 0,
        duration: 0.5,
        stagger: 0.1,
        ease: "back.out(1.7)",
        scrollTrigger: {
          trigger: footer,
          start: "top 70%",
          toggleActions: "play none none reverse"
        }
      });
    }
  } catch (error) {
    console.warn('Footer ScrollTrigger setup error:', error);
    // Fallback: just show the footer without animation
    if (footer) {
      gsap.set(footer, { autoAlpha: 1, scale: 1 });
    }
  }



  // Add simple hover effects for interactive elements with error handling
  try {
    if (socialIcons.length > 0) {
      socialIcons.forEach(icon => {
        if (icon) {
          icon.addEventListener('mouseenter', () => {
            try {
              gsap.to(icon, {
                scale: 1.15,
                rotation: 5,
                duration: 0.3,
                ease: "power2.out"
              });
            } catch (error) {
              console.warn('Social icon hover in error:', error);
            }
          });

          icon.addEventListener('mouseleave', () => {
            try {
              gsap.to(icon, {
                scale: 1,
                rotation: 0,
                duration: 0.3,
                ease: "power2.out"
              });
            } catch (error) {
              console.warn('Social icon hover out error:', error);
            }
          });
        }
      });
    }

    // Newsletter input focus animation
    const newsletterInput = footer.querySelector('#newsletter-email');
    if (newsletterInput) {
      newsletterInput.addEventListener('focus', () => {
        try {
          gsap.to(newsletterInput, {
            scale: 1.02,
            duration: 0.3,
            ease: "power2.out"
          });
        } catch (error) {
          console.warn('Newsletter input focus error:', error);
        }
      });

      newsletterInput.addEventListener('blur', () => {
        try {
          gsap.to(newsletterInput, {
            scale: 1,
            duration: 0.3,
            ease: "power2.out"
          });
        } catch (error) {
          console.warn('Newsletter input blur error:', error);
        }
      });
    }
  } catch (error) {
    console.warn('Footer interactive elements error:', error);
  }

}

// Alternative simple footer animation (fallback)
export function initSimpleFooterGSAP() {
  // Register ScrollTrigger
  gsap.registerPlugin(ScrollTrigger);

  const footer = document.querySelector('footer');

  if (!footer) {
    return;
  }

  try {
    // Simple fade-in animation on scroll - no transform to avoid scrollbar
    gsap.set(footer, { autoAlpha: 0 });

    gsap.to(footer, {
      autoAlpha: 1,
      duration: 1,
      ease: "power2.out",
      scrollTrigger: {
        trigger: footer,
        start: "top 90%",
        toggleActions: "play none none reverse"
      }
    });
  } catch (error) {
    console.warn('Simple footer animation error:', error);
    // Ensure footer is visible as fallback
    if (footer) {
      footer.style.opacity = '1';
    }
  }
}
