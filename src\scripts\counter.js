// Counter animation functionality
export function initCounterAnimation() {
  const counters = document.querySelectorAll('.counter');
  
  // Function to animate a single counter
  function animateCounter(counter) {
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const increment = target / (duration / 16); // 60fps
    let current = 0;
    
    const timer = setInterval(() => {
      current += increment;
      if (current >= target) {
        current = target;
        clearInterval(timer);
      }
      counter.textContent = Math.floor(current);
    }, 16);
  }
  
  // Create intersection observer to trigger animation when element is visible
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // Add a delay to start after the AOS animation completes
        setTimeout(() => {
          animateCounter(entry.target);
        }, 500); // Start counter 500ms after element becomes visible
        
        // Stop observing this element
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.5 // Trigger when 50% of element is visible
  });
  
  // Observe all counter elements
  counters.forEach(counter => {
    observer.observe(counter);
  });
}

// Alternative approach using AOS events
export function initCounterWithAOS() {
  // Listen for AOS animation completion
  document.addEventListener('aos:in', ({ detail }) => {
    const element = detail;
    const counter = element.querySelector('.counter');
    
    if (counter && !counter.classList.contains('animated')) {
      counter.classList.add('animated');
      
      const target = parseInt(counter.getAttribute('data-target'));
      const duration = 2000;
      const increment = target / (duration / 16);
      let current = 0;
      
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        counter.textContent = Math.floor(current);
      }, 16);
    }
  });
}
