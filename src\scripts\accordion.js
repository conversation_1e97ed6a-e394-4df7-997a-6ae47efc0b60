import { gsap } from "gsap";

// Accordion functionality with smooth animations
export function initAccordion() {
  // Get all accordion items
  const accordionItems = document.querySelectorAll('.accordion-item');


  if (accordionItems.length === 0) {
    return;
  }

  // Initialize all accordions as closed
  accordionItems.forEach((item, index) => {
    const header = item.querySelector('.accordion-header');
    const body = item.querySelector('.accordion-body');
    const icon = item.querySelector('.accordion-icon');

    if (!header || !body || !icon) {
      return;
    }

    // Set initial closed state
    gsap.set(body, {
      height: 0,
      autoAlpha: 0,
      overflow: 'hidden'
    });

    // Set initial icon state (plus for closed)
    icon.className = 'fa-solid fa-plus text-primary-blue accordion-icon';

    // Add click event listener
    header.addEventListener('click', () => {
      toggleAccordion(item, header, body, icon);
    });

  });

  // Function to toggle accordion state
  function toggleAccordion(item, header, body, icon) {
    const isOpen = item.classList.contains('accordion-open');

    if (isOpen) {
      // Close accordion
      closeAccordion(item, header, body, icon);
    } else {
      // Check if we're on large screens (lg and above)
      const isLargeScreen = window.innerWidth >= 1024;

      if (isLargeScreen) {
        // On large screens, only close accordion in the same row
        closeAccordionsInSameColumn(item);
      } else {
        // On smaller screens, close all other accordions
        closeAllAccordions();
      }

      // Then open this one
      openAccordion(item, header, body, icon);
    }
  }

  // Function to open accordion
  function openAccordion(item, header, body, icon) {
    const timeline = gsap.timeline();

    // Add open class
    item.classList.add('accordion-open');

    // Animate header transformation
    timeline
      .to(header, {
        backgroundColor: '#2458A7',
        color: '#ffffff',
        duration: 0.3,
        ease: "power2.out"
      })
      .to(icon, {
        rotation: 180,
        color: '#ffffff',
        duration: 0.3,
        ease: "back.out(1.7)"
      }, "<")
      .to(header.querySelector('p'), {
        color: '#ffffff',
        duration: 0.3,
        ease: "power2.out"
      }, "<");

    // Change icon class after rotation
    setTimeout(() => {
      icon.className = 'fa-solid fa-minus text-white accordion-icon';
    }, 150);

    // Animate body opening with enhanced effects
    timeline
      .to(body, {
        height: 'auto',
        autoAlpha: 1,
        duration: 0.4,
        ease: "power2.out"
      }, "-=0.1")
      .fromTo(body.querySelector('p'), {
        y: 20,
        autoAlpha: 0
      }, {
        y: 0,
        autoAlpha: 1,
        duration: 0.3,
        ease: "back.out(1.7)"
      }, "-=0.2")
      .to(body, {
        paddingTop: '1rem',
        duration: 0.2,
        ease: "power2.out"
      }, "-=0.3");

  }

  // Function to close accordion
  function closeAccordion(item, header, body, icon) {
    const timeline = gsap.timeline();

    // Remove open class
    item.classList.remove('accordion-open');

    // Animate content fade out first
    timeline
      .to(body.querySelector('p'), {
        y: -10,
        autoAlpha: 0,
        duration: 0.2,
        ease: "power2.in"
      })
      .to(body, {
        paddingTop: '0rem',
        duration: 0.15,
        ease: "power2.in"
      }, "-=0.1")
      .to(body, {
        height: 0,
        autoAlpha: 0,
        duration: 0.25,
        ease: "power2.in"
      }, "-=0.1");

    // Animate header transformation back to normal
    timeline
      .to(header, {
        backgroundColor: 'transparent',
        color: 'inherit',
        duration: 0.3,
        ease: "power2.out"
      }, "-=0.2")
      .to(icon, {
        rotation: 0,
        color: '#F4B942', // primary-yellow
        duration: 0.3,
        ease: "back.out(1.7)"
      }, "<")
      .to(header.querySelector('p'), {
        color: 'inherit',
        duration: 0.3,
        ease: "power2.out"
      }, "<");

    // Change icon class after rotation
    setTimeout(() => {
      icon.className = 'fa-solid fa-plus text-primary-yellow accordion-icon';
    }, 150);

  }

  // Function to close accordions in the same row (for large screens)
  function closeAccordionsInSameColumn(targetItem) {
    const accordionContainer = document.querySelector('.accordion-container');
    if (!accordionContainer) return;

    // Get all accordion items as an array
    const allItems = Array.from(accordionItems);
    const targetIndex = allItems.indexOf(targetItem);

    // In a 2-column grid, items in the same row are adjacent pairs
    // Row 1: indices 0, 1
    // Row 2: indices 2, 3
    // Row 3: indices 4, 5
    // etc.
    let rowPartnerIndex;
    if (targetIndex % 2 === 0) {
      // If target is in left column (even index), partner is at index + 1
      rowPartnerIndex = targetIndex + 1;
    } else {
      // If target is in right column (odd index), partner is at index - 1
      rowPartnerIndex = targetIndex - 1;
    }

    // Close the row partner if it exists and is open
    if (rowPartnerIndex >= 0 && rowPartnerIndex < allItems.length) {
      const partnerItem = allItems[rowPartnerIndex];
      if (partnerItem && partnerItem.classList.contains('accordion-open')) {
        const header = partnerItem.querySelector('.accordion-header');
        const body = partnerItem.querySelector('.accordion-body');
        const icon = partnerItem.querySelector('.accordion-icon');
        closeAccordion(partnerItem, header, body, icon);
      }
    }
  }

  // Function to close all accordions
  function closeAllAccordions() {
    accordionItems.forEach(item => {
      const header = item.querySelector('.accordion-header');
      const body = item.querySelector('.accordion-body');
      const icon = item.querySelector('.accordion-icon');

      if (item.classList.contains('accordion-open')) {
        closeAccordion(item, header, body, icon);
      }
    });
  }

  // Handle window resize to adjust accordion behavior
  let resizeTimeout;
  window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
      // Close all accordions on resize to reset state
      closeAllAccordions();
    }, 250);
  });

}
