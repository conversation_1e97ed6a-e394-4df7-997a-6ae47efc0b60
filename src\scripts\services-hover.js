// Interactive Services Hover Effect with Slow Blur Transitions
export function initServicesHoverEffect() {
  // Get all service items and grid images
  const serviceItems = document.querySelectorAll('.service-item');
  const gridImages = document.querySelectorAll('.grid-item img');

  // Define the image paths for each service
  const serviceImages = {
    1: 'public/pages/home-page/our-services/website-1.webp', // Default
    2: 'public/pages/home-page/our-services/website-2.webp',
    3: 'public/pages/home-page/our-services/website-3.webp',
    4: 'public/pages/home-page/our-services/website-4.webp'
  };

  // Default image and last hovered image tracking
  const defaultImage = serviceImages[1];
  let lastHoveredImage = defaultImage;

  // Function to change all grid images with slow blur effect
  function changeGridImagesWithBlur(newImagePath) {
    gridImages.forEach((img, index) => {
      // Step 1: Add blur effect (1.5 seconds)
      img.classList.add('blur-out');

      // Step 2: Change image source after blur is applied (wait 1 second)
      setTimeout(() => {
        img.src = newImagePath;

        // Step 3: Remove blur effect after image loads (wait another 0.5 seconds)
        setTimeout(() => {
          img.classList.remove('blur-out');
        }, 500);
      }, 1000);
    });
  }

  // Add hover effects to each service item
  serviceItems.forEach((serviceItem, index) => {
    const serviceNumber = index + 1;

    // Mouse enter - change to service-specific image
    serviceItem.addEventListener('mouseenter', () => {
      const imagePath = serviceImages[serviceNumber] || defaultImage;
      lastHoveredImage = imagePath; // Update last hovered image
      changeGridImagesWithBlur(imagePath);

      // Enhanced service item feedback
      serviceItem.style.transition = 'all 0.3s ease-out';
      serviceItem.style.transform = 'translateX(8px) scale(1.02)';
      serviceItem.style.borderColor = '#3B82F6';
      serviceItem.style.boxShadow = '0 10px 25px rgba(59, 130, 246, 0.15)';

    });

    // Mouse leave - keep showing last hovered image instead of reverting to default
    serviceItem.addEventListener('mouseleave', () => {
      // Don't change the image - keep showing the last hovered one

      // Reset service item visual feedback only
      serviceItem.style.transform = 'translateX(0) scale(1)';
      serviceItem.style.borderColor = '';
      serviceItem.style.boxShadow = '';

    });
  });

  // Set initial default image for all grid items
  gridImages.forEach(img => {
    img.src = defaultImage;
  });

}

// Enhanced hover effect with smooth transitions
export function initServicesHoverEffectEnhanced() {
  const serviceItems = document.querySelectorAll('.service-item');
  const gridItems = document.querySelectorAll('.grid-item');

  const serviceImages = {
    1: 'public/pages/home-page/our-services/website-1.webp',
    2: 'public/pages/home-page/our-services/website-2.webp',
    3: 'public/pages/home-page/our-services/website-3.webp',
    4: 'public/pages/home-page/our-services/website-4.webp'
  };

  const defaultImage = serviceImages[1];

  // Function to create very slow and visible fade effect
  function changeGridImageSmooth(imagePath) {
    gridItems.forEach((gridItem, index) => {
      // Remove any existing transitions first
      gridItem.style.transition = 'none';

      // Force a reflow
      gridItem.offsetHeight;

      // Phase 1: Very slow fade out with heavy blur (3 seconds!)
      gridItem.style.transition = 'opacity 3s ease-in-out, filter 3s ease-in-out';
      gridItem.style.opacity = '0.1';  // Almost invisible but not completely
      gridItem.style.filter = 'blur(25px) brightness(0.5)';

      // Phase 2: Change image when almost completely faded (wait 2 seconds)
      setTimeout(() => {
        gridItem.style.backgroundImage = `url('${imagePath}')`;

        // Phase 3: Very slow fade in (wait 1 more second, then 3 second fade in)
        setTimeout(() => {
          gridItem.style.transition = 'opacity 3s ease-in-out, filter 3s ease-in-out';
          gridItem.style.opacity = '1';
          gridItem.style.filter = 'blur(0px) brightness(1)';
        }, 1000);
      }, 2000);
    });
  }

  // Add enhanced hover effects
  serviceItems.forEach((serviceItem, index) => {
    const serviceNumber = index + 1;

    serviceItem.addEventListener('mouseenter', () => {
      const imagePath = serviceImages[serviceNumber] || defaultImage;
      changeGridImageSmooth(imagePath);

      // Enhanced visual feedback
      serviceItem.style.transform = 'translateX(5px)';
      serviceItem.style.borderColor = '#3B82F6';
    });

    serviceItem.addEventListener('mouseleave', () => {
      changeGridImageSmooth(defaultImage);

      // Reset visual feedback
      serviceItem.style.transform = 'translateX(0)';
      serviceItem.style.borderColor = '';
    });
  });

  // Initialize with default image
  changeGridImageSmooth(defaultImage);

}

// Ultra-smooth hover effect with staggered blur transitions
export function initServicesHoverEffectUltra() {
  const serviceItems = document.querySelectorAll('.service-item');
  const gridItems = document.querySelectorAll('.grid-item');

  const serviceImages = {
    1: 'public/pages/home-page/our-services/website-1.webp',
    2: 'public/pages/home-page/our-services/website-2.webp',
    3: 'public/pages/home-page/our-services/website-3.webp',
    4: 'public/pages/home-page/our-services/website-4.webp'
  };

  const defaultImage = serviceImages[1];

  // Ultra-smooth function with staggered blur effect
  function changeGridImageUltra(imagePath) {
    gridItems.forEach((gridItem, index) => {
      // Staggered delay for each grid item
      const delay = index * 50;

      setTimeout(() => {
        // Phase 1: Fade out with increasing blur
        gridItem.style.transition = 'opacity 0.5s ease-out, filter 0.5s ease-out, transform 0.5s ease-out';
        gridItem.style.opacity = '0';
        gridItem.style.filter = 'blur(12px) brightness(0.8)';
        gridItem.style.transform = 'scale(0.98)';

        // Phase 2: Change image at peak blur
        setTimeout(() => {
          gridItem.style.backgroundImage = `url('${imagePath}')`;

          // Phase 3: Fade in with clearing blur and scale back
          setTimeout(() => {
            gridItem.style.opacity = '1';
            gridItem.style.filter = 'blur(0px) brightness(1)';
            gridItem.style.transform = 'scale(1)';
          }, 100);
        }, 250);
      }, delay);
    });
  }

  // Add ultra-smooth hover effects
  serviceItems.forEach((serviceItem, index) => {
    const serviceNumber = index + 1;

    serviceItem.addEventListener('mouseenter', () => {
      const imagePath = serviceImages[serviceNumber] || defaultImage;
      changeGridImageUltra(imagePath);

      // Enhanced service item feedback
      serviceItem.style.transition = 'all 0.3s ease-out';
      serviceItem.style.transform = 'translateX(8px) scale(1.02)';
      serviceItem.style.borderColor = '#3B82F6';
      serviceItem.style.boxShadow = '0 10px 25px rgba(59, 130, 246, 0.15)';
    });

    serviceItem.addEventListener('mouseleave', () => {
      changeGridImageUltra(defaultImage);

      // Reset service item
      serviceItem.style.transform = 'translateX(0) scale(1)';
      serviceItem.style.borderColor = '';
      serviceItem.style.boxShadow = '';
    });
  });

  // Initialize with default image
  changeGridImageUltra(defaultImage);

}
