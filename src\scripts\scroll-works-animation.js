import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Professional Responsive Scroll Works Animation
export function initScrollWorksAnimation() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);


  // Get all elements
  const section = document.querySelector('.scroll-works-section');
  const container = document.querySelector('.scroll-works-container');
  const title = document.querySelector('.scroll-works-title');
  const cards = document.querySelectorAll('.scroll-work-card');

  if (!section || !container || !title || cards.length === 0) {
    return;
  }


  // Function to get responsive positioning
  function getResponsivePosition(card) {
    const side = card.dataset.side;
    const screenWidth = window.innerWidth;

    if (screenWidth <= 640) {
      // Mobile: center all cards
      return "7.5%";
    } else if (screenWidth <= 1024) {
      // Tablet: center all cards
      return "12.5%";
    } else {
      // Desktop: left/right positioning
      return side === 'left' ? "5%" : "50%";
    }
  }

  // Set initial states
  gsap.set(title, {
    autoAlpha: 1,
    y: 0,
    scale: 1
  });

  // Set all cards to start from bottom of section, invisible, and position them left/right
  cards.forEach((card, index) => {
    gsap.set(card, {
      autoAlpha: 0,
      y: "100%", // Start from bottom of section
      left: getResponsivePosition(card), // Responsive positioning
      top: 0,
      transform: "translateY(0)", // No vertical centering initially
      scale: 0.8,
      filter: "blur(8px)"
    });
  });

  // Create main timeline with ScrollTrigger
  const mainTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: section,
      start: "top top",
      end: "bottom bottom",
      scrub: 1,
      pin: container,
      anticipatePin: 1,
    }
  });

  // Calculate timing for overlapping cards - longer duration for overlap
  const cardDuration = 1 / (cards.length - 1); // Overlap the last card
  const overlapAmount = 0.2; // 20% overlap between cards (reduced for more spacing)

  // 1. Title fade out animation at the beginning
  mainTimeline.to(title, {
    autoAlpha: 0.3,
    y: -40,
    scale: 0.9,
    duration: 0.15,
    ease: "power2.inOut"
  }, 0);

  // Card animations with responsive positioning
  cards.forEach((card, index) => {
    const startTime = index * cardDuration * (1 - overlapAmount);
    const responsiveLeft = getResponsivePosition(card);

    const cardTimeline = gsap.timeline();

    // Phase 1: Fade in and move up
    cardTimeline.fromTo(card,
      {
        autoAlpha: 0,
        y: "100%",
        left: responsiveLeft,
        transform: "translateY(0)",
        scale: 0.8,
        filter: "blur(8px)"
      },
      {
        autoAlpha: 1,
        y: "75%",
        left: responsiveLeft,
        transform: "translateY(0)",
        scale: 1,
        filter: "blur(0px)",
        duration: cardDuration * 0.25,
        ease: "power2.out"
      }
    );

    // Phase 2: Continue moving up
    cardTimeline.to(card, {
      y: "25%",
      left: responsiveLeft,
      transform: "translateY(0)",
      duration: cardDuration * 0.5,
      ease: "none"
    });

    // Phase 3: Fade out and exit
    cardTimeline.to(card, {
      autoAlpha: 0,
      y: "0%",
      left: responsiveLeft,
      transform: "translateY(0)",
      scale: 0.8,
      filter: "blur(8px)",
      duration: cardDuration * 0.25,
      ease: "power2.in"
    });

    mainTimeline.add(cardTimeline, startTime);
  });

  // Handle window resize
  window.addEventListener('resize', () => {
    ScrollTrigger.refresh();

    // Update card positions on resize
    cards.forEach(card => {
      gsap.set(card, {
        left: getResponsivePosition(card)
      });
    });
  });

}

// Enhanced version with more cinematic effects
export function initScrollWorksAnimationEnhanced() {
  gsap.registerPlugin(ScrollTrigger);


  const section = document.querySelector('.scroll-works-section');
  const container = document.querySelector('.scroll-works-container');
  const title = document.querySelector('.scroll-works-title');
  const cards = document.querySelectorAll('.scroll-work-card');
  const dots = document.querySelectorAll('.progress-dot');

  if (!section || !container || !title || cards.length === 0) {
    return;
  }

  // Set initial states - cards start from bottom of section
  gsap.set(title, {
    autoAlpha: 1,
    y: 0,
    scale: 1,
    rotationX: 0
  });

  gsap.set(cards, {
    autoAlpha: 0,
    y: "120%", // Start below section
    scale: 0.5,
    skewY: 15,
    skewX: 8,
    filter: "blur(25px)",
    rotationX: 40,
    rotationY: 20,
    rotationZ: 5
  });

  // Enhanced timeline with more complex animations
  const enhancedTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: section,
      start: "top top",
      end: "bottom bottom",
      scrub: 0.8,
      pin: container,
      anticipatePin: 1,
      onUpdate: (self) => {
        const progress = self.progress;

        // More complex background with multiple gradients and animations
        const hue1 = 200 + (progress * 100);
        const hue2 = 240 + (progress * 80);
        const hue3 = 280 + (progress * 60);

        section.style.background = `
          radial-gradient(circle at 20% 80%, hsl(${hue1}, 80%, 20%) 0%, transparent 40%),
          radial-gradient(circle at 80% 20%, hsl(${hue2}, 70%, 25%) 0%, transparent 40%),
          radial-gradient(circle at 50% 50%, hsl(${hue3}, 60%, 30%) 0%, transparent 40%),
          conic-gradient(from ${progress * 360}deg at 50% 50%, 
            hsl(${hue1}, 70%, 15%) 0deg, 
            hsl(${hue2}, 60%, 20%) 120deg, 
            hsl(${hue3}, 50%, 25%) 240deg, 
            hsl(${hue1}, 70%, 15%) 360deg)
        `;
      }
    }
  });

  const cardDuration = 1 / (cards.length - 1); // Overlap the last card
  const overlapAmount = 0.5; // 50% overlap between cards

  // Enhanced title animation
  enhancedTimeline.to(title, {
    autoAlpha: 0.1,
    y: -100,
    scale: 0.7,
    rotationX: -30,
    filter: "blur(8px)",
    duration: cardDuration * 0.5,
    ease: "power3.inOut"
  }, 0);

  // Enhanced card animations - traveling from bottom to top of section with overlapping
  cards.forEach((card, index) => {
    const cardIndex = parseInt(card.dataset.card);
    const side = card.dataset.side;
    const isLeft = side === 'left';
    const startTime = (index + 0.5) * cardDuration * (1 - overlapAmount); // Start earlier for overlap

    // Phase 1: Cinematic entrance from bottom with fade in (0% to 30% of card duration)
    enhancedTimeline.fromTo(card,
      {
        autoAlpha: 0,
        y: "120%", // Start below section
        left: isLeft ? "5%" : "50%",
        scale: 0.5,
        skewY: 15,
        skewX: 8,
        filter: "blur(25px)",
        rotationX: 40,
        rotationY: 20,
        rotationZ: 5
      },
      {
        autoAlpha: 1,
        y: "70%", // Move up while fading in
        left: isLeft ? "5%" : "50%",
        scale: 1.1,
        skewY: 0,
        skewX: 0,
        filter: "blur(0px)",
        rotationX: 0,
        rotationY: 0,
        rotationZ: 0,
        duration: cardDuration * 0.3,
        ease: "elastic.out(1, 0.5)"
      }, startTime);

    // Phase 2: Scale and settle while continuing to move up (30% to 70% of card duration)
    enhancedTimeline.to(card, {
      scale: 1,
      y: "30%", // Continue moving up through section
      duration: cardDuration * 0.4,
      ease: "power2.out"
    }, startTime + cardDuration * 0.2);

    // Phase 3: Cinematic exit at top (70% to 100% of card duration) - except last card
    if (index < cards.length - 1) {
      enhancedTimeline.to(card, {
        autoAlpha: 0,
        y: "-20%", // Exit above section
        left: isLeft ? "5%" : "50%",
        scale: 0.6,
        skewY: -12,
        skewX: -6,
        filter: "blur(20px)",
        rotationX: -35,
        rotationY: -15,
        rotationZ: -8,
        duration: cardDuration * 0.3,
        ease: "power3.in"
      }, startTime + cardDuration * 0.7);
    }

    // Enhanced dot animations (if dots exist)
    if (dots && dots[cardIndex]) {
      enhancedTimeline.to(dots[cardIndex], {
        scale: 2.2,
        backgroundColor: "#60a5fa",
        boxShadow: "0 0 25px #60a5fa, 0 0 50px rgba(96, 165, 250, 0.3)",
        duration: 0.3,
        ease: "elastic.out(1, 0.8)"
      }, startTime + cardDuration * 0.15);

      if (index > 0) {
        const prevDotIndex = parseInt(cards[index - 1].dataset.card);
        if (dots[prevDotIndex]) {
          enhancedTimeline.to(dots[prevDotIndex], {
            scale: 1,
            backgroundColor: "rgba(255, 255, 255, 0.3)",
            boxShadow: "none",
            duration: 0.3,
            ease: "power2.out"
          }, startTime + cardDuration * 0.15);
        }
      }
    }
  });

}