// RTL Testimonials Carousel
export function initTestimonialsCarousel() {
  // Check if testimonials section exists before initializing
  const testimonialsSection = document.querySelector('.testimonials-section');
  if (!testimonialsSection) {
    return; // Exit early if testimonials section doesn't exist
  }

  class RTLTestimonialCarousel {
    constructor() {
      this.currentIndex = 0;
      this.testimonialsData = [
        {
          title: "أفضل شركة تقدم تطبيقات ومواقع متوافقة",
          text: "سيرف فايف شركة رائدة في مجال تطوير التطبيقات والمواقع الإلكترونية. تتميز بخدماتها المتطورة وفريقها المحترف الذي يضمن تقديم حلول تقنية مبتكرة تلبي احتياجات العملاء بأعلى معايير الجودة والكفاءة.",
          customerName: "سعيد راشد",
          customerTitle: "مدير التقنية",
          image: "public/pages/home-page/testimonials/testimonials-1.webp",
          rating: 5
        },
        {
          title: "خدمة عملاء ممتازة وتطوير احترافي",
          text: "تجربتي مع سيرف فايف كانت استثنائية. الفريق محترف جداً ويتفهم احتياجات العميل بشكل دقيق. تم تسليم المشروع في الوقت المحدد وبجودة عالية تفوق التوقعات.",
          customerName: "علي المحذوف",
          customerTitle: "مدير الأعمال",
          image: "public/pages/home-page/testimonials/testimonials-2.webp",
          rating: 5
        },
        {
          title: "حلول تقنية مبتكرة ومتطورة",
          text: "سيرف فايف قدمت لنا حلول تقنية متقدمة ساعدتنا في تطوير أعمالنا بشكل كبير. الدعم الفني ممتاز والفريق دائماً متاح للمساعدة. أنصح بشدة بالتعامل معهم.",
          customerName: "محمد أحمد",
          customerTitle: "رئيس قسم التطوير",
          image: "public/pages/home-page/testimonials/testimonials-3.webp",
          rating: 5
        },
        {
          title: "شراكة ناجحة وثقة متبادلة",
          text: "منذ بداية التعامل مع سيرف فايف والعلاقة تتطور للأفضل. يتميزون بالشفافية والالتزام بالمواعيد. المشاريع التي نفذوها لنا حققت نجاحاً كبيراً وساهمت في نمو أعمالنا.",
          customerName: "أحمد سالم",
          customerTitle: "مدير عام",
          image: "public/pages/home-page/testimonials/testimonials-1.webp",
          rating: 5
        },
        // Duplicate the testimonials to enable more swiping
        {
          title: "أفضل شركة تقدم تطبيقات ومواقع متوافقة",
          text: "سيرف فايف شركة رائدة في مجال تطوير التطبيقات والمواقع الإلكترونية. تتميز بخدماتها المتطورة وفريقها المحترف الذي يضمن تقديم حلول تقنية مبتكرة تلبي احتياجات العملاء بأعلى معايير الجودة والكفاءة.",
          customerName: "سعيد راشد",
          customerTitle: "مدير التقنية",
          image: "public/pages/home-page/testimonials/testimonials-1.webp",
          rating: 5
        },
        {
          title: "خدمة عملاء ممتازة وتطوير احترافي",
          text: "تجربتي مع سيرف فايف كانت استثنائية. الفريق محترف جداً ويتفهم احتياجات العميل بشكل دقيق. تم تسليم المشروع في الوقت المحدد وبجودة عالية تفوق التوقعات.",
          customerName: "علي المحذوف",
          customerTitle: "مدير الأعمال",
          image: "public/pages/home-page/testimonials/testimonials-2.webp",
          rating: 5
        },
        {
          title: "حلول تقنية مبتكرة ومتطورة",
          text: "سيرف فايف قدمت لنا حلول تقنية متقدمة ساعدتنا في تطوير أعمالنا بشكل كبير. الدعم الفني ممتاز والفريق دائماً متاح للمساعدة. أنصح بشدة بالتعامل معهم.",
          customerName: "محمد أحمد",
          customerTitle: "رئيس قسم التطوير",
          image: "public/pages/home-page/testimonials/testimonials-3.webp",
          rating: 5
        },
        {
          title: "شراكة ناجحة وثقة متبادلة",
          text: "منذ بداية التعامل مع سيرف فايف والعلاقة تتطور للأفضل. يتميزون بالشفافية والالتزام بالمواعيد. المشاريع التي نفذوها لنا حققت نجاحاً كبيراً وساهمت في نمو أعمالنا.",
          customerName: "أحمد سالم",
          customerTitle: "مدير عام",
          image: "public/pages/home-page/testimonials/testimonials-1.webp",
          rating: 5
        }
      ];

      this.cards = document.querySelectorAll('.testimonial-card');
      this.title = document.querySelector('.testimonial-title');
      this.text = document.querySelector('.testimonial-text p');
      this.nextBtn = document.querySelector('.next-btn');
      this.prevBtn = document.querySelector('.prev-btn');
      this.testimonialSection = document.querySelector('.testimonials-section');

      // Check if all required elements exist
      if (!this.nextBtn || !this.prevBtn || !this.testimonialSection || this.cards.length === 0) {
        console.warn('Testimonials carousel: Required elements not found');
        return;
      }

      this.init();
    }

    init() {
      this.setupEventListeners();
      this.updateDisplay();
      this.startAutoPlay();
    }

    setupEventListeners() {
      // Navigation buttons
      this.nextBtn.addEventListener('click', () => this.next());
      this.prevBtn.addEventListener('click', () => this.prev());

      // Auto-play pause on hover
      this.testimonialSection.addEventListener('mouseenter', () => this.stopAutoPlay());
      this.testimonialSection.addEventListener('mouseleave', () => this.startAutoPlay());

      // Card click interactions
      this.cards.forEach((card, index) => {
        card.addEventListener('click', () => {
          if (index === 1) this.next(); // Middle card goes to active
          else if (index === 2) this.prev(); // Left card goes to active
        });
      });
    }

    next() {
      this.currentIndex = (this.currentIndex + 1) % this.testimonialsData.length;
      this.updateDisplay();
    }

    prev() {
      this.currentIndex = (this.currentIndex - 1 + this.testimonialsData.length) % this.testimonialsData.length;
      this.updateDisplay();
    }



    updateDisplay() {
      this.updateCards();
      this.updateContent();
    }

    updateCards() {
      const totalCards = this.testimonialsData.length;

      // Calculate indices for RTL (right to left)
      const activeIndex = this.currentIndex;
      const nextIndex = (this.currentIndex + 1) % totalCards;
      const furtherIndex = (this.currentIndex + 2) % totalCards;

      // Update card classes and content
      this.cards.forEach((card, index) => {
        card.classList.remove('active', 'next', 'further', 'hidden');

        if (index === 0) {
          // Right card (active)
          card.classList.add('active');
          this.updateCardContent(card, activeIndex);
        } else if (index === 1) {
          // Middle card (next)
          card.classList.add('next');
          this.updateCardContent(card, nextIndex);
        } else if (index === 2) {
          // Left card (further)
          card.classList.add('further');
          this.updateCardContent(card, furtherIndex);
        } else if (index === 3) {
          // Hidden card
          card.classList.add('hidden');
          this.updateCardContent(card, (this.currentIndex + 3) % totalCards);
        }
      });
    }

    updateCardContent(card, dataIndex) {
      const data = this.testimonialsData[dataIndex];
      const img = card.querySelector('img');
      const nameEl = card.querySelector('.image-overlay p:first-child');
      const titleEl = card.querySelector('.image-overlay p:last-child');

      if (img) img.src = data.image;
      if (nameEl) nameEl.textContent = data.customerName;
      if (titleEl) titleEl.textContent = data.customerTitle;
    }

    updateContent() {
      const data = this.testimonialsData[this.currentIndex];

      // Add fade effect
      this.title.style.opacity = '0';
      this.text.style.opacity = '0';
      this.title.style.transform = 'translateY(20px)';
      this.text.style.transform = 'translateY(20px)';

      setTimeout(() => {
        this.title.textContent = data.title;
        this.text.textContent = data.text;

        // Fade in
        this.title.style.opacity = '1';
        this.text.style.opacity = '1';
        this.title.style.transform = 'translateY(0)';
        this.text.style.transform = 'translateY(0)';
      }, 300);

      // Update star rating
      this.updateStarRating(data.rating);
    }

    updateStarRating(rating) {
      const stars = document.querySelectorAll('.testimonial-rating i');
      stars.forEach((star, index) => {
        if (index < rating) {
          star.classList.remove('text-gray');
          star.classList.add('text-primary-blue');
        } else {
          star.classList.remove('text-primary-blue');
          star.classList.add('text-gray');
        }
      });
    }



    startAutoPlay() {
      this.autoPlayInterval = setInterval(() => {
        this.next();
      }, 5000);
    }

    stopAutoPlay() {
      clearInterval(this.autoPlayInterval);
    }
  }

  // Initialize the carousel when the DOM is loaded
  document.addEventListener('DOMContentLoaded', () => {
    new RTLTestimonialCarousel();
  });

  // Create an instance immediately if the DOM is already loaded
  if (document.readyState === 'complete' || document.readyState === 'interactive') {
    new RTLTestimonialCarousel();
  }

}
