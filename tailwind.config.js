/** @type {import('tailwindcss').Config} */
export default {
  content: ["./*", "./src/**/*.{js,ts,jsx,tsx,html}"],
  theme: {
    extend: {
      screens: {
        "xs": { max: "550px" },
      },
      container: {
        center: true,
        padding: '1rem', // px-4
      },
      colors: {
        'primary-yellow': "#DE8545",
        'primary-blue': "#15205C",
        'main-blue': "#070e19",
        'light-blue': "#15205C",
        'lightest-blue': "#EAEDFF",
        'secondary-blue': "#2458A7",
        'dark-blue': "#050609",
        'gray': "#667299",
        'light-gray': "#F3F6FB",
        'section-gray': "#F7FAFF",
      },
      keyframes: {
        'scale-x': {
          '0%, 30%': {
            transform: 'translateX(0) scaleX(1)',
            'transform-origin': '100% 50%',
          },
          '50%': {
            transform: 'translateX(0) scaleX(1.2)',
            'transform-origin': '100% 50%',
          },
          '70%,  100%': {
            transform: 'translateX(0) scaleX(1)',
            'transform-origin': '100% 50%',
          },
        },
        'scale-hand': {
          '0%, 30%': {
            transform: 'scale(1)',
            'transform-origin': '100% 50%',
          },
          '50%': {
            transform: ' scale(1.8)',
            'transform-origin': '100% 50%',
          },
          '70%,  100%': {
            transform: ' scale(1)',
            'transform-origin': '100% 50%',
          },
        },
        'scale-winning': {
          '0%, 30%': {
            transform: 'scale(1)',
            'transform-origin': '0% 50%',
          },
          '50%': {
            transform: ' scale(1.8)',
            'transform-origin': '0% 100%',
          },
          '70%,  100%': {
            transform: ' scale(1)',
            'transform-origin': '0% 50%',
          },
        },
      },
      animation: {
        'scale-x': 'scale-x 3s ease-in-out infinite alternate',
        'scale-hand': 'scale-hand 3s ease-in-out infinite alternate',
        'scale-winning': 'scale-winning 3s ease-in-out infinite alternate',
      },
    },
  },
};
