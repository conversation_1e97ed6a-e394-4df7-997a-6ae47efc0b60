import { gsap } from "gsap";

// Professional Web Design Page Hover Effects
export function initWebDesignHoverEffects() {
  initFeatureItemHoverEffects();
  initTabHoverEffects();
  initTemplateCardHoverEffects();
  initButtonHoverEffects();
  initImageHoverEffects();
}

// 1. Feature Items Hover Effects (for drag-drop section)
function initFeatureItemHoverEffects() {
  const featureItems = document.querySelectorAll('.choose-features-section li[data-id]');

  featureItems.forEach(item => {
    let hoverTl = null;

    item.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(item, {
          scale: 1.05,
          y: -8,
          rotationX: 5,
          boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
          duration: 0.3,
          ease: "power2.out"
        })
        .to(item.querySelector('i'), {
          scale: 1.2,
          rotation: 10,
          color: "#DE8545",
          duration: 0.2,
          ease: "back.out(1.7)"
        }, "-=0.2");
    });

    item.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(item, {
          scale: 1,
          y: 0,
          rotationX: 0,
          boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
          duration: 0.3,
          ease: "power2.out"
        })
        .to(item.querySelector('i'), {
          scale: 1,
          rotation: 0,
          color: "#F3C518",
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });
  });
}

// 2. Tab Hover Effects
function initTabHoverEffects() {
  const tabs = document.querySelectorAll('.work-tab');

  tabs.forEach(tab => {
    let hoverTl = null;

    tab.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.to(tab, {
        scale: 1.05,
        y: -3,
        boxShadow: "0 8px 25px rgba(36, 88, 167, 0.3)",
        duration: 0.3,
        ease: "back.out(1.7)"
      });
    });

    tab.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.to(tab, {
        scale: 1,
        y: 0,
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  });
}

// 3. Template Card Hover Effects
function initTemplateCardHoverEffects() {
  const templateCards = document.querySelectorAll('.template-card');

  templateCards.forEach(card => {
    let hoverTl = null;

    card.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(card, {
          scale: 1.03,
          y: -10,
          rotationX: 5,
          boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
          duration: 0.4,
          ease: "power2.out"
        })
        .to(card.querySelector('.template-image'), {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to(card.querySelector('.btn-purchase'), {
          scale: 1.1,
          backgroundColor: "#2458A7",
          color: "#ffffff",
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });

    card.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(card, {
          scale: 1,
          y: 0,
          rotationX: 0,
          boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
          duration: 0.4,
          ease: "power2.out"
        })
        .to(card.querySelector('.template-image'), {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to(card.querySelector('.btn-purchase'), {
          scale: 1,
          backgroundColor: "transparent",
          color: "#2458A7",
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });
  });
}

// 4. Button Hover Effects
function initButtonHoverEffects() {
  // Hero buttons
  const heroButtons = document.querySelectorAll('.hero-buttons button');

  heroButtons.forEach(button => {
    let hoverTl = null;

    button.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(button, {
          scale: 1.05,
          y: -2,
          duration: 0.3,
          ease: "back.out(1.7)"
        })
        .to(button.querySelector('i'), {
          x: 5,
          scale: 1.2,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });

    button.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(button, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(button.querySelector('i'), {
          x: 0,
          scale: 1,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });
  });

  // Drop zone buttons
  const dropZoneButtons = document.querySelectorAll('#dropZone button');

  dropZoneButtons.forEach(button => {
    let hoverTl = null;

    button.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.to(button, {
        scale: 1.05,
        y: -3,
        boxShadow: "0 8px 20px rgba(0,0,0,0.2)",
        duration: 0.3,
        ease: "back.out(1.7)"
      });
    });

    button.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.to(button, {
        scale: 1,
        y: 0,
        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  });
}

// 5. Image Hover Effects
function initImageHoverEffects() {
  // Best company section images
  const companyImages = document.querySelectorAll('.best-company-section img');

  companyImages.forEach(img => {
    let hoverTl = null;

    img.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.to(img, {
        scale: 1.05,
        rotationY: 5,
        filter: "brightness(1.1) contrast(1.1)",
        duration: 0.4,
        ease: "power2.out"
      });
    });

    img.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.to(img, {
        scale: 1,
        rotationY: 0,
        filter: "brightness(1) contrast(1)",
        duration: 0.4,
        ease: "power2.out"
      });
    });
  });

  // Enhanced Design yourself section images hover effects
  const designImages = document.querySelectorAll('.design-yourself-section .design-images-container img');

  designImages.forEach((image, index) => {
    let hoverTl = null;

    image.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(image, {
          scale: 1.08,
          rotation: index % 2 === 0 ? 2 : -2,
          filter: "brightness(1.1) saturate(1.2)",
          y: -8,
          duration: 0.4,
          ease: "power2.out"
        })
        .to(image, {
          boxShadow: "0 20px 40px rgba(0,0,0,0.2)",
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.3");
    });

    image.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(image, {
          scale: 1,
          rotation: 0,
          filter: "brightness(1) saturate(1)",
          y: 0,
          boxShadow: "0 10px 20px rgba(0,0,0,0.1)",
          duration: 0.4,
          ease: "power2.out"
        });
    });
  });

  // CTA Button hover effect
  const ctaButton = document.querySelector('.design-cta-button');

  if (ctaButton) {
    let hoverTl = null;

    ctaButton.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(ctaButton, {
          scale: 1.05,
          y: -3,
          duration: 0.3,
          ease: "back.out(1.7)"
        })
        .to(ctaButton.querySelector('i'), {
          x: 5,
          scale: 1.2,
          rotation: 15,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });

    ctaButton.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(ctaButton, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        })
        .to(ctaButton.querySelector('i'), {
          x: 0,
          scale: 1,
          rotation: 0,
          duration: 0.2,
          ease: "power2.out"
        }, "-=0.2");
    });
  }

  // Text lines hover effect
  const textLines = document.querySelectorAll('.design-main-text span');

  textLines.forEach((line, index) => {
    let hoverTl = null;

    line.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.to(line, {
        scale: 1.05,
        textShadow: "2px 2px 8px rgba(0,0,0,0.3)",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    line.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.to(line, {
        scale: 1,
        textShadow: "none",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  });
}

// 6. Floating Animation for Icons and Elements
export function initWebDesignFloatingAnimations() {
  // Floating animation for breadcrumb icons
  const floatingIcons = document.querySelectorAll('.breadcrumbs-section .floating-icon');

  floatingIcons.forEach((icon, index) => {
    gsap.to(icon, {
      y: "random(-20, 20)",
      x: "random(-15, 15)",
      rotation: "random(-10, 10)",
      duration: "random(2, 4)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: index * 0.2
    });
  });

  // Floating animation for feature icons
  const featureIcons = document.querySelectorAll('.features-section i');

  featureIcons.forEach((icon, index) => {
    gsap.to(icon, {
      y: "random(-5, 5)",
      rotation: "random(-5, 5)",
      duration: "random(3, 5)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: index * 0.3
    });
  });
}
