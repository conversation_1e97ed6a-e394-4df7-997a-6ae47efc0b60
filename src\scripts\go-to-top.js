export function initGoToTopButton() {
    const goToTopBtn = document.getElementById("goToTopBtn");
    if (!goToTopBtn) return;

    // Show button when user scrolls down 300px from the top
    window.addEventListener("scroll", () => {
        if (window.scrollY > 300) {
            goToTopBtn.classList.add("opacity-100");
            goToTopBtn.classList.remove("opacity-0");
        } else {
            goToTopBtn.classList.add("opacity-0");
            goToTopBtn.classList.remove("opacity-100");
        }
    });

    // Scroll to top with smooth behavior when button is clicked
    goToTopBtn.addEventListener("click", () => {
        window.scrollTo({
            top: 0,
            behavior: "smooth",
        });
    });

    // Initially hide the button
    goToTopBtn.classList.add("opacity-0", "transition-opacity", "duration-300");
}