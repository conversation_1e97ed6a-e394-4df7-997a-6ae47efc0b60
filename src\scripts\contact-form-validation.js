// Contact Form Validation
export function initContactFormValidation() {
  const form = document.getElementById('contactForm');

  if (!form) {
    return;
  }


  // Get form elements
  const firstName = form.querySelector('#firstName');
  const email = form.querySelector('#email');
  const phone = form.querySelector('#phone');
  const message = form.querySelector('#message');
  const submitButton = form.querySelector('button[type="submit"]');

  // Validation rules
  const validationRules = {
    firstName: {
      required: true,
      minLength: 2,
      pattern: /^[\u0600-\u06FFa-zA-Z\s]+$/,
      errorMessages: {
        required: 'الإسم الأول مطلوب',
        minLength: 'الإسم يجب أن يكون أكثر من حرفين',
        pattern: 'الإسم يجب أن يحتوي على أحرف فقط'
      }
    },
    email: {
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      errorMessages: {
        required: 'البريد الإلكتروني مطلوب',
        pattern: 'يرجى إدخال بريد إلكتروني صحيح'
      }
    },
    phone: {
      required: true,
      pattern: /^[0-9]{8,15}$/,
      errorMessages: {
        required: 'رقم الهاتف مطلوب',
        pattern: 'يرجى إدخال رقم هاتف صحيح (8-15 رقم)'
      }
    },
    message: {
      required: false,
      maxLength: 500,
      errorMessages: {
        maxLength: 'الرسالة يجب أن تكون أقل من 500 حرف'
      }
    }
  };

  // Show error message
  function showError(field, message) {
    const errorSpan = field.parentNode.querySelector('.error-message');
    if (errorSpan) {
      errorSpan.textContent = message;
      errorSpan.classList.remove('hidden');
    }
    field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.remove('border-gray-300', 'focus:ring-primary-blue', 'focus:border-primary-blue');
  }

  // Hide error message
  function hideError(field) {
    const errorSpan = field.parentNode.querySelector('.error-message');
    if (errorSpan) {
      errorSpan.classList.add('hidden');
    }
    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.add('border-gray-300', 'focus:ring-primary-blue', 'focus:border-primary-blue');
  }

  // Validate single field
  function validateField(field, rules) {
    const value = field.value.trim();

    // Check required
    if (rules.required && !value) {
      showError(field, rules.errorMessages.required);
      return false;
    }

    // Check pattern
    if (value && rules.pattern && !rules.pattern.test(value)) {
      showError(field, rules.errorMessages.pattern);
      return false;
    }

    // Check min length
    if (value && rules.minLength && value.length < rules.minLength) {
      showError(field, rules.errorMessages.minLength);
      return false;
    }

    // Check max length
    if (value && rules.maxLength && value.length > rules.maxLength) {
      showError(field, rules.errorMessages.maxLength);
      return false;
    }

    hideError(field);
    return true;
  }

  // Real-time validation on input
  firstName.addEventListener('input', () => {
    validateField(firstName, validationRules.firstName);
  });

  email.addEventListener('input', () => {
    validateField(email, validationRules.email);
  });

  phone.addEventListener('input', () => {
    validateField(phone, validationRules.phone);
  });

  message.addEventListener('input', () => {
    validateField(message, validationRules.message);
  });

  // Form submission
  form.addEventListener('submit', (e) => {
    e.preventDefault();

    // Validate all fields
    const isFirstNameValid = validateField(firstName, validationRules.firstName);
    const isEmailValid = validateField(email, validationRules.email);
    const isPhoneValid = validateField(phone, validationRules.phone);
    const isMessageValid = validateField(message, validationRules.message);

    const isFormValid = isFirstNameValid && isEmailValid && isPhoneValid && isMessageValid;

    if (isFormValid) {
      // Show loading state
      submitButton.disabled = true;
      submitButton.textContent = 'جاري الإرسال...';
      submitButton.classList.add('opacity-75', 'cursor-not-allowed');

      // Simulate form submission
      setTimeout(() => {
        // Show success message
        showSuccessMessage();

        // Reset form
        form.reset();

        // Reset button
        submitButton.disabled = false;
        submitButton.textContent = 'إرسال';
        submitButton.classList.remove('opacity-75', 'cursor-not-allowed');

      }, 2000);
    } else {

      // Focus on first invalid field
      const firstInvalidField = form.querySelector('.border-red-500');
      if (firstInvalidField) {
        firstInvalidField.focus();
      }
    }
  });

  // Show success message
  function showSuccessMessage() {
    const successDiv = document.createElement('div');
    successDiv.className = 'fixed top-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300';
    successDiv.innerHTML = `
      <div class="flex items-center gap-2">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
        </svg>
        <span>تم إرسال الرسالة بنجاح!</span>
      </div>
    `;

    document.body.appendChild(successDiv);

    // Animate in
    setTimeout(() => {
      successDiv.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      successDiv.classList.add('translate-x-full');
      setTimeout(() => {
        document.body.removeChild(successDiv);
      }, 300);
    }, 3000);
  }

}
