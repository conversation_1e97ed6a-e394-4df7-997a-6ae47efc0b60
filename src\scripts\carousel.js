import Embla<PERSON>arousel from "embla-carousel";
import <PERSON>S<PERSON>roll from "embla-carousel-auto-scroll";

// Carousel configuration
const DIRECTION = "rtl";

const CAROUSEL = {
  LOOP: true,
  DIRECTION: DIRECTION,
  ALIGN: "center",
  AUTO_SCROLL: {
    PLAY_ON_INIT: true,
    STOP_ON_INTERACTION: true,
    STOP_ON_MOUSE_ENTER: true,
    STOP_ON_FOCUS_IN: true,
    DURATION: 2000, // 2 seconds in milliseconds
  },
};

// Initialize partners carousel
export function initPartnersCarousel() {
  const emblaNodePartners = document.querySelector(".embla_partners");

  if (emblaNodePartners) {
    const viewportNodeCars = emblaNodePartners.querySelector(".embla__viewport");

    if (viewportNodeCars) {
      const emblaApi = EmblaCarousel(
        viewportNodeCars,
        {
          loop: CAROUSEL.LOOP,
          direction: CAROUSEL.DIRECTION,
          align: CAROUSEL.ALIGN,
          startIndex: 0,
        },
        [AutoScroll({
          playOnInit: true,
        }),]
      );

      // Store the API reference on the DOM element for direct access
      emblaNodePartners.emblaApi = emblaApi;

      // Initialize with a manual scroll to ensure everything is set up
      setTimeout(() => {
        emblaApi.reInit();
        emblaApi.scrollTo(0);
      }, 100);

      return emblaApi;
    }
  }

  return null;
}



// Add dots functionality for blogs carousel
function addBlogsDotBtns(emblaApi, dotsNode) {
  let dotNodes = [];

  const addDotBtnsWithClickHandlers = () => {
    dotsNode.innerHTML = emblaApi
      .scrollSnapList()
      .map(() => '<button class="embla__dot" type="button"></button>')
      .join('');

    const scrollTo = (index) => {
      emblaApi.scrollTo(index);
    };

    dotNodes = Array.from(dotsNode.querySelectorAll('.embla__dot'));
    dotNodes.forEach((dotNode, index) => {
      dotNode.addEventListener('click', () => scrollTo(index));
    });
  };

  const toggleDotBtnsActive = () => {
    const previous = emblaApi.previousScrollSnap();
    const selected = emblaApi.selectedScrollSnap();
    dotNodes[previous].classList.remove('embla__dot--selected');
    dotNodes[selected].classList.add('embla__dot--selected');
  };

  emblaApi
    .on('init', addDotBtnsWithClickHandlers)
    .on('reInit', addDotBtnsWithClickHandlers)
    .on('init', toggleDotBtnsActive)
    .on('reInit', toggleDotBtnsActive)
    .on('select', toggleDotBtnsActive);

  return () => {
    dotsNode.innerHTML = '';
  };
}

// Initialize blogs carousel
export function initBlogsCarousel() {
  const emblaNode = document.querySelector(".embla_blogs");

  if (!emblaNode) {
    console.warn('Blogs carousel: .embla_blogs not found');
    return null;
  }

  const viewportNode = emblaNode.querySelector(".embla__viewport");
  const dotsNode = emblaNode.querySelector(".embla__dots");

  if (!viewportNode) {
    console.warn('Blogs carousel: .embla__viewport not found');
    return null;
  }

  // Blogs carousel configuration
  const blogsConfig = {
    loop: true,
    direction: CAROUSEL.DIRECTION,
    align: "start",
    slidesToScroll: 1,
    containScroll: "trimSnaps",
    breakpoints: {
      '(min-width: 640px)': { slidesToScroll: 2 },
      '(min-width: 1024px)': { slidesToScroll: 3 },
      '(min-width: 1280px)': { slidesToScroll: 4 }
    }
  };

  // Initialize Embla carousel
  const emblaApi = EmblaCarousel(viewportNode, blogsConfig);

  // Store the API reference on the DOM element for direct access
  emblaNode.emblaApi = emblaApi;

  // Add dots navigation if dots container exists
  if (dotsNode) {
    const removeDotBtns = addBlogsDotBtns(emblaApi, dotsNode);
  }

  // Initialize with a manual scroll to ensure everything is set up
  setTimeout(() => {
    emblaApi.reInit();
    emblaApi.scrollTo(0);
  }, 100);

  return emblaApi;
}

// Initialize all carousel functionality
export function initCarousels() {
  // Initialize partners carousel
  const partnersApi = initPartnersCarousel();

  // Initialize blogs carousel
  const blogsApi = initBlogsCarousel();

  return { partnersApi, blogsApi };
}

// Advanced carousel initialization with more options
export function initCarouselAdvanced(options = {}) {
  const defaultOptions = {
    loop: true,
    direction: "rtl",
    align: "center",
    autoScroll: true,
    autoScrollOptions: {
      playOnInit: true,
      stopOnInteraction: true,
      stopOnMouseEnter: true,
      stopOnFocusIn: true,
      duration: 2000,
    }
  };

  const config = { ...defaultOptions, ...options };

  const emblaNodePartners = document.querySelector(".embla_partners");

  if (emblaNodePartners) {
    const viewportNode = emblaNodePartners.querySelector(".embla__viewport");

    if (viewportNode) {
      const plugins = [];

      if (config.autoScroll) {
        plugins.push(AutoScroll(config.autoScrollOptions));
      }

      const emblaApi = EmblaCarousel(
        viewportNode,
        {
          loop: config.loop,
          direction: config.direction,
          align: config.align,
          startIndex: 0,
        },
        plugins
      );

      emblaNodePartners.emblaApi = emblaApi;

      setTimeout(() => {
        emblaApi.reInit();
        emblaApi.scrollTo(0);
      }, 100);

      return emblaApi;
    }
  }

  return null;
}
