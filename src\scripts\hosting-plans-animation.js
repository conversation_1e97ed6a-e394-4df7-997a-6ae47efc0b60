import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// Professional Hosting Plans Section Animation
export function initHostingPlansAnimation() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Check if hosting plans section exists
  const hostingSection = document.querySelector('.hosting-plans-section');
  if (!hostingSection) {
    return;
  }

  // Clear any existing animations first
  gsap.killTweensOf(".hosting-title");
  gsap.killTweensOf(".hosting-underline");
  gsap.killTweensOf(".hosting-subtitle");
  gsap.killTweensOf(".hosting-tab");
  gsap.killTweensOf(".hosting-card");

  // Set initial states for title
  gsap.set(".hosting-title", {
    autoAlpha: 0,
    y: 60,
    scale: 0.8,
    skewY: 8,
    filter: "blur(12px)"
  });

  // Set initial states for underline
  gsap.set(".hosting-underline", {
    scaleX: 0,
    transformOrigin: dir === 'rtl' ? "right center" : "left center"
  });

  // Set initial states for subtitle
  gsap.set(".hosting-subtitle", {
    autoAlpha: 0,
    y: 40,
    scale: 0.9,
    filter: "blur(8px)"
  });

  // Set initial states for tabs
  gsap.set(".hosting-tab", {
    autoAlpha: 0,
    y: 30,
    x: 20 * xDir,
    scale: 0.8,
    skewX: 5 * xDir,
    filter: "blur(6px)"
  });

  // Set initial states for hosting cards
  gsap.set(".hosting-card", {
    autoAlpha: 0,
    y: 100,
    scale: 0.7,
    rotationX: 15,
    skewY: 8,
    filter: "blur(15px)"
  });

  // Create main timeline for hosting plans animation
  const hostingTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".hosting-plans-section",
      start: "top 70%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });

  // Animation sequence
  hostingTimeline
    // 1. Title entrance with dramatic effect
    .to(".hosting-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1.2,
      ease: "back.out(1.7)"
    })

    // 2. Underline animation
    .to(".hosting-underline", {
      scaleX: 1,
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.6")

    // 3. Subtitle fade in
    .to(".hosting-subtitle", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.4")

    // 4. Tabs with stagger effect
    .to(".hosting-tab", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      scale: 1,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: {
        amount: 0.6,
        from: "start",
        ease: "back.out(1.4)"
      },
      ease: "back.out(1.4)"
    }, "-=0.3")

    // 5. Hosting cards with dramatic wave entrance
    .to(".hosting-card", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1.2,
      stagger: {
        amount: 1.2,
        from: "start",
        ease: "power2.out",
        onComplete: function () {
          // Add a subtle bounce effect after each card appears
          gsap.to(this.targets(), {
            scale: 1.05,
            duration: 0.2,
            yoyo: true,
            repeat: 1,
            ease: "power2.inOut"
          });
        }
      },
      ease: "back.out(1.7)"
    }, "-=0.4");

  // Add hover effects for hosting cards
  initHostingCardHoverEffects();

  // Add tab interaction animations
  initHostingTabAnimations();

  // Start continuous floating animations after entrance
  setTimeout(() => {
    initHostingCardFloatingAnimation();
  }, 2000);
}

// Hosting Card Hover Effects
function initHostingCardHoverEffects() {
  const hostingCards = document.querySelectorAll('.hosting-card');

  hostingCards.forEach((card, index) => {
    let hoverTl = null;

    card.addEventListener('mouseenter', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(card, {
          scale: 1.05,
          y: -15,
          rotationY: 5,
          boxShadow: "0 25px 50px rgba(0,0,0,0.2)",
          duration: 0.5,
          ease: "power2.out"
        })
        .to(card.querySelector('svg'), {
          scale: 1.02,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to(card.querySelector('.hosting-btn'), {
          scale: 1.1,
          y: -3,
          duration: 0.3,
          ease: "back.out(1.7)"
        }, "-=0.2");
    });

    card.addEventListener('mouseleave', () => {
      if (hoverTl) hoverTl.kill();

      hoverTl = gsap.timeline();
      hoverTl
        .to(card, {
          scale: 1,
          y: 0,
          rotationY: 0,
          boxShadow: "0 10px 30px rgba(0,0,0,0.1)",
          duration: 0.5,
          ease: "power2.out"
        })
        .to(card.querySelector('svg'), {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.3")
        .to(card.querySelector('.hosting-btn'), {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        }, "-=0.2");
    });
  });
}

// Hosting Tab Animations
function initHostingTabAnimations() {
  const hostingTabs = document.querySelectorAll('.hosting-tab');

  hostingTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Animate tab selection
      gsap.to(tab, {
        scale: 0.95,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut"
      });

      // Add ripple effect
      const ripple = document.createElement('span');
      ripple.style.cssText = `
        position: absolute;
        border-radius: 50%;
        background: rgba(255,255,255,0.3);
        transform: scale(0);
        animation: ripple 0.6s linear;
        pointer-events: none;
      `;

      const rect = tab.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      ripple.style.width = ripple.style.height = size + 'px';
      ripple.style.left = (rect.width / 2 - size / 2) + 'px';
      ripple.style.top = (rect.height / 2 - size / 2) + 'px';

      tab.style.position = 'relative';
      tab.appendChild(ripple);

      setTimeout(() => {
        ripple.remove();
      }, 600);
    });

    // Hover effects for tabs
    tab.addEventListener('mouseenter', () => {
      if (!tab.classList.contains('active')) {
        gsap.to(tab, {
          scale: 1.05,
          y: -2,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });

    tab.addEventListener('mouseleave', () => {
      if (!tab.classList.contains('active')) {
        gsap.to(tab, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  });
}

// Continuous Floating Animation for Hosting Cards
function initHostingCardFloatingAnimation() {
  const hostingCards = document.querySelectorAll('.hosting-card');

  hostingCards.forEach((card, index) => {
    // Subtle floating animation for each card
    gsap.to(card, {
      y: "random(-8, 8)",
      x: "random(-5, 5)",
      rotation: "random(-1, 1)",
      duration: "random(4, 6)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      delay: index * 0.5
    });

    // Floating animation for SVG elements inside cards
    const svg = card.querySelector('svg');
    if (svg) {
      gsap.to(svg, {
        y: "random(-3, 3)",
        rotation: "random(-0.5, 0.5)",
        duration: "random(3, 5)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: index * 0.3
      });
    }

    // Floating animation for floating icons inside cards
    const floatingIcons = card.querySelectorAll('.floating-icon');
    floatingIcons.forEach((icon, iconIndex) => {
      gsap.to(icon, {
        y: "random(-15, 15)",
        x: "random(-10, 10)",
        rotation: "random(-8, 8)",
        duration: "random(2, 4)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: (index * 0.2) + (iconIndex * 0.1)
      });
    });
  });
}

// Add CSS for ripple animation
const style = document.createElement('style');
style.textContent = `
  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }

  /* Enhanced hosting card styles */
  .hosting-card {
    transition: transform 0.3s ease;
  }

  .hosting-card:hover {
    transform-style: preserve-3d;
  }
`;
document.head.appendChild(style);
