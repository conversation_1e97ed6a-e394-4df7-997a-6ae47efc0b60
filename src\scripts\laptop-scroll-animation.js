import { gsap } from "gsap";

// Professional laptop website scrolling animation
export function initLaptopScrollAnimation() {
  const templateCards = document.querySelectorAll('.template-card');

  templateCards.forEach(card => {
    // Support both old and new structure
    const laptopScreen = card.querySelector('.laptop-screen') || card.querySelector('.laptop-container');
    const websiteImage = card.querySelector('.laptop-website');

    if (!laptopScreen || !websiteImage) return;

    let scrollAnimation = null;
    let isAnimating = false;

    // Calculate the scroll distance based on image height vs container height
    const calculateScrollDistance = () => {
      // For the new structure, calculate based on actual dimensions
      if (card.querySelector('.laptop-container')) {
        const containerHeight = laptopScreen.offsetHeight * 0.68; // 68% is the screen height
        const imageHeight = websiteImage.offsetHeight;
        return Math.max(0, imageHeight - containerHeight);
      } else {
        // Original calculation for old structure
        const containerHeight = laptopScreen.offsetHeight;
        const imageHeight = websiteImage.offsetHeight;
        return imageHeight - containerHeight;
      }
    };

    // Mouse enter - start scrolling animation
    card.addEventListener('mouseenter', () => {
      if (isAnimating) return;

      isAnimating = true;
      const scrollDistance = calculateScrollDistance();
      const isNewStructure = card.querySelector('.laptop-container');

      // Only animate if there's content to scroll
      if (scrollDistance > 0) {
        // For new structure, use a different animation approach
        if (isNewStructure) {
          // Get the current top position (in percentage)
          const currentTop = parseFloat(websiteImage.style.top || '6');

          // Animate from current position to show more of the website
          scrollAnimation = gsap.fromTo(websiteImage,
            { top: `${currentTop}%` },
            {
              top: `-${scrollDistance}px`,
              duration: 8, // Slower duration to see more content
              ease: "power1.inOut", // Smoother easing
              onComplete: () => {
                isAnimating = false;
              }
            }
          );
        } else {
          // Original animation for old structure
          scrollAnimation = gsap.to(websiteImage, {
            y: -scrollDistance,
            duration: 2, // 2 seconds for quick scrolling
            ease: "power2.inOut", // Smooth ease-in-out animation
            onComplete: () => {
              isAnimating = false;
            }
          });
        }
      } else {
        isAnimating = false;
      }
    });

    // Mouse leave - scroll back to top
    card.addEventListener('mouseleave', () => {
      if (scrollAnimation) {
        scrollAnimation.kill();
      }

      isAnimating = true;

      // Smooth return to top with ease-in-out
      gsap.to(websiteImage, {
        y: 0,
        duration: 2, // 2 seconds for consistent timing
        ease: "power2.inOut", // Consistent ease-in-out animation
        onComplete: () => {
          isAnimating = false;
        }
      });
    });

    // Handle window resize to recalculate distances
    window.addEventListener('resize', () => {
      if (scrollAnimation) {
        scrollAnimation.kill();
        gsap.set(websiteImage, { y: 0 });
        isAnimating = false;
      }
    });
  });
}

// Enhanced laptop scrolling with additional effects
export function initLaptopScrollAnimationEnhanced() {
  const templateCards = document.querySelectorAll('.template-card');

  templateCards.forEach(card => {
    const laptopScreen = card.querySelector('.laptop-screen');
    const websiteImage = card.querySelector('.laptop-website');

    if (!laptopScreen || !websiteImage) return;

    let scrollTimeline = null;
    let isAnimating = false;

    // Calculate scroll distance
    const calculateScrollDistance = () => {
      const containerHeight = laptopScreen.offsetHeight;
      const imageHeight = websiteImage.offsetHeight;
      return Math.max(0, imageHeight - containerHeight);
    };

    // Mouse enter - enhanced scrolling with subtle effects
    card.addEventListener('mouseenter', () => {
      if (isAnimating) return;

      isAnimating = true;
      const scrollDistance = calculateScrollDistance();

      if (scrollDistance > 0) {
        // Create timeline for complex animation
        scrollTimeline = gsap.timeline({
          onComplete: () => {
            isAnimating = false;
          }
        });

        // Main scrolling animation with smooth ease-in-out
        scrollTimeline.to(websiteImage, {
          y: -scrollDistance,
          duration: 2, // 2 seconds for quick scrolling
          ease: "power2.inOut" // Consistent ease-in-out animation
        });

        // Add subtle card hover effect
        scrollTimeline.to(card, {
          scale: 1.02,
          boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
          duration: 0.5,
          ease: "power2.out"
        }, 0);

        // Add subtle screen glow effect
        scrollTimeline.to(laptopScreen, {
          boxShadow: "inset 0 0 20px rgba(59, 130, 246, 0.1)",
          duration: 0.5,
          ease: "power2.out"
        }, 0);

      } else {
        isAnimating = false;
      }
    });

    // Mouse leave - enhanced return animation
    card.addEventListener('mouseleave', () => {
      if (scrollTimeline) {
        scrollTimeline.kill();
      }

      isAnimating = true;

      // Create return timeline
      const returnTimeline = gsap.timeline({
        onComplete: () => {
          isAnimating = false;
        }
      });

      // Return website to top with ease-in-out
      returnTimeline.to(websiteImage, {
        y: 0,
        duration: 2, // 2 seconds for consistent timing
        ease: "power2.inOut" // Consistent ease-in-out animation
      });

      // Reset card effects
      returnTimeline.to(card, {
        scale: 1,
        boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
        duration: 0.4,
        ease: "power2.out"
      }, 0);

      // Reset screen glow
      returnTimeline.to(laptopScreen, {
        boxShadow: "none",
        duration: 0.4,
        ease: "power2.out"
      }, 0);
    });

    // Handle resize
    window.addEventListener('resize', () => {
      if (scrollTimeline) {
        scrollTimeline.kill();
        gsap.set(websiteImage, { y: 0 });
        gsap.set(card, { scale: 1, boxShadow: "0 4px 6px rgba(0,0,0,0.1)" });
        gsap.set(laptopScreen, { boxShadow: "none" });
        isAnimating = false;
      }
    });
  });
}

// Simple version for better performance
export function initLaptopScrollAnimationSimple() {
  const templateCards = document.querySelectorAll('.template-card');

  templateCards.forEach(card => {
    const websiteImage = card.querySelector('.laptop-website');

    if (!websiteImage) return;

    let scrollAnimation = null;

    card.addEventListener('mouseenter', () => {
      if (scrollAnimation) scrollAnimation.kill();

      // Simple scroll down animation with ease-in-out
      scrollAnimation = gsap.to(websiteImage, {
        y: -400, // Fixed scroll distance
        duration: 2, // 2 seconds for quick scrolling
        ease: "power2.inOut" // Smooth ease-in-out animation
      });
    });

    card.addEventListener('mouseleave', () => {
      if (scrollAnimation) scrollAnimation.kill();

      // Quick return to top with ease-in-out
      scrollAnimation = gsap.to(websiteImage, {
        y: 0,
        duration: 2, // 2 seconds for consistent timing
        ease: "power2.inOut" // Consistent ease-in-out animation
      });
    });
  });
}
