import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Professional Carousel Section GSAP Animation
export function initCarouselSectionGSAP() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Create carousel section timeline
  const carouselTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".carousel-customers-section",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse"
    }
  });

  // Set initial states for section elements
  gsap.set(".carousel-title", {
    autoAlpha: 0,
    y: 80,
    scale: 0.8,
    rotationY: 15,
    filter: "blur(12px)"
  });

  gsap.set(".carousel-container", {
    autoAlpha: 0,
    y: 100,
    scale: 0.9,
    skewY: 5,
    filter: "blur(10px)"
  });

  gsap.set(".carousel-item", {
    autoAlpha: 0,
    scale: 0.6,
    y: 60,
    rotation: 10,
    filter: "blur(8px)"
  });

  gsap.set(".carousel-dots", {
    autoAlpha: 0,
    y: 40,
    scale: 0.7,
    filter: "blur(6px)"
  });

  // Animation sequence - Optimized for speed (50% faster)
  carouselTimeline
    // 1. Title animation with 3D rotation (faster)
    .to(".carousel-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "back.out(1.7)"
    })

    // 2. Carousel container entrance (faster)
    .to(".carousel-container", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.5,
      ease: "power3.out"
    }, "-=0.3")

    // 3. Carousel items with staggered entrance (faster)
    .to(".carousel-item", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      rotation: 0,
      filter: "blur(0px)",
      duration: 0.4,
      stagger: {
        amount: 0.75,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    }, "-=0.2")

    // 4. Navigation dots entrance (faster)
    .to(".carousel-dots", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.4,
      ease: "back.out(1.7)"
    }, "-=0.3");

  return carouselTimeline;
}

// Advanced carousel section animation with floating effects
export function initCarouselSectionAdvanced() {
  gsap.registerPlugin(ScrollTrigger);

  const carouselTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".carousel-customers-section",
      start: "top 70%",
      end: "bottom 30%",
      toggleActions: "play none none reverse",
    }
  });

  // More dramatic initial states
  gsap.set(".carousel-title", {
    autoAlpha: 0,
    y: 120,
    scale: 0.5,
    rotationX: -30,
    rotationY: 20,
    filter: "blur(20px)"
  });

  gsap.set(".carousel-container", {
    autoAlpha: 0,
    y: 150,
    scale: 0.7,
    skewX: 10,
    skewY: 8,
    filter: "blur(15px)"
  });

  // Individual item animations with different directions
  gsap.set(".carousel-item:nth-child(odd)", {
    autoAlpha: 0,
    scale: 0.4,
    x: -100,
    y: 80,
    rotation: -20,
    filter: "blur(12px)"
  });

  gsap.set(".carousel-item:nth-child(even)", {
    autoAlpha: 0,
    scale: 0.4,
    x: 100,
    y: 80,
    rotation: 20,
    filter: "blur(12px)"
  });

  gsap.set(".carousel-dots .embla__dot", {
    autoAlpha: 0,
    scale: 0.3,
    y: 50,
    filter: "blur(8px)"
  });

  // Complex animation sequence
  carouselTimeline
    // Title with dramatic 3D entrance
    .to(".carousel-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 1.5,
      ease: "back.out(2)"
    })

    // Container with skew correction
    .to(".carousel-container", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewX: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1.2,
      ease: "power3.out"
    }, "-=0.8")

    // Odd items from left
    .to(".carousel-item:nth-child(odd)", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      y: 0,
      rotation: 0,
      filter: "blur(0px)",
      duration: 1,
      stagger: 0.1,
      ease: "back.out(1.7)"
    }, "-=0.6")

    // Even items from right
    .to(".carousel-item:nth-child(even)", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      y: 0,
      rotation: 0,
      filter: "blur(0px)",
      duration: 1,
      stagger: 0.1,
      ease: "back.out(1.7)"
    }, "-=0.9")

    // Dots with individual stagger
    .to(".carousel-dots .embla__dot", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      filter: "blur(0px)",
      duration: 0.6,
      stagger: 0.1,
      ease: "back.out(1.7)"
    }, "-=0.4");

  return carouselTimeline;
}

// Add floating animation to carousel items after they appear
export function initCarouselFloatingEffect() {
  // Wait for initial animation to complete
  setTimeout(() => {
    gsap.to(".carousel-item", {
      y: "random(-10, 10)",
      x: "random(-5, 5)",
      rotation: "random(-2, 2)",
      duration: "random(3, 5)",
      repeat: -1,
      yoyo: true,
      ease: "sine.inOut",
      stagger: {
        amount: 2,
        from: "random"
      }
    });
  }, 3000);
}

// Professional hover effects for carousel items
export function initCarouselHoverEffects() {
  const carouselItems = document.querySelectorAll('.carousel-item');

  carouselItems.forEach(item => {
    const img = item.querySelector('img');

    if (img) {
      item.addEventListener('mouseenter', () => {
        gsap.to(img, {
          scale: 1.1,
          rotation: 5,
          filter: "brightness(1.1) contrast(1.1)",
          duration: 0.4,
          ease: "power2.out"
        });

        gsap.to(item, {
          y: -10,
          boxShadow: "0 20px 40px rgba(0,0,0,0.2)",
          duration: 0.4,
          ease: "power2.out"
        });
      });

      item.addEventListener('mouseleave', () => {
        gsap.to(img, {
          scale: 1,
          rotation: 0,
          filter: "brightness(1) contrast(1)",
          duration: 0.4,
          ease: "power2.out"
        });

        gsap.to(item, {
          y: 0,
          boxShadow: "0 5px 15px rgba(0,0,0,0.1)",
          duration: 0.4,
          ease: "power2.out"
        });
      });
    }
  });
}
