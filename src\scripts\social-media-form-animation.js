import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Social Media Form Section Animation
export function initSocialMediaFormAnimation() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Get direction for RTL support
  const getDirection = () => {
    return document.documentElement.dir || 'rtl';
  };

  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Get form elements
  const section = document.getElementById('socialMediaSection');
  const formContainer = document.getElementById('socialMediaFormContainer');
  const formRows = document.querySelectorAll('.form-row');
  const submitButton = document.querySelector('.form-submit');

  if (!section || !formContainer) return;

  // Set initial states for form container
  gsap.set(formContainer, {
    autoAlpha: 0,
    y: 80,
    scale: 0.9,
    rotationX: 15,
    filter: "blur(10px)"
  });

  // Set initial states for form rows
  gsap.set(formRows, {
    autoAlpha: 0,
    y: 60,
    x: 40 * xDir,
    scale: 0.95,
    skewY: 3,
    filter: "blur(8px)"
  });

  // Set initial state for submit button
  if (submitButton) {
    gsap.set(submitButton, {
      autoAlpha: 0,
      y: 40,
      scale: 0.8,
      filter: "blur(6px)"
    });
  }

  // Create main animation timeline
  const mainTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: section,
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse"
    }
  });

  // Animate form container entrance
  mainTimeline.to(formContainer, {
    autoAlpha: 1,
    y: 0,
    scale: 1,
    rotationX: 0,
    filter: "blur(0px)",
    duration: 1.2,
    ease: "back.out(1.4)"
  });

  // Animate form rows with stagger
  mainTimeline.to(formRows, {
    autoAlpha: 1,
    y: 0,
    x: 0,
    scale: 1,
    skewY: 0,
    filter: "blur(0px)",
    duration: 0.8,
    stagger: {
      amount: 1.0,
      from: "start",
      ease: "power2.out"
    },
    ease: "power2.out"
  }, "-=0.6");

  // Animate submit button
  if (submitButton) {
    mainTimeline.to(submitButton, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "back.out(1.7)"
    }, "-=0.3");
  }

  // Add floating animation to form container
  gsap.to(formContainer, {
    y: -10,
    duration: 3,
    ease: "power1.inOut",
    yoyo: true,
    repeat: -1
  });

  // Add individual field hover effects
  const formFields = document.querySelectorAll('#socialMediaForm input, #socialMediaForm textarea, #socialMediaForm select');
  
  formFields.forEach(field => {
    field.addEventListener('focus', () => {
      gsap.to(field, {
        scale: 1.02,
        y: -2,
        duration: 0.3,
        ease: "power2.out"
      });
    });

    field.addEventListener('blur', () => {
      gsap.to(field, {
        scale: 1,
        y: 0,
        duration: 0.3,
        ease: "power2.out"
      });
    });
  });

  // Add submit button hover effects
  if (submitButton) {
    const button = submitButton.querySelector('button');
    if (button) {
      button.addEventListener('mouseenter', () => {
        gsap.to(button, {
          scale: 1.05,
          y: -3,
          duration: 0.3,
          ease: "power2.out"
        });
      });

      button.addEventListener('mouseleave', () => {
        gsap.to(button, {
          scale: 1,
          y: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      });
    }
  }

  // Add parallax effect to form container
  gsap.to(formContainer, {
    y: -50,
    ease: "none",
    scrollTrigger: {
      trigger: section,
      start: "top bottom",
      end: "bottom top",
      scrub: 1
    }
  });

  // Add file upload zone animation
  const dropZone = document.getElementById('dropZone');
  if (dropZone) {
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      gsap.to(dropZone, {
        scale: 1.02,
        borderColor: "#3B82F6",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    dropZone.addEventListener('dragleave', () => {
      gsap.to(dropZone, {
        scale: 1,
        borderColor: "#D1D5DB",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    dropZone.addEventListener('drop', () => {
      gsap.to(dropZone, {
        scale: 1,
        borderColor: "#10B981",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  }

  // Add label animation effects
  const labels = document.querySelectorAll('#socialMediaForm label');
  labels.forEach(label => {
    label.addEventListener('mouseenter', () => {
      gsap.to(label, {
        scale: 1.02,
        color: "#3B82F6",
        duration: 0.2,
        ease: "power2.out"
      });
    });

    label.addEventListener('mouseleave', () => {
      gsap.to(label, {
        scale: 1,
        color: "#06033E",
        duration: 0.2,
        ease: "power2.out"
      });
    });
  });

  // Add success animation for form submission
  const form = document.getElementById('socialMediaForm');
  if (form) {
    form.addEventListener('submit', (e) => {
      // Add a subtle success animation
      gsap.to(formContainer, {
        scale: 0.98,
        duration: 0.1,
        yoyo: true,
        repeat: 1,
        ease: "power2.inOut",
        onComplete: () => {
          gsap.to(formContainer, {
            scale: 1,
            duration: 0.3,
            ease: "back.out(1.7)"
          });
        }
      });
    });
  }
}
