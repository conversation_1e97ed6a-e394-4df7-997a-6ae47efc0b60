// Quotation Request Form Functionality
export function initQuotationForm() {
  const form = document.getElementById('quotationForm');
  const dropZone = document.getElementById('dropZone');
  const fileInput = document.getElementById('fileInput');
  const browseFiles = document.getElementById('browseFiles');
  const fileList = document.getElementById('fileList');

  if (!form) {
    return;
  }

  // Get form elements
  const clientName = form.querySelector('#clientName');
  const companyName = form.querySelector('#companyName');
  const phone = form.querySelector('#phone');
  const countryCode = form.querySelector('#countryCode');
  const email = form.querySelector('#email');
  const country = form.querySelector('#country');
  const city = form.querySelector('#city');
  const typeOfRequest = form.querySelector('#typeOfRequest');
  const budget = form.querySelector('#budget');
  const websiteLink = form.querySelector('#websiteLink');
  const haveAssistant = form.querySelector('#haveAssistant');
  const haveLogo = form.querySelector('#haveLogo');
  const haveImage = form.querySelector('#haveImage');
  const yourGoals = form.querySelector('#yourGoals');
  const yourServices = form.querySelector('#yourServices');
  const favouriteColors = form.querySelector('#favouriteColors');
  const favouriteWebsites = form.querySelector('#favouriteWebsites');
  const additionalNotes = form.querySelector('#additionalNotes');
  const submitButton = form.querySelector('button[type="submit"]');

  // Store uploaded files
  let uploadedFiles = [];

  // Validation rules
  const validationRules = {
    clientName: {
      required: true,
      minLength: 2,
      pattern: /^[\u0600-\u06FFa-zA-Z\s]+$/,
      errorMessages: {
        required: 'اسم العميل مطلوب',
        minLength: 'الاسم يجب أن يكون أكثر من حرفين',
        pattern: 'الاسم يجب أن يحتوي على أحرف فقط'
      }
    },
    companyName: {
      required: true,
      minLength: 2,
      pattern: /^[\u0600-\u06FFa-zA-Z0-9\s&.-]+$/,
      errorMessages: {
        required: 'اسم الشركة مطلوب',
        minLength: 'اسم الشركة يجب أن يكون أكثر من حرفين',
        pattern: 'اسم الشركة يحتوي على أحرف غير صالحة'
      }
    },
    phone: {
      required: true,
      pattern: /^[0-9]{8,15}$/,
      errorMessages: {
        required: 'رقم الهاتف مطلوب',
        pattern: 'يرجى إدخال رقم هاتف صحيح (8-15 رقم)'
      }
    },
    email: {
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      errorMessages: {
        required: 'البريد الإلكتروني مطلوب',
        pattern: 'يرجى إدخال بريد إلكتروني صحيح'
      }
    },
    country: {
      required: true,
      minLength: 2,
      pattern: /^[\u0600-\u06FFa-zA-Z\s]+$/,
      errorMessages: {
        required: 'الدولة مطلوبة',
        minLength: 'اسم الدولة يجب أن يكون أكثر من حرفين',
        pattern: 'اسم الدولة يجب أن يحتوي على أحرف فقط'
      }
    },
    city: {
      required: true,
      minLength: 2,
      pattern: /^[\u0600-\u06FFa-zA-Z\s]+$/,
      errorMessages: {
        required: 'المدينة مطلوبة',
        minLength: 'اسم المدينة يجب أن يكون أكثر من حرفين',
        pattern: 'اسم المدينة يجب أن يحتوي على أحرف فقط'
      }
    },
    typeOfRequest: {
      required: true,
      minLength: 3,
      errorMessages: {
        required: 'نوع الطلب مطلوب',
        minLength: 'نوع الطلب يجب أن يكون أكثر من 3 أحرف'
      }
    },
    budget: {
      required: true,
      minLength: 2,
      errorMessages: {
        required: 'الميزانية مطلوبة',
        minLength: 'يرجى تحديد الميزانية المقترحة'
      }
    },
    websiteLink: {
      required: false,
      pattern: /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/,
      errorMessages: {
        pattern: 'يرجى إدخال رابط صحيح'
      }
    },
    yourGoals: {
      required: false,
      maxLength: 1000,
      errorMessages: {
        maxLength: 'الأهداف يجب أن تكون أقل من 1000 حرف'
      }
    },
    yourServices: {
      required: false,
      maxLength: 1000,
      errorMessages: {
        maxLength: 'وصف الخدمات يجب أن يكون أقل من 1000 حرف'
      }
    },
    favouriteColors: {
      required: false,
      maxLength: 500,
      errorMessages: {
        maxLength: 'تفضيلات التقنية يجب أن تكون أقل من 500 حرف'
      }
    },
    favouriteWebsites: {
      required: false,
      maxLength: 500,
      errorMessages: {
        maxLength: 'المواقع المفضلة يجب أن تكون أقل من 500 حرف'
      }
    },
    additionalNotes: {
      required: false,
      maxLength: 500,
      errorMessages: {
        maxLength: 'الملاحظات الإضافية يجب أن تكون أقل من 500 حرف'
      }
    }
  };

  // Show error message
  function showError(field, message) {
    const errorSpan = field.parentNode.querySelector('.error-message');
    if (errorSpan) {
      errorSpan.textContent = message;
      errorSpan.classList.remove('hidden');
    }
    field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.remove('border-gray-300', 'focus:ring-primary-blue', 'focus:border-transparent');
  }

  // Hide error message
  function hideError(field) {
    const errorSpan = field.parentNode.querySelector('.error-message');
    if (errorSpan) {
      errorSpan.classList.add('hidden');
    }
    field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
    field.classList.add('border-gray-300', 'focus:ring-primary-blue', 'focus:border-transparent');
  }

  // Validate single field
  function validateField(field, rules) {
    if (!field || !rules) return false;
    const value = field.value.trim();

    // Check required
    if (rules.required && !value) {
      showError(field, rules.errorMessages.required);
      return false;
    }

    // Check pattern
    if (value && rules.pattern && !rules.pattern.test(value)) {
      showError(field, rules.errorMessages.pattern);
      return false;
    }

    // Check min length
    if (value && rules.minLength && value.length < rules.minLength) {
      showError(field, rules.errorMessages.minLength);
      return false;
    }

    // Check max length
    if (value && rules.maxLength && value.length > rules.maxLength) {
      showError(field, rules.errorMessages.maxLength);
      return false;
    }

    hideError(field);
    return true;
  }

  // File handling functions
  function handleFiles(files) {
    Array.from(files).forEach(file => {
      // Check file type
      const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif'
      ];
      if (!allowedTypes.includes(file.type)) {
        showNotification('يرجى رفع ملفات PDF، Word، أو صور فقط', 'error');
        return;
      }

      // Check file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        showNotification('حجم الملف يجب أن يكون أقل من 10 ميجابايت', 'error');
        return;
      }

      // Add to uploaded files
      uploadedFiles.push(file);
      displayFile(file);
    });
  }

  function displayFile(file) {
    const fileDiv = document.createElement('div');
    fileDiv.className = 'flex items-center justify-between bg-gray-50 p-3 rounded-lg';

    // Determine file icon
    let iconClass = 'fas fa-file-alt';
    if (file.type.startsWith('image/')) {
      iconClass = 'fas fa-image';
    } else if (file.type.includes('pdf')) {
      iconClass = 'fas fa-file-pdf';
    } else if (file.type.includes('word')) {
      iconClass = 'fas fa-file-word';
    }

    fileDiv.innerHTML = `
      <div class="flex items-center gap-3">
        <i class="${iconClass} text-primary-blue"></i>
        <span class="text-sm font-medium">${file.name}</span>
        <span class="text-xs text-gray-500">(${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
      </div>
      <button type="button" class="text-red-500 hover:text-red-700" onclick="removeFile('${file.name}')">
        <i class="fas fa-times"></i>
      </button>
    `;

    fileList.appendChild(fileDiv);
    fileList.classList.remove('hidden');
  }

  // Make removeFile function global
  window.removeFile = function (fileName) {
    uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
    const fileElements = fileList.querySelectorAll('div');
    fileElements.forEach(element => {
      if (element.textContent.includes(fileName)) {
        element.remove();
      }
    });

    if (uploadedFiles.length === 0) {
      fileList.classList.add('hidden');
    }
  };

  // Show notification
  function showNotification(message, type = 'success') {
    const notificationDiv = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    notificationDiv.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
    notificationDiv.innerHTML = `
      <div class="flex items-center gap-2">
        <i class="fas ${type === 'success' ? 'fa-check' : 'fa-exclamation-triangle'}"></i>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(notificationDiv);

    // Animate in
    setTimeout(() => {
      notificationDiv.classList.remove('translate-x-full');
    }, 100);

    // Remove after 4 seconds
    setTimeout(() => {
      notificationDiv.classList.add('translate-x-full');
      setTimeout(() => {
        if (document.body.contains(notificationDiv)) {
          document.body.removeChild(notificationDiv);
        }
      }, 300);
    }, 4000);
  }

  // Event listeners for drag and drop
  if (dropZone) {
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropZone.classList.add('border-primary-blue', 'bg-blue-50');
    });

    dropZone.addEventListener('dragleave', (e) => {
      e.preventDefault();
      dropZone.classList.remove('border-primary-blue', 'bg-blue-50');
    });

    dropZone.addEventListener('drop', (e) => {
      e.preventDefault();
      dropZone.classList.remove('border-primary-blue', 'bg-blue-50');
      const files = e.dataTransfer.files;
      handleFiles(files);
    });
  }

  // Browse files button
  if (browseFiles) {
    browseFiles.addEventListener('click', () => {
      fileInput.click();
    });
  }

  // File input change
  if (fileInput) {
    fileInput.addEventListener('change', (e) => {
      handleFiles(e.target.files);
    });
  }

  // Real-time validation
  if (clientName) {
    clientName.addEventListener('input', () => {
      validateField(clientName, validationRules.clientName);
    });
  }

  if (companyName) {
    companyName.addEventListener('input', () => {
      validateField(companyName, validationRules.companyName);
    });
  }

  if (phone) {
    phone.addEventListener('input', () => {
      validateField(phone, validationRules.phone);
    });
  }

  if (email) {
    email.addEventListener('input', () => {
      validateField(email, validationRules.email);
    });
  }

  if (country) {
    country.addEventListener('input', () => {
      validateField(country, validationRules.country);
    });
  }

  if (city) {
    city.addEventListener('input', () => {
      validateField(city, validationRules.city);
    });
  }

  if (typeOfRequest) {
    typeOfRequest.addEventListener('input', () => {
      validateField(typeOfRequest, validationRules.typeOfRequest);
    });
  }

  if (budget) {
    budget.addEventListener('input', () => {
      validateField(budget, validationRules.budget);
    });
  }

  if (websiteLink) {
    websiteLink.addEventListener('input', () => {
      validateField(websiteLink, validationRules.websiteLink);
    });
  }

  if (yourGoals) {
    yourGoals.addEventListener('input', () => {
      validateField(yourGoals, validationRules.yourGoals);
    });
  }

  if (yourServices) {
    yourServices.addEventListener('input', () => {
      validateField(yourServices, validationRules.yourServices);
    });
  }

  if (favouriteColors) {
    favouriteColors.addEventListener('input', () => {
      validateField(favouriteColors, validationRules.favouriteColors);
    });
  }

  if (favouriteWebsites) {
    favouriteWebsites.addEventListener('input', () => {
      validateField(favouriteWebsites, validationRules.favouriteWebsites);
    });
  }

  if (additionalNotes) {
    additionalNotes.addEventListener('input', () => {
      validateField(additionalNotes, validationRules.additionalNotes);
    });
  }

  // Form submission
  if (form) {
    form.addEventListener('submit', (e) => {
      e.preventDefault();

      // Validate all required fields
      const isClientNameValid = clientName ? validateField(clientName, validationRules.clientName) : false;
      const isCompanyNameValid = companyName ? validateField(companyName, validationRules.companyName) : false;
      const isPhoneValid = phone ? validateField(phone, validationRules.phone) : false;
      const isEmailValid = email ? validateField(email, validationRules.email) : false;
      const isCountryValid = country ? validateField(country, validationRules.country) : false;
      const isCityValid = city ? validateField(city, validationRules.city) : false;
      const isTypeOfRequestValid = typeOfRequest ? validateField(typeOfRequest, validationRules.typeOfRequest) : false;
      const isBudgetValid = budget ? validateField(budget, validationRules.budget) : false;

      // Optional fields validation
      const isWebsiteLinkValid = websiteLink ? validateField(websiteLink, validationRules.websiteLink) : true;
      const isYourGoalsValid = yourGoals ? validateField(yourGoals, validationRules.yourGoals) : true;
      const isYourServicesValid = yourServices ? validateField(yourServices, validationRules.yourServices) : true;
      const isFavouriteColorsValid = favouriteColors ? validateField(favouriteColors, validationRules.favouriteColors) : true;
      const isFavouriteWebsitesValid = favouriteWebsites ? validateField(favouriteWebsites, validationRules.favouriteWebsites) : true;
      const isAdditionalNotesValid = additionalNotes ? validateField(additionalNotes, validationRules.additionalNotes) : true;

      const isFormValid = isClientNameValid && isCompanyNameValid && isPhoneValid && isEmailValid &&
        isCountryValid && isCityValid && isTypeOfRequestValid && isBudgetValid &&
        isWebsiteLinkValid && isYourGoalsValid && isYourServicesValid &&
        isFavouriteColorsValid && isFavouriteWebsitesValid && isAdditionalNotesValid;

      if (isFormValid) {
        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span>جاري الإرسال...</span><i class="fas fa-spinner fa-spin"></i>';
        submitButton.classList.add('opacity-75', 'cursor-not-allowed');

        // Simulate form submission
        setTimeout(() => {
          // Show success message
          showNotification('تم إرسال طلب العرض بنجاح! سنتواصل معك قريباً', 'success');

          // Reset form
          form.reset();
          uploadedFiles = [];
          fileList.innerHTML = '';
          fileList.classList.add('hidden');

          // Reset button
          submitButton.disabled = false;
          submitButton.innerHTML = '<span>إرسال الطلب</span><i class="fas fa-arrow-left"></i>';
          submitButton.classList.remove('opacity-75', 'cursor-not-allowed');

        }, 2500);
      } else {
        // Focus on first invalid field
        const firstInvalidField = form.querySelector('.border-red-500');
        if (firstInvalidField) {
          firstInvalidField.focus();
          firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    });
  }
}
