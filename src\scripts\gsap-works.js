import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Professional Works Section GSAP Animation
export function initWorksGSAP() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Create works section timeline
  const worksTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".works-section",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse"
    }
  });

  // Set initial states for section elements
  gsap.set(".works-title", {
    autoAlpha: 0,
    y: 80,
    scale: 0.8,
    skewY: 6,
    filter: "blur(10px)"
  });

  // Set initial state for the yellow underline
  gsap.set(".works-title span", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: "left center"
  });

  // Animation sequence
  worksTimeline
    // 1. Title animation with professional effects
    .to(".works-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    })

    // 2. Yellow underline animation
    .to(".works-title span", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4");

  return worksTimeline;
}
