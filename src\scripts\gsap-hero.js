import { gsap } from "gsap";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// Professional Hero Section GSAP Animation
export function initHeroGSAP() {
  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Create hero timeline
  const heroTimeline = gsap.timeline({ paused: true });

  // Set initial states for text elements
  gsap.set(".hero-text p", {
    autoAlpha: 0,
    y: 60,
    skewY: 5,
    filter: "blur(8px)"
  });

  gsap.set(".hero-arrow", {
    autoAlpha: 0,
    scale: 0.3,
    rotation: -45,
    filter: "blur(6px)"
  });

  gsap.set(".hero-buttons button", {
    autoAlpha: 0,
    scale: 0.7,
    x: 40 * xDir,
    skewX: 8 * skewXDir,
    filter: "blur(4px)"
  });

  // Set initial states for stat boxes
  gsap.set(".stat-box", {
    autoAlpha: 0,
    x: 100 * xDir,
    y: 30,
    scale: 0.8,
    skewX: -12 * skewXDir,
    filter: "blur(6px)"
  });

  gsap.set(".counter", {
    textContent: 0
  });

  // 1. Hero text paragraphs - stagger from top with professional effects (faster)
  heroTimeline.to(".hero-text p", {
    autoAlpha: 1,
    y: 0,
    skewY: 0,
    filter: "blur(0px)",
    duration: 0.6,
    stagger: {
      amount: 0.5,
      from: "start",
      ease: "power2.out"
    },
    ease: "back.out(1.4)"
  });

  // 2. Hero arrow with rotation and scale (faster)
  heroTimeline.to(".hero-arrow", {
    autoAlpha: 1,
    scale: 1,
    rotation: 0,
    filter: "blur(0px)",
    duration: 0.5,
    ease: "back.out(1.7)"
  }, "-=0.2");

  // 3. Buttons sequential animation from left/right (faster)
  heroTimeline.to(".hero-buttons button", {
    autoAlpha: 1,
    scale: 1,
    x: 0,
    skewX: 0,
    filter: "blur(0px)",
    duration: 0.4,
    stagger: 0.15,
    ease: "back.out(1.7)"
  }, "-=0.15");

  // 4. Stat boxes from right/left with stagger (faster)
  heroTimeline.to(".stat-box", {
    autoAlpha: 1,
    x: 0,
    y: 0,
    scale: 1,
    skewX: 0,
    filter: "blur(0px)",
    duration: 0.6,
    stagger: {
      amount: 0.4,
      from: "end",
      ease: "power2.out"
    },
    ease: "back.out(1.4)"
  }, "-=0.3");

  // 5. Counter animations (faster)
  heroTimeline.call(() => {
    animateCounters();
  }, null, "-=0.15");

  return heroTimeline;
}

// Professional counter animation function
function animateCounters() {
  const counters = document.querySelectorAll('.counter');

  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));

    gsap.to(counter, {
      textContent: target,
      duration: 1.2,
      ease: "power2.out",
      snap: { textContent: 1 },
      onUpdate: function () {
        counter.textContent = Math.ceil(counter.textContent);
      }
    });
  });
}

// Function to start hero animation after header completes
export function startHeroAnimation() {
  const heroTl = initHeroGSAP();
  heroTl.play();
}

// Advanced hero animation with more effects
export function initHeroGSAPAdvanced() {
  const heroTimeline = gsap.timeline({ paused: true });

  // More complex initial states
  gsap.set(".hero-text p:nth-child(1)", {
    autoAlpha: 0,
    y: 80,
    x: -50,
    skewY: 8,
    filter: "blur(10px)"
  });

  gsap.set(".hero-text p:nth-child(2)", {
    autoAlpha: 0,
    y: 60,
    x: 30,
    skewY: -5,
    filter: "blur(8px)"
  });

  gsap.set(".hero-text p:nth-child(3)", {
    autoAlpha: 0,
    y: 70,
    x: -20,
    skewY: 6,
    filter: "blur(9px)"
  });

  gsap.set(".hero-arrow", {
    autoAlpha: 0,
    scale: 0.2,
    rotation: 180,
    skewX: 15,
    filter: "blur(12px)"
  });

  // Complex text animations
  heroTimeline
    .to(".hero-text p:nth-child(1)", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1.2,
      ease: "back.out(1.7)"
    })
    .to(".hero-text p:nth-child(2)", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.5)"
    }, "-=0.8")
    .to(".hero-arrow", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(2)"
    }, "-=0.6")
    .to(".hero-text p:nth-child(3)", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.5)"
    }, "-=0.7");

  return heroTimeline;
}
