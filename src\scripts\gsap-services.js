import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// Professional Services Section GSAP Animation
export function initServicesGSAP() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;
  const transformOriginDir = dir === 'rtl' ? 'left center' : 'right center';

  // Create services section timeline
  const servicesTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".services-section",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse"
    }
  });

  // Set initial states for section elements
  gsap.set(".services-title", {
    autoAlpha: 0,
    y: 80,
    scale: 0.8,
    rotationX: -15,
    filter: "blur(10px)"
  });

  gsap.set(".services-subtitle", {
    autoAlpha: 0,
    y: 50,
    x: -40 * xDir,
    filter: "blur(6px)"
  });

  gsap.set(".service-item", {
    autoAlpha: 0,
    x: -100 * xDir,
    y: 50,
    scale: 0.9,
    skewX: 8 * skewXDir,
    filter: "blur(8px)"
  });

  gsap.set(".services-grid", {
    autoAlpha: 0
  });

  // Set initial state for the entire grid (fade in effect)
  gsap.set(".services-grid", {
    autoAlpha: 0
  });

  // Set initial state for outer squares (positions 4, 6, 9 in grid)
  gsap.set(".grid-item:nth-child(4), .grid-item:nth-child(6), .grid-item:nth-child(9)", {
    width: "100%"
  });

  // Animation sequence
  servicesTimeline
    // 1. Title animation with 3D rotation
    .to(".services-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      filter: "blur(0px)",
      duration: 1.2,
      ease: "back.out(1.7)"
    })

    // 2. Subtitle slide from left/right
    .to(".services-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.6")

    // 3. Service items stagger from left/right
    .to(".service-item", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: {
        amount: 1.2,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    }, "-=0.4")

    // 4. Grid fade in (image appears)
    .to(".services-grid", {
      autoAlpha: 1,
      duration: 1,
      ease: "power2.out"
    }, "-=0.6");

  return servicesTimeline;
}

// Custom grid sliding animation for specific divs
export function initGridSlidingAnimation() {
  // Wait for initial fade in to complete
  setTimeout(() => {
    // Create repeating timeline for the specific div animations
    const gridSlidingTimeline = gsap.timeline({
      repeat: -1, // Repeat infinitely
      repeatDelay: 2, // No delay between repeats
      scrollTrigger: {
        trigger: ".services-grid",
        start: "top 60%",
        end: "bottom 40%",
        toggleActions: "play pause resume pause"
      }
    });

    // Get the specific divs by their position in the grid
    const div1 = document.querySelector('.grid-item:nth-child(1)'); // Div #1 - Top left
    const div5 = document.querySelector('.grid-item:nth-child(5)'); // Div #5 - Bottom center
    const div7 = document.querySelector('.grid-item:nth-child(7)'); // Div #7 - Bottom right

    // Animate the three specific divs with proper delays
    gridSlidingTimeline
      // Phase 1: Scale to zero (collapse)
      .to(div1, {
        scaleX: 0, // Scale to zero (collapse)
        transformOrigin: "right center", // Right edge stays fixed, left side moves toward right
        duration: 2,
        ease: "power2.inOut"
      })
      .to(div5, {
        scaleX: 0,
        transformOrigin: "right center", // Scale from right, so left side moves toward right
        duration: 2,
        ease: "power2.inOut"
      }, "-=1") // Start at same time as div1
      .to(div7, {
        height: 0,
        transformOrigin: "top center", // Collapse from top
        duration: 2,
        ease: "power2.inOut"
      }, "-=1") // Start at same time as div1

      // Phase 2: Wait 1 second at collapsed state
      .to({}, { duration: 2 })

      // Phase 3: Scale back to original (expand)
      .to(div1, {
        scaleX: 1, // Back to original scale
        duration: 2,
        ease: "power2.inOut"
      })
      .to(div5, {
        scaleX: 1, // Back to original scale
        duration: 2,
        ease: "power2.inOut"
      }, "-=1") // Start at same time
      .to(div7, {
        height: "267px", // Back to original height
        duration: 2,
        ease: "power2.inOut"
      }, "-=1") // Start at same time

      // Phase 4: Wait 1 second at original state before next repeat
      .to({}, { duration: 1 });

  }, 5000); // Start after initial animation completes
}

// Remove the old floating animation - replaced with sliding animation

// Professional hover effects for service items
export function initServiceHoverEffects() {
  const serviceItems = document.querySelectorAll('.service-item');

  serviceItems.forEach(item => {
    const serviceNumber = item.querySelector('.service-number');
    const serviceTitle = item.querySelector('.service-title');

    item.addEventListener('mouseenter', () => {
      gsap.to(item, {
        y: -8,
        scale: 1.02,
        boxShadow: "0 25px 50px rgba(0,0,0,0.15)",
        duration: 0.4,
        ease: "power2.out"
      });

      if (serviceNumber) {
        gsap.to(serviceNumber, {
          scale: 1.1,
          rotation: 5,
          duration: 0.4,
          ease: "back.out(1.7)"
        });
      }

      if (serviceTitle) {
        gsap.to(serviceTitle, {
          x: 5,
          duration: 0.4,
          ease: "power2.out"
        });
      }
    });

    item.addEventListener('mouseleave', () => {
      gsap.to(item, {
        y: 0,
        scale: 1,
        boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
        duration: 0.4,
        ease: "power2.out"
      });

      if (serviceNumber) {
        gsap.to(serviceNumber, {
          scale: 1,
          rotation: 0,
          duration: 0.4,
          ease: "power2.out"
        });
      }

      if (serviceTitle) {
        gsap.to(serviceTitle, {
          x: 0,
          duration: 0.4,
          ease: "power2.out"
        });
      }
    });
  });
}

// Grid items hover effects (simplified for image grid)
export function initGridHoverEffects() {
  const gridItems = document.querySelectorAll('.grid-item');

  gridItems.forEach(item => {
    item.addEventListener('mouseenter', () => {
      gsap.to(item, {
        scale: 1.05,
        boxShadow: "0 10px 25px rgba(0,0,0,0.3)",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    item.addEventListener('mouseleave', () => {
      gsap.to(item, {
        scale: 1,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  });
}


