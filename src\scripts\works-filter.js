import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Works Filter and Navigation for Our Business Page
export function initWorksFilter() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Get DOM elements
  const workTabs = Array.from(document.querySelectorAll('.work-tab')); // Convert to array
  const templateCards = Array.from(document.querySelectorAll('.template-card')); // Convert to array
  const templatesGrid = document.querySelector('.templates-grid');
  const downArrow = document.getElementById('worksDownArrow');

  // Early return if this is not the works page or elements don't exist
  if (!workTabs.length || !templateCards.length || !templatesGrid) {
    return;
  }

  let currentFilter = 'all';
  let isAnimating = false;
  let currentPage = 0;
  const cardsPerPage = 3; // Show 3 cards at a time

  // Initialize - hide all cards except first page
  function initializeCards() {
    templateCards.forEach((card, index) => {
      if (index >= cardsPerPage) {
        gsap.set(card, { autoAlpha: 0, display: 'none' });
      } else {
        gsap.set(card, { autoAlpha: 1, display: 'block' });
      }
    });
    updateDownArrowVisibility();
  }

  // Filter templates by category
  function filterTemplates(category) {
    if (isAnimating || category === currentFilter) return;

    isAnimating = true;
    currentFilter = category;
    currentPage = 0; // Reset to first page when filtering

    // Update active tab
    updateActiveTab(category);

    // Animate cards out
    const timeline = gsap.timeline({
      onComplete: () => {
        // Show filtered cards
        showFilteredCards(category);
        isAnimating = false;
      }
    });

    // Animate visible cards out
    const visibleCards = templateCards.filter(card =>
      card.style.display !== 'none'
    );

    timeline.to(visibleCards, {
      autoAlpha: 0,
      y: -30,
      scale: 0.95,
      rotationY: -15,
      filter: "blur(3px)",
      duration: 0.4,
      stagger: {
        amount: 0.2,
        from: "end",
        ease: "power2.in"
      },
      ease: "power2.in"
    });
  }

  // Update active tab styling with stagger animation
  function updateActiveTab(category) {
    // Create wave effect from active tab
    const activeTab = document.querySelector(`[data-category="${category}"]`);
    const activeIndex = workTabs.indexOf(activeTab);

    workTabs.forEach((tab, index) => {
      tab.classList.remove('active');
      tab.classList.remove('bg-secondary-blue', 'text-white');
      tab.classList.add('bg-white', 'text-secondary-blue');

      // Create wave effect based on distance from active tab
      const distance = Math.abs(index - activeIndex);
      const delay = distance * 0.05;

      gsap.to(tab, {
        scale: 0.95,
        duration: 0.1,
        delay: delay,
        ease: "power2.out",
        yoyo: true,
        repeat: 1
      });
    });

    if (activeTab) {
      activeTab.classList.add('active');
      activeTab.classList.remove('bg-white', 'text-secondary-blue');
      activeTab.classList.add('bg-secondary-blue', 'text-white');

      // Special animation for the active tab
      gsap.to(activeTab, {
        scale: 1.1,
        duration: 0.2,
        ease: "back.out(1.7)",
        yoyo: true,
        repeat: 1
      });
    }
  }

  // Show filtered cards with pagination
  function showFilteredCards(category) {
    const filteredCards = templateCards.filter(card => {
      const cardCategory = card.getAttribute('data-category');
      return category === 'all' || cardCategory === category;
    });

    // Hide all cards first
    templateCards.forEach(card => {
      card.style.display = 'none';
      gsap.set(card, { autoAlpha: 0 });
    });

    // Show cards for current page
    const startIndex = currentPage * cardsPerPage;
    const endIndex = startIndex + cardsPerPage;
    const cardsToShow = filteredCards.slice(startIndex, endIndex);

    // Enhanced stagger animation for cards
    gsap.set(cardsToShow, {
      autoAlpha: 0,
      y: 50,
      scale: 0.9,
      rotationY: 15,
      filter: "blur(5px)"
    });

    cardsToShow.forEach(card => {
      card.style.display = 'block';
    });

    gsap.to(cardsToShow, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: {
        amount: 0.6,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    });

    updateDownArrowVisibility();
  }

  // Handle down arrow click
  function handleDownArrow() {
    if (isAnimating) return;

    const filteredCards = templateCards.filter(card => {
      const cardCategory = card.getAttribute('data-category');
      return currentFilter === 'all' || cardCategory === currentFilter;
    });

    const totalPages = Math.ceil(filteredCards.length / cardsPerPage);

    if (currentPage < totalPages - 1) {
      currentPage++;
      showFilteredCards(currentFilter);

      // Enhanced down arrow animation with ripple effect
      gsap.to(downArrow, {
        scale: 0.9,
        rotation: 180,
        duration: 0.2,
        ease: "power2.inOut"
      });

      gsap.to(downArrow, {
        scale: 1,
        rotation: 0,
        duration: 0.3,
        delay: 0.2,
        ease: "back.out(1.7)"
      });

      // Create ripple effect around down arrow
      const ripple = document.createElement('div');
      ripple.style.cssText = `
        position: absolute;
        width: 60px;
        height: 60px;
        border: 2px solid rgba(36, 88, 167, 0.3);
        border-radius: 50%;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0);
        pointer-events: none;
        z-index: -1;
      `;
      downArrow.style.position = 'relative';
      downArrow.appendChild(ripple);

      gsap.to(ripple, {
        scale: 2,
        opacity: 0,
        duration: 0.6,
        ease: "power2.out",
        onComplete: () => ripple.remove()
      });

      // Scroll to show new cards
      setTimeout(() => {
        templatesGrid.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }, 300);
    }
  }

  // Update down arrow visibility
  function updateDownArrowVisibility() {
    const filteredCards = templateCards.filter(card => {
      const cardCategory = card.getAttribute('data-category');
      return currentFilter === 'all' || cardCategory === currentFilter;
    });

    const totalPages = Math.ceil(filteredCards.length / cardsPerPage);
    const hasMorePages = currentPage < totalPages - 1;

    if (downArrow) {
      if (hasMorePages) {
        gsap.to(downArrow, {
          autoAlpha: 1,
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      } else {
        gsap.to(downArrow, {
          autoAlpha: 0.5,
          scale: 0.8,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    }
  }

  // Add click event listeners to tabs
  workTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      const category = tab.getAttribute('data-category');
      filterTemplates(category);
    });

    // Add keyboard support
    tab.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        const category = tab.getAttribute('data-category');
        filterTemplates(category);
      }
    });

    // Add hover effects
    tab.addEventListener('mouseenter', () => {
      if (!tab.classList.contains('active')) {
        gsap.to(tab, {
          scale: 1.05,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });

    tab.addEventListener('mouseleave', () => {
      if (!tab.classList.contains('active')) {
        gsap.to(tab, {
          scale: 1,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });

    // Add focus styles
    tab.addEventListener('focus', () => {
      if (!tab.classList.contains('active')) {
        gsap.to(tab, {
          scale: 1.02,
          duration: 0.2,
          ease: "power2.out"
        });
      }
    });

    tab.addEventListener('blur', () => {
      if (!tab.classList.contains('active')) {
        gsap.to(tab, {
          scale: 1,
          duration: 0.2,
          ease: "power2.out"
        });
      }
    });
  });

  // Add down arrow event listener
  if (downArrow) {
    downArrow.addEventListener('click', handleDownArrow);

    // Add keyboard support for down arrow
    downArrow.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleDownArrow();
      }
    });

    // Enhanced floating animation for down arrow
    gsap.to(downArrow, {
      y: -8,
      rotation: 5,
      duration: 2.5,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    // Add subtle scale animation
    gsap.to(downArrow, {
      scale: 1.05,
      duration: 3,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1,
      delay: 0.5
    });
  }

  // Initialize the cards display
  initializeCards();

  // Add scroll-triggered animations for the section
  const worksSection = document.querySelector('.container');
  if (worksSection && workTabs.length > 0) {
    // Get all section children for stagger animation
    const sectionTitle = worksSection.querySelector('h2');
    const sectionSubtitle = worksSection.querySelector('p');
    const tabsContainer = worksSection.querySelector('.flex.flex-wrap');

    // Set initial states for elements that exist
    const titleElements = [sectionTitle, sectionSubtitle].filter(el => el !== null);
    if (titleElements.length > 0) {
      gsap.set(titleElements, {
        autoAlpha: 0,
        y: 50,
        scale: 0.9
      });
    }

    if (workTabs.length > 0) {
      gsap.set(workTabs, {
        autoAlpha: 0,
        y: 30,
        scale: 0.8,
        rotationX: -15
      });
    }

    if (downArrow) {
      gsap.set(downArrow, {
        autoAlpha: 0,
        y: 20,
        scale: 0.5
      });
    }

    if (templateCards.length > 0) {
      gsap.set(templateCards, {
        autoAlpha: 0,
        y: 80,
        scale: 0.9,
        rotationY: 15
      });
    }

    const sectionTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: worksSection,
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    // Enhanced stagger animation sequence
    let timelinePosition = 0;

    // Animate section title and subtitle first (if they exist)
    if (titleElements.length > 0) {
      sectionTimeline.to(titleElements, {
        autoAlpha: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        stagger: 0.2,
        ease: "back.out(1.4)"
      });
      timelinePosition = "-=0.4";
    }

    // Animate tabs with enhanced stagger (if they exist)
    if (workTabs.length > 0) {
      sectionTimeline.to(workTabs, {
        autoAlpha: 1,
        y: 0,
        scale: 1,
        rotationX: 0,
        duration: 0.6,
        stagger: {
          amount: 0.8,
          from: "center",
          ease: "power2.out"
        },
        ease: "back.out(1.4)"
      }, timelinePosition);
      timelinePosition = "-=0.2";
    }

    // Animate down arrow (if it exists)
    if (downArrow) {
      sectionTimeline.to(downArrow, {
        autoAlpha: 1,
        y: 0,
        scale: 1,
        duration: 0.5,
        ease: "back.out(1.7)"
      }, timelinePosition);
      timelinePosition = "-=0.3";
    }

    // Animate initial cards with enhanced stagger (if they exist)
    if (templateCards.length > 0) {
      sectionTimeline.to(templateCards.slice(0, cardsPerPage), {
        autoAlpha: 1,
        y: 0,
        scale: 1,
        rotationY: 0,
        duration: 0.8,
        stagger: {
          amount: 0.6,
          from: "start",
          ease: "power2.out"
        },
        ease: "back.out(1.4)"
      }, timelinePosition);
    }

    // Add individual hover animations for tabs during scroll (if they exist)
    if (workTabs.length > 0) {
      workTabs.forEach((tab, index) => {
        gsap.to(tab, {
          y: -2,
          duration: 2 + (index * 0.3),
          ease: "power2.inOut",
          yoyo: true,
          repeat: -1,
          delay: index * 0.1
        });
      });
    }
  }
}
