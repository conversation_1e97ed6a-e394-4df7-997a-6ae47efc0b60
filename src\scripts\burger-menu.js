import { gsap } from 'gsap';

// Burger menu functionality with GSAP animations and RTL/LTR support
export function initBurgerMenu() {
  const burgerBtn = document.getElementById('burgerMenuBtn');
  const mobileMenu = document.getElementById('mobileMenu');
  const mobileMenuOverlay = document.getElementById('mobileMenuOverlay');
  const closeMobileMenu = document.getElementById('closeMobileMenu');
  const mobileLangToggle = document.getElementById('mobileLangToggle');
  const html = document.documentElement;

  if (!burgerBtn || !mobileMenu || !mobileMenuOverlay) return;

  let isMenuOpen = false;
  let menuTimeline = null;

  // Get direction for animations
  function getDirection() {
    return html.getAttribute('dir') === 'rtl' ? 1 : -1;
  }

  // Initialize menu position based on direction
  function initializeMenuPosition() {
    const direction = getDirection();
    const translateX = direction === 1 ? '100%' : '-100%';

    gsap.set(mobileMenu, {
      x: translateX,
      autoAlpha: 1
    });

    // Position menu on correct side
    if (direction === 1) {
      mobileMenu.classList.remove('left-0');
      mobileMenu.classList.add('right-0');
    } else {
      mobileMenu.classList.remove('right-0');
      mobileMenu.classList.add('left-0');
    }
  }

  // Burger button animation
  function animateBurgerButton(isOpen) {
    const lines = burgerBtn.querySelectorAll('.burger-line');

    if (isOpen) {
      // Transform to X
      gsap.to(lines[0], {
        rotation: 45,
        y: 6,
        duration: 0.3,
        ease: "power2.inOut"
      });
      gsap.to(lines[1], {
        autoAlpha: 0,
        duration: 0.2,
        ease: "power2.inOut"
      });
      gsap.to(lines[2], {
        rotation: -45,
        y: -6,
        duration: 0.3,
        ease: "power2.inOut"
      });
    } else {
      // Transform back to hamburger
      gsap.to(lines[0], {
        rotation: 0,
        y: 0,
        duration: 0.3,
        ease: "power2.inOut"
      });
      gsap.to(lines[1], {
        autoAlpha: 1,
        duration: 0.2,
        delay: 0.1,
        ease: "power2.inOut"
      });
      gsap.to(lines[2], {
        rotation: 0,
        y: 0,
        duration: 0.3,
        ease: "power2.inOut"
      });
    }
  }

  // Open menu animation
  function openMenu() {
    if (isMenuOpen || menuTimeline) return;

    isMenuOpen = true;
    const direction = getDirection();

    // Prevent body scroll
    document.body.style.overflow = 'hidden';

    // Create timeline
    menuTimeline = gsap.timeline({
      onComplete: () => {
        menuTimeline = null;
      }
    });

    // Set initial states for menu items
    gsap.set('.mobile-nav-item', {
      autoAlpha: 0,
      x: 50 * direction,
      y: 30,
      skewX: 10 * direction,
      filter: "blur(5px)"
    });

    gsap.set('.mobile-menu-actions', {
      autoAlpha: 0,
      y: 40,
      scale: 0.9,
      filter: "blur(3px)"
    });

    // Animation sequence
    menuTimeline
      // Show overlay
      .to(mobileMenuOverlay, {
        autoAlpha: 1,
        duration: 0.3,
        ease: "power2.out"
      })
      // Slide in menu
      .to(mobileMenu, {
        x: 0,
        duration: 0.4,
        ease: "power3.out"
      }, "-=0.1")
      // Animate menu items with stagger
      .to('.mobile-nav-item', {
        autoAlpha: 1,
        x: 0,
        y: 0,
        skewX: 0,
        filter: "blur(0px)",
        duration: 0.6,
        stagger: {
          amount: 0.8,
          from: "start",
          ease: "back.out(1.7)"
        }
      }, "-=0.2")
      // Animate actions
      .to('.mobile-menu-actions', {
        autoAlpha: 1,
        y: 0,
        scale: 1,
        filter: "blur(0px)",
        duration: 0.5,
        ease: "back.out(1.7)"
      }, "-=0.4");

    // Animate burger button
    animateBurgerButton(true);
  }

  // Close menu animation
  function closeMenu() {
    if (!isMenuOpen || menuTimeline) return;

    isMenuOpen = false;
    const direction = getDirection();

    // Create timeline
    menuTimeline = gsap.timeline({
      onComplete: () => {
        document.body.style.overflow = '';
        menuTimeline = null;
      }
    });

    // Animation sequence
    menuTimeline
      // Hide menu items
      .to('.mobile-nav-item', {
        autoAlpha: 0,
        x: -30 * direction,
        y: -20,
        skewX: -8 * direction,
        filter: "blur(3px)",
        duration: 0.3,
        stagger: {
          amount: 0.4,
          from: "end",
          ease: "power2.in"
        }
      })
      // Hide actions
      .to('.mobile-menu-actions', {
        autoAlpha: 0,
        y: 20,
        scale: 0.95,
        filter: "blur(2px)",
        duration: 0.3,
        ease: "power2.in"
      }, "-=0.2")
      // Slide out menu
      .to(mobileMenu, {
        x: direction === 1 ? '100%' : '-100%',
        duration: 0.4,
        ease: "power3.in"
      }, "-=0.1")
      // Hide overlay
      .to(mobileMenuOverlay, {
        autoAlpha: 0,
        duration: 0.3,
        ease: "power2.in"
      }, "-=0.3");

    // Animate burger button
    animateBurgerButton(false);
  }

  // Mobile language toggle functionality
  function initMobileLangToggle() {
    if (!mobileLangToggle) return;

    mobileLangToggle.addEventListener('click', () => {
      const currentLang = html.getAttribute('lang');

      if (currentLang === 'ar') {
        // Switch to English
        html.setAttribute('lang', 'en');
        html.setAttribute('dir', 'ltr');
        mobileLangToggle.textContent = 'AR';

        // Update navigation text
        updateMobileNavigationText('en');
      } else {
        // Switch to Arabic
        html.setAttribute('lang', 'ar');
        html.setAttribute('dir', 'rtl');
        mobileLangToggle.textContent = 'EN';

        // Update navigation text
        updateMobileNavigationText('ar');
      }

      // Reinitialize menu position after direction change
      if (isMenuOpen) {
        closeMenu();
        setTimeout(() => {
          initializeMenuPosition();
        }, 500);
      } else {
        initializeMenuPosition();
      }

      // Also update desktop language toggle
      const desktopLangToggle = document.getElementById('langToggle');
      if (desktopLangToggle) {
        desktopLangToggle.textContent = mobileLangToggle.textContent;
      }
    });
  }

  // Update mobile navigation text
  function updateMobileNavigationText(lang) {
    const navItems = {
      ar: {
        home: 'الرئيسية',
        about: 'نبذة عنا',
        services: 'خدماتنا',
        portfolio: 'أعمالــنا',
        blog: 'المدونــة',
        brochure: 'الكتيب التعريفي',
        profile: 'بروفايل الشركة'
      },
      en: {
        home: 'Home',
        about: 'About Us',
        services: 'Our Services',
        portfolio: 'Our Work',
        blog: 'Blog',
        brochure: 'Brochure',
        profile: 'Company Profile'
      }
    };

    const texts = navItems[lang];

    // Update mobile navigation items
    const mobileNavItems = mobileMenu.querySelectorAll('[data-nav]');
    mobileNavItems.forEach(item => {
      const navKey = item.getAttribute('data-nav');
      if (texts[navKey]) {
        item.textContent = texts[navKey];
      }
    });
  }

  // Handle window resize
  function handleResize() {
    if (window.innerWidth >= 1024 && isMenuOpen) {
      closeMenu();
    }
    initializeMenuPosition();
  }

  // Event listeners
  burgerBtn.addEventListener('click', openMenu);
  if (closeMobileMenu) {
    closeMobileMenu.addEventListener('click', closeMenu);
  }
  mobileMenuOverlay.addEventListener('click', closeMenu);

  // Close menu on escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && isMenuOpen) {
      closeMenu();
    }
  });

  // Listen for custom close event from smooth scrolling
  document.addEventListener('closeMobileMenu', () => {
    if (isMenuOpen) {
      closeMenu();
    }
  });

  // Handle window resize
  window.addEventListener('resize', handleResize);

  // Initialize
  initializeMenuPosition();
  initMobileLangToggle();

  // Sync with desktop language toggle
  const desktopLangToggle = document.getElementById('langToggle');
  if (desktopLangToggle) {
    desktopLangToggle.addEventListener('click', () => {
      setTimeout(() => {
        const currentLang = html.getAttribute('lang');
        mobileLangToggle.textContent = currentLang === 'ar' ? 'EN' : 'AR';
        initializeMenuPosition();
      }, 50);
    });
  }
}
