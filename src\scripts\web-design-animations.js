import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// Professional Web Design Page Animations
export function initWebDesignAnimations() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Initialize all section animations
  initBreadcrumbsAnimation();
  initSubHeaderAnimation();
  initBestCompanyAnimation();
  initFeaturesAnimation();
  initNewestWorksAnimation();
  initChooseFeaturesAnimation();
  initRelatedWorksAnimation();
  initDesignYourselfAnimation();
}

// 1. Breadcrumbs Section Animation
function initBreadcrumbsAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Check if breadcrumbs section exists
  const breadcrumbsSection = document.querySelector('.breadcrumbs-section');
  if (!breadcrumbsSection) {
    return;
  }

  // Clear any existing animations on these elements first
  gsap.killTweensOf(".breadcrumbs-section .banner-text");
  gsap.killTweensOf(".breadcrumbs-section ul li");
  gsap.killTweensOf(".breadcrumbs-section .floating-icon");

  // Set initial states
  gsap.set(".breadcrumbs-section .banner-text", {
    autoAlpha: 0,
    y: 50,
    scale: 0.8,
    skewY: 5,
    filter: "blur(8px)"
  });

  gsap.set(".breadcrumbs-section ul li", {
    autoAlpha: 0,
    x: 30 * xDir,
    scale: 0.8
  });

  gsap.set(".breadcrumbs-section .floating-icon", {
    autoAlpha: 0,
    scale: 0.6,
    rotation: 20,
    filter: "blur(8px)"
  });

  // Create timeline for breadcrumbs animation
  const breadcrumbTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".breadcrumbs-section",
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });

  // Animation sequence
  breadcrumbTimeline
    // 1. Navigation links first
    .to(".breadcrumbs-section ul li", {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      duration: 0.6,
      stagger: 0.1,
      ease: "power2.out"
    })

    // 2. Banner text
    .to(".breadcrumbs-section .banner-text", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.7)"
    }, "-=0.3")

    // 3. Floating icons
    .to(".breadcrumbs-section .floating-icon", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: 0.15,
      ease: "back.out(1.7)"
    }, "-=0.5");

  // Start floating animations after entrance
  setTimeout(() => {
    const floatingIcons = document.querySelectorAll('.breadcrumbs-section .floating-icon');

    floatingIcons.forEach((icon, index) => {
      gsap.to(icon, {
        y: "random(-20, 20)",
        x: "random(-15, 15)",
        rotation: "random(-10, 10)",
        duration: "random(2, 4)",
        repeat: -1,
        yoyo: true,
        ease: "sine.inOut",
        delay: index * 0.2
      });
    });
  }, 2000);
}

// 2. Sub Header Animation
function initSubHeaderAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states
  gsap.set(".sub-header-section p", {
    autoAlpha: 0,
    x: -50 * xDir,
    scale: 0.9
  });

  gsap.set(".sub-header-section ul li", {
    autoAlpha: 0,
    y: 20,
    scale: 0.8
  });

  gsap.set(".sub-header-section button", {
    autoAlpha: 0,
    x: 50 * xDir,
    scale: 0.8
  });

  // Animation timeline
  const subHeaderTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".sub-header-section",
      start: "top 80%",
      toggleActions: "play none none reverse"
    }
  });

  subHeaderTl
    .to(".sub-header-section p", {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      duration: 0.6,
      ease: "back.out(1.4)"
    })
    .to(".sub-header-section ul li", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      duration: 0.4,
      stagger: 0.1,
      ease: "power2.out"
    }, "-=0.3")
    .to(".sub-header-section button", {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      duration: 0.6,
      ease: "back.out(1.4)"
    }, "-=0.2");
}

// 3. Best Company Section Animation
function initBestCompanyAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Set initial states for title and subtitle
  gsap.set(".best-company-section .section-title", {
    autoAlpha: 0,
    y: 60,
    scale: 0.8,
    skewY: 5,
    filter: "blur(8px)"
  });

  gsap.set(".best-company-section .section-title span", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: "left center"
  });

  gsap.set(".best-company-section .section-subtitle", {
    autoAlpha: 0,
    y: 30,
    x: 20 * xDir
  });

  // Set initial states for images
  gsap.set(".best-company-section .flex-1:first-child img", {
    autoAlpha: 0,
    scale: 0.8,
    y: 50,
    rotationY: 15,
    filter: "blur(6px)"
  });

  // Set initial states for content
  gsap.set(".best-company-section .flex-1:last-child h2", {
    autoAlpha: 0,
    x: 60 * xDir,
    scale: 0.9,
    skewX: 5 * skewXDir
  });

  gsap.set(".best-company-section .flex-1:last-child p", {
    autoAlpha: 0,
    y: 40,
    filter: "blur(4px)"
  });

  gsap.set(".best-company-section .flex-1:last-child .flex.gap-3", {
    autoAlpha: 0,
    y: 30,
    scale: 0.8
  });

  // Animation timeline
  const bestCompanyTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".best-company-section",
      start: "top 75%",
      toggleActions: "play none none reverse"
    }
  });

  bestCompanyTl
    // Title animation
    .to(".best-company-section .section-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    })
    // Yellow underline
    .to(".best-company-section .section-title span", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")
    // Subtitle
    .to(".best-company-section .section-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.3")
    // Images stagger
    .to(".best-company-section .flex-1:first-child img", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: 0.2,
      ease: "back.out(1.4)"
    }, "-=0.4")
    // Content heading
    .to(".best-company-section .flex-1:last-child h2", {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      skewX: 0,
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.6")
    // Content paragraph
    .to(".best-company-section .flex-1:last-child p", {
      autoAlpha: 1,
      y: 0,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")
    // Features
    .to(".best-company-section .flex-1:last-child .flex.gap-3", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      duration: 0.6,
      stagger: 0.1,
      ease: "back.out(1.4)"
    }, "-=0.3");
}

// 4. Features Section Animation
function initFeaturesAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states
  gsap.set(".features-section .features-title", {
    autoAlpha: 0,
    y: 50,
    scale: 0.8,
    skewY: 5
  });

  gsap.set(".features-section .features-title span", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: "left center"
  });

  gsap.set(".features-section ul li", {
    autoAlpha: 0,
    y: 40,
    x: 30 * xDir,
    scale: 0.8,
    rotationX: 15,
    filter: "blur(6px)"
  });

  // Animation timeline
  const featuresTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".features-section",
      start: "top 75%",
      toggleActions: "play none none reverse"
    }
  });

  featuresTl
    .to(".features-section .features-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      duration: 0.8,
      ease: "back.out(1.7)"
    })
    .to(".features-section .features-title span", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")
    .to(".features-section ul li", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      scale: 1,
      rotationX: 0,
      filter: "blur(0px)",
      duration: 0.6,
      stagger: {
        amount: 0.8,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    }, "-=0.3");
}

// 5. Newest Works Section Animation
function initNewestWorksAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states for header
  gsap.set(".newest-works-section .section-title", {
    autoAlpha: 0,
    y: 60,
    scale: 0.8,
    skewY: 5,
    filter: "blur(8px)"
  });

  gsap.set(".newest-works-section .section-title span", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: "left center"
  });

  gsap.set(".newest-works-section .section-subtitle", {
    autoAlpha: 0,
    y: 30,
    x: 20 * xDir
  });

  // Set initial states for tabs
  gsap.set(".newest-works-section .work-tab", {
    autoAlpha: 0,
    y: 30,
    scale: 0.8,
    rotationX: -15
  });

  // Set initial states for content
  gsap.set(".newest-works-section .flex.items-center.justify-between .flex-1:first-child img", {
    autoAlpha: 0,
    scale: 0.8,
    x: -60 * xDir,
    rotationY: 20,
    filter: "blur(8px)"
  });

  gsap.set(".newest-works-section .flex.items-center.justify-between .basis-\\[60\\%\\]", {
    autoAlpha: 0,
    x: 80 * xDir,
    scale: 0.9,
    rotationY: -15,
    filter: "blur(6px)"
  });

  // Animation timeline
  const newestWorksTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".newest-works-section",
      start: "top 75%",
      toggleActions: "play none none reverse"
    }
  });

  newestWorksTl
    .to(".newest-works-section .section-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    })
    .to(".newest-works-section .section-title span", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")
    .to(".newest-works-section .section-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.3")
    .to(".newest-works-section .work-tab", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      duration: 0.5,
      stagger: 0.1,
      ease: "back.out(1.4)"
    }, "-=0.4")
    .to(".newest-works-section .flex.items-center.justify-between .flex-1:first-child img", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.3")
    .to(".newest-works-section .flex.items-center.justify-between .basis-\\[60\\%\\]", {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.6");
}

// 6. Choose Features Section Animation
function initChooseFeaturesAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states for left column items
  gsap.set(".choose-features-section #leftColumn li", {
    autoAlpha: 0,
    x: -80 * xDir,
    y: 30,
    scale: 0.8,
    rotationY: 20,
    filter: "blur(6px)"
  });

  // Set initial states for right column items
  gsap.set(".choose-features-section #rightColumn li", {
    autoAlpha: 0,
    x: 80 * xDir,
    y: 30,
    scale: 0.8,
    rotationY: -20,
    filter: "blur(6px)"
  });

  // Set initial states for drop zone
  gsap.set(".choose-features-section #dropZone", {
    autoAlpha: 0,
    scale: 0.8,
    y: 50,
    filter: "blur(8px)"
  });

  // Animation timeline
  const chooseFeaturesTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".choose-features-section",
      start: "top 75%",
      toggleActions: "play none none reverse"
    }
  });

  chooseFeaturesTl
    .to(".choose-features-section #dropZone", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    })
    .to(".choose-features-section #leftColumn li", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.6,
      stagger: 0.1,
      ease: "back.out(1.4)"
    }, "-=0.6")
    .to(".choose-features-section #rightColumn li", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.6,
      stagger: 0.1,
      ease: "back.out(1.4)"
    }, "-=0.6");
}

// 7. Related Works Section Animation
function initRelatedWorksAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  // Set initial states for header
  gsap.set(".related-works-section .section-title", {
    autoAlpha: 0,
    y: 60,
    scale: 0.8,
    skewY: 5,
    filter: "blur(8px)"
  });

  gsap.set(".related-works-section .section-title span", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: "left center"
  });

  gsap.set(".related-works-section .section-subtitle", {
    autoAlpha: 0,
    y: 30,
    x: 20 * xDir
  });

  // Set initial states for template cards
  gsap.set(".related-works-section .template-card", {
    autoAlpha: 0,
    y: 60,
    scale: 0.8,
    rotationX: 15,
    filter: "blur(8px)"
  });

  // Set initial states for navigation buttons
  gsap.set(".related-works-section .testimonial-nav button", {
    autoAlpha: 0,
    scale: 0.5,
    rotation: 180
  });

  // Animation timeline
  const relatedWorksTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".related-works-section",
      start: "top 75%",
      toggleActions: "play none none reverse"
    }
  });

  relatedWorksTl
    .to(".related-works-section .section-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    })
    .to(".related-works-section .section-title span", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")
    .to(".related-works-section .section-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.3")
    .to(".related-works-section .template-card", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      filter: "blur(0px)",
      duration: 0.8,
      stagger: 0.2,
      ease: "back.out(1.4)"
    }, "-=0.4")
    .to(".related-works-section .testimonial-nav button", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      duration: 0.5,
      stagger: 0.1,
      ease: "back.out(1.7)"
    }, "-=0.3");
}

// 8. Enhanced Design Yourself Section Animation
function initDesignYourselfAnimation() {
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Set initial states for floating elements
  gsap.set(".design-floating-envelope", {
    autoAlpha: 0,
    scale: 0.5,
    rotation: 45,
    filter: "blur(4px)"
  });

  gsap.set(".design-floating-circle-right", {
    autoAlpha: 0,
    scale: 0.8,
    rotation: -90,
    filter: "blur(6px)"
  });

  gsap.set(".design-floating-circle-left", {
    autoAlpha: 0,
    scale: 0.6,
    rotation: 90,
    filter: "blur(6px)"
  });

  gsap.set(".design-floating-line", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: "center center"
  });

  gsap.set(".design-floating-dots", {
    autoAlpha: 0,
    scale: 0,
    rotation: 180
  });

  // Set initial states for images with stagger effect
  gsap.set(".design-image-1", {
    autoAlpha: 0,
    x: -100 * xDir,
    y: 50,
    scale: 0.7,
    rotationY: 45,
    filter: "blur(8px)"
  });

  gsap.set(".design-image-2", {
    autoAlpha: 0,
    y: -80,
    scale: 0.6,
    rotationX: 30,
    filter: "blur(10px)"
  });

  gsap.set(".design-image-3", {
    autoAlpha: 0,
    x: 100 * xDir,
    y: 40,
    scale: 0.8,
    rotationY: -45,
    filter: "blur(8px)"
  });

  // Set initial states for text lines
  gsap.set(".design-text-line-1", {
    autoAlpha: 0,
    x: 80 * xDir,
    y: -30,
    scale: 0.8,
    skewX: 10 * skewXDir,
    filter: "blur(6px)"
  });

  gsap.set(".design-text-line-2", {
    autoAlpha: 0,
    x: -60 * xDir,
    y: 20,
    scale: 1.2,
    skewX: -8 * skewXDir,
    filter: "blur(8px)"
  });

  gsap.set(".design-text-line-3", {
    autoAlpha: 0,
    x: 70 * xDir,
    y: 30,
    scale: 0.9,
    skewX: 6 * skewXDir,
    filter: "blur(6px)"
  });

  gsap.set(".design-cta-container", {
    autoAlpha: 0,
    y: 50,
    scale: 0.8,
    filter: "blur(4px)"
  });

  // Main animation timeline
  const designYourselfTl = gsap.timeline({
    scrollTrigger: {
      trigger: ".design-yourself-section",
      start: "top 75%",
      toggleActions: "play none none reverse"
    }
  });

  designYourselfTl
    // Floating elements entrance
    .to(".design-floating-circle-left", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      filter: "blur(0px)",
      duration: 1.2,
      ease: "back.out(1.4)"
    })
    .to(".design-floating-circle-right", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      filter: "blur(0px)",
      duration: 1.0,
      ease: "back.out(1.4)"
    }, "-=0.8")
    .to(".design-floating-line", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.6")
    .to(".design-floating-envelope", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    }, "-=0.4")
    .to(".design-floating-dots", {
      autoAlpha: 1,
      scale: 1,
      rotation: 0,
      duration: 0.6,
      stagger: 0.1,
      ease: "back.out(1.4)"
    }, "-=0.6")

    // Images entrance with stagger
    .to(".design-image-1", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.4")
    .to(".design-image-3", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.6")
    .to(".design-image-2", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      filter: "blur(0px)",
      duration: 1.0,
      ease: "back.out(1.4)"
    }, "-=0.4")

    // Text lines entrance with stagger
    .to(".design-text-line-1", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.6")
    .to(".design-text-line-2", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.5")
    .to(".design-text-line-3", {
      autoAlpha: 1,
      x: 0,
      y: 0,
      scale: 1,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.5")

    // CTA button entrance
    .to(".design-cta-container", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.6,
      ease: "back.out(1.4)"
    }, "-=0.3");

  // Start continuous floating animations after entrance
  setTimeout(() => {
    initDesignYourselfFloatingAnimations();
  }, 3000);
}

// Continuous floating animations for design yourself section
function initDesignYourselfFloatingAnimations() {
  // Floating envelope animation
  gsap.to(".design-floating-envelope", {
    y: "random(-15, 15)",
    x: "random(-10, 10)",
    rotation: "random(-10, 10)",
    duration: "random(3, 5)",
    repeat: -1,
    yoyo: true,
    ease: "sine.inOut"
  });

  // Floating circles animation
  gsap.to(".design-floating-circle-right", {
    rotation: 360,
    duration: 20,
    repeat: -1,
    ease: "none"
  });

  gsap.to(".design-floating-circle-left", {
    rotation: -360,
    duration: 25,
    repeat: -1,
    ease: "none"
  });

  // Floating dots animation
  gsap.to(".design-floating-dots", {
    y: "random(-8, 8)",
    x: "random(-8, 8)",
    scale: "random(0.8, 1.2)",
    duration: "random(2, 4)",
    repeat: -1,
    yoyo: true,
    ease: "sine.inOut",
    stagger: 0.3
  });

  // Subtle floating for images
  gsap.to(".design-image-1, .design-image-3", {
    y: "random(-5, 5)",
    rotation: "random(-2, 2)",
    duration: "random(4, 6)",
    repeat: -1,
    yoyo: true,
    ease: "sine.inOut",
    stagger: 0.5
  });

  gsap.to(".design-image-2", {
    y: "random(-8, 8)",
    rotation: "random(-1, 1)",
    duration: "random(5, 7)",
    repeat: -1,
    yoyo: true,
    ease: "sine.inOut"
  });
}
