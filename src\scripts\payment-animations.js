import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

export function initPaymentAnimations() {
  // Register GSAP plugins
  gsap.registerPlugin(ScrollTrigger);

  // Payment section container
  const paymentSection = document.querySelector('section.container.flex.flex-col.items-center');
  
  if (!paymentSection) return;

  // Get all animated elements
  const paymentImage = paymentSection.querySelector('img[alt="Payment image"]');
  const mainTitle = paymentSection.querySelector('p.font-medium.text-light-blue.text-3xl');
  const countryTabs = paymentSection.querySelectorAll('.country-tab');
  const sectionTitle = paymentSection.querySelector('#payment-title');
  const sectionSubtitle = paymentSection.querySelector('#payment-subtitle');
  const paymentCards = paymentSection.querySelectorAll('.flex.items-center.shadow-md');
  const paginationContainer = paymentSection.querySelector('.pagination-container');

  // Create main timeline
  const mainTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: paymentSection,
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse"
    }
  });

  // 1. Payment image animation - scale and rotate entrance
  if (paymentImage) {
    gsap.set(paymentImage, {
      scale: 0.3,
      rotation: -180,
      opacity: 0
    });

    mainTimeline.to(paymentImage, {
      scale: 1,
      rotation: 0,
      opacity: 1,
      duration: 1.2,
      ease: "back.out(1.7)"
    });
  }

  // 2. Main title animation - slide from top with blur
  if (mainTitle) {
    gsap.set(mainTitle, {
      y: -50,
      opacity: 0,
      filter: "blur(10px)"
    });

    mainTimeline.to(mainTitle, {
      y: 0,
      opacity: 1,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.6");
  }

  // 3. Country tabs animation - staggered entrance from sides
  if (countryTabs.length > 0) {
    countryTabs.forEach((tab, index) => {
      gsap.set(tab, {
        x: index === 0 ? -100 : 100,
        opacity: 0,
        scale: 0.8,
        rotationY: index === 0 ? -45 : 45
      });
    });

    mainTimeline.to(countryTabs, {
      x: 0,
      opacity: 1,
      scale: 1,
      rotationY: 0,
      duration: 0.8,
      ease: "back.out(1.4)",
      stagger: 0.2
    }, "-=0.4");
  }

  // 4. Section title and subtitle animation
  if (sectionTitle) {
    gsap.set(sectionTitle, {
      y: 30,
      opacity: 0,
      scale: 0.9
    });

    mainTimeline.to(sectionTitle, {
      y: 0,
      opacity: 1,
      scale: 1,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.3");
  }

  if (sectionSubtitle) {
    gsap.set(sectionSubtitle, {
      y: 20,
      opacity: 0
    });

    mainTimeline.to(sectionSubtitle, {
      y: 0,
      opacity: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.5");
  }

  // 5. Payment cards animation - sequential reveal with skew
  if (paymentCards.length > 0) {
    paymentCards.forEach((card, index) => {
      gsap.set(card, {
        y: 60,
        opacity: 0,
        scale: 0.8,
        skewY: 5,
        filter: "blur(5px)"
      });
    });

    mainTimeline.to(paymentCards, {
      y: 0,
      opacity: 1,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "power3.out",
      stagger: {
        amount: 0.6,
        from: "start"
      }
    }, "-=0.2");
  }

  // 6. Pagination animation - bounce entrance
  if (paginationContainer) {
    const paginationDots = paginationContainer.querySelectorAll('.pagination-dot');
    
    paginationDots.forEach((dot, index) => {
      gsap.set(dot, {
        y: 30,
        opacity: 0,
        scale: 0.5
      });
    });

    mainTimeline.to(paginationDots, {
      y: 0,
      opacity: 1,
      scale: 1,
      duration: 0.6,
      ease: "bounce.out",
      stagger: 0.1
    }, "-=0.3");
  }

  // Add floating animation to payment image
  if (paymentImage) {
    gsap.to(paymentImage, {
      y: "+=15",
      rotation: "+=3",
      duration: 3,
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1,
      delay: 1.5
    });
  }

  // Add hover effects to payment cards
  paymentCards.forEach(card => {
    const cardImage = card.querySelector('img');
    const cardContent = card.querySelector('div:last-child');

    card.addEventListener('mouseenter', () => {
      gsap.to(card, {
        scale: 1.02,
        y: -5,
        boxShadow: "0 15px 35px rgba(0, 0, 0, 0.15)",
        duration: 0.3,
        ease: "power2.out"
      });

      if (cardImage) {
        gsap.to(cardImage, {
          scale: 1.1,
          rotation: 5,
          duration: 0.3,
          ease: "power2.out"
        });
      }

      if (cardContent) {
        gsap.to(cardContent, {
          x: 5,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });

    card.addEventListener('mouseleave', () => {
      gsap.to(card, {
        scale: 1,
        y: 0,
        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
        duration: 0.3,
        ease: "power2.out"
      });

      if (cardImage) {
        gsap.to(cardImage, {
          scale: 1,
          rotation: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      }

      if (cardContent) {
        gsap.to(cardContent, {
          x: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  });

  // Add continuous floating animation to country tabs
  countryTabs.forEach((tab, index) => {
    gsap.to(tab, {
      y: "+=8",
      duration: 2 + (index * 0.5),
      ease: "sine.inOut",
      yoyo: true,
      repeat: -1,
      delay: 2 + (index * 0.3)
    });
  });

  // Refresh ScrollTrigger on window resize
  window.addEventListener('resize', () => {
    ScrollTrigger.refresh();
  });
}
