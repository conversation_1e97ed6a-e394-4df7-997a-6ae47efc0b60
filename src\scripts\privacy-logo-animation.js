import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

export function initPrivacyLogoAnimation() {
  const privacyLogo = document.getElementById("privacyLogo");
  const logoContainer = document.querySelector(".privacy-logo-container");

  if (!privacyLogo || !logoContainer) {
    console.log("Privacy logo elements not found");
    return;
  }

  console.log("Initializing privacy logo animation");

  // Set initial states
  gsap.set(privacyLogo, {
    scale: 0.2,
    autoAlpha: 0,
    rotationX: 180,
  });

  // Set initial states for individual elements
  gsap.set("#logo-circle-1, #logo-circle-2", {
    strokeDasharray: "1000",
    strokeDashoffset: "1000",
  });

  gsap.set("#logo-base, #logo-main-element, #logo-accent-element", {
    autoAlpha: 0,
    scale: 0.8,
  });

  gsap.set(
    "#logo-text-s, #logo-text-e, #logo-text-r, #logo-text-v, #logo-text-5",
    {
      autoAlpha: 0,
      y: 20,
    }
  );

  // Main entrance animation timeline
  const logoTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: logoContainer,
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse",
    },
  });

  // Logo entrance with scale and rotation
  logoTimeline
    .to(privacyLogo, {
      scale: 1,
      autoAlpha: 1,
      rotationX: 0,
      duration: 1.5,
      ease: "back.out(1.7)",
    })
    // Animate circles with stroke dash
    .to(
      "#logo-circle-1",
      {
        strokeDashoffset: 0,
        duration: 1.2,
        ease: "power2.out",
      },
      "-=1"
    )
    .to(
      "#logo-circle-2",
      {
        strokeDashoffset: 0,
        duration: 1.2,
        ease: "power2.out",
      },
      "-=0.8"
    )
    // Animate base and main elements
    .to(
      "#logo-base",
      {
        autoAlpha: 1,
        scale: 1,
        duration: 0.8,
        ease: "power2.out",
      },
      "-=0.6"
    )
    .to(
      "#logo-main-element",
      {
        autoAlpha: 1,
        scale: 1,
        duration: 0.8,
        ease: "power2.out",
      },
      "-=0.4"
    )
    .to(
      "#logo-accent-element",
      {
        autoAlpha: 1,
        scale: 1,
        duration: 0.8,
        ease: "power2.out",
      },
      "-=0.2"
    )
    // Animate text letters sequentially
    .to(
      "#logo-text-s, #logo-text-e, #logo-text-r, #logo-text-v, #logo-text-5",
      {
        autoAlpha: 1,
        y: 0,
        duration: 0.6,
        stagger: 0.1,
        ease: "power2.out",
      },
      "-=0.4"
    );

  // Continuous floating animation
  gsap.to(privacyLogo, {
    y: -10,
    duration: 3,
    ease: "power1.inOut",
    yoyo: true,
    repeat: -1,
  });

  gsap.to(privacyLogo, {
    x: 5,
    duration: 4,
    ease: "power1.inOut",
    yoyo: true,
    repeat: -1,
  });

  // Hover interaction
  privacyLogo.addEventListener("mouseenter", function () {
    gsap.to(privacyLogo, {
      scale: 1.1,
      rotationY: 360,
      duration: 1.5,
      ease: "power2.out",
    });
  });

  privacyLogo.addEventListener("mouseleave", function () {
    gsap.to(privacyLogo, {
      scale: 1,
      rotationY: 0,
      duration: 0.8,
      ease: "power2.out",
    });
  });

  // Click interaction - replay entrance animation
  privacyLogo.addEventListener("click", function () {
    // Reset and replay the entrance animation
    gsap.set("#logo-circle-1, #logo-circle-2", {
      strokeDashoffset: "1000",
    });

    gsap.set("#logo-base, #logo-main-element, #logo-accent-element", {
      autoAlpha: 0,
      scale: 0.8,
    });

    gsap.set(
      "#logo-text-s, #logo-text-e, #logo-text-r, #logo-text-v, #logo-text-5",
      {
        autoAlpha: 0,
        y: 20,
      }
    );

    // Replay animation
    const replayTimeline = gsap.timeline();

    replayTimeline
      .to("#logo-circle-1", {
        strokeDashoffset: 0,
        duration: 0.8,
        ease: "power2.out",
      })
      .to(
        "#logo-circle-2",
        {
          strokeDashoffset: 0,
          duration: 0.8,
          ease: "power2.out",
        },
        "-=0.6"
      )
      .to(
        "#logo-base",
        {
          autoAlpha: 1,
          scale: 1,
          duration: 0.6,
          ease: "power2.out",
        },
        "-=0.4"
      )
      .to(
        "#logo-main-element",
        {
          autoAlpha: 1,
          scale: 1,
          duration: 0.6,
          ease: "power2.out",
        },
        "-=0.3"
      )
      .to(
        "#logo-accent-element",
        {
          autoAlpha: 1,
          scale: 1,
          duration: 0.6,
          ease: "power2.out",
        },
        "-=0.2"
      )
      .to(
        "#logo-text-s, #logo-text-e, #logo-text-r, #logo-text-v, #logo-text-5",
        {
          autoAlpha: 1,
          y: 0,
          duration: 0.4,
          stagger: 0.08,
          ease: "power2.out",
        },
        "-=0.3"
      );
  });

  console.log("Privacy logo animation initialized successfully");
}
