import { gsap } from "gsap";
import { initSVGLogoAnimation, initSVGLogoHoverEffect } from "./gsap-svg-logo.js";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// Header GSAP Animation Timeline - Responsive
export function initHeaderGSAP() {
  // Create main timeline
  const headerTimeline = gsap.timeline();

  // Check if we're on mobile/tablet (lg breakpoint is 1024px)
  const isMobile = window.innerWidth < 1024;

  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // 1. Initialize and play SVG logo animation
  const svgLogoTimeline = initSVGLogoAnimation();
  headerTimeline.add(svgLogoTimeline);

  // Only animate desktop elements if not on mobile
  if (!isMobile) {
    // Set initial states for desktop elements
    gsap.set(".nav-item", {
      autoAlpha: 0,
      y: 50,
      x: 30 * xDir,
      skewY: 8,
      filter: "blur(5px)"
    });

    gsap.set("#langToggle", {
      autoAlpha: 0,
      scale: 0.6,
      x: 40 * -xDir, // opposite direction for button
      skewX: -10 * skewXDir,
      filter: "blur(3px)"
    });

    gsap.set(".header-profile-btn", {
      autoAlpha: 0,
      scale: 0.6,
      y: -25,
      skewX: 12 * skewXDir,
      filter: "blur(4px)"
    });

    gsap.set(".header-vision", {
      autoAlpha: 0,
      scale: 0.5,
      x: 60 * xDir,
      y: 20,
      skewY: -8,
      filter: "blur(6px)"
    });

    // 2. Navigation links stagger from different directions with skew (start at 0.8s)
    headerTimeline.to(".nav-item", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.5,
      stagger: {
        amount: 0.4,
        from: "start",
        ease: "power2.out"
      },
      ease: "back.out(1.4)"
    }, 0.8);

    // 3. Language button - slide from right/left with skew (start at 1.0s)
    headerTimeline.to("#langToggle", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.4,
      ease: "back.out(1.7)"
    }, 1.0);

    // 4. Profile button
    headerTimeline.to(".header-profile-btn", {
      autoAlpha: 1,
      scale: 1,
      y: 0,
      skewX: 0,
      filter: "blur(0px)",
      duration: 0.4,
      ease: "back.out(1.7)"
    }, "-=0.2");

    // 5. Vision image
    headerTimeline.to(".header-vision", {
      autoAlpha: 1,
      scale: 1,
      x: 0,
      y: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.4,
      ease: "back.out(1.7)"
    }, "-=0.2");
  }

  return headerTimeline;
}

// Old logo hover effect function - replaced by SVG version
// function initLogoHoverEffect() {
//   const logo = document.querySelector(".header-logo");
//
//   if (logo) {
//     // Mouse enter - professional rotation with skew and scale
//     logo.addEventListener("mouseenter", () => {
//       gsap.to(logo, {
//         rotation: 360,
//         scale: 1.5,
//         skewX: 5,
//         skewY: 2,
//         filter: "brightness(1.1) contrast(1.1)",
//         duration: 0.8,
//         ease: "power3.out"
//       });
//     });
//
//     // Mouse leave - smooth return with slight overshoot
//     logo.addEventListener("mouseleave", () => {
//       gsap.to(logo, {
//         rotation: 0,
//         scale: 1,
//         skewX: 0,
//         skewY: 0,
//         filter: "brightness(1) contrast(1)",
//         duration: 0.8,
//         ease: "back.out(1.4)"
//       });
//     });
//   }
// }



// Alternative function with more control
export function initHeaderGSAPAdvanced() {
  // Create main timeline with callbacks
  const tl = gsap.timeline({

  });

  // Initial setup - hide all elements
  const elementsToAnimate = [
    ".header-logo",
    ".nav-item",
    "#langToggle",
    ".header-profile-btn",
    ".header-vision"
  ];

  gsap.set(elementsToAnimate, { opacity: 0 });
  gsap.set(".header-logo", { scale: 0.8, transformOrigin: "center" });
  gsap.set(".nav-item", { y: 30, rotationX: -15 });
  gsap.set(["#langToggle", ".header-profile-btn", ".header-vision"], { scale: 0.9 });

  // Animation sequence
  tl
    // Logo animation with rotation effect
    .to(".header-logo", {
      opacity: 1,
      scale: 1,
      rotationY: 360,
      duration: 1,
      ease: "power2.out"
    })

    // Navigation items with stagger and 3D effect
    .to(".nav-item", {
      opacity: 1,
      y: 0,
      rotationX: 0,
      duration: 0.8,
      stagger: {
        amount: 1.2,
        from: "start",
        ease: "back.out(1.7)"
      }
    }, "-=0.3")

    // Buttons with bounce effect
    .to("#langToggle", {
      opacity: 1,
      scale: 1,
      duration: 0.6,
      ease: "bounce.out"
    }, "-=0.4")

    .to(".header-profile-btn", {
      opacity: 1,
      scale: 1,
      duration: 0.6,
      ease: "bounce.out"
    }, "-=0.4")

    // Vision image with elegant fade
    .to(".header-vision", {
      opacity: 1,
      scale: 1,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.2");

  return tl;
}
