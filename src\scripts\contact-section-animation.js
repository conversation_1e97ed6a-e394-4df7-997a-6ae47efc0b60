import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Contact Section Animation
export function initContactSectionAnimation() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);


  // Get contact section elements
  const contactTitle = document.querySelector('.contact-title');
  const contactSubtitle = document.querySelector('.contact-subtitle');
  const contactUnderline = document.querySelector('.contact-underline');
  const contactCardLeft = document.querySelector('.contact-card-left');
  const contactMapContainer = document.querySelector('.contact-map-container');
  const contactFormContainer = document.querySelector('.contact-form-container');
  const accordionContainer = document.querySelector('.accordion-container');
  const contactMap = document.querySelector('.contact-map');
  const contactForm = document.querySelector('.contact-form');

  // Set initial states for header elements
  if (contactTitle) {
    gsap.set(contactTitle, {
      autoAlpha: 0,
      y: 80,
      scale: 0.8,
      skewY: 6,
      filter: "blur(10px)"
    });
  }

  if (contactSubtitle) {
    gsap.set(contactSubtitle, {
      autoAlpha: 0,
      y: 50,
      x: -40,
      filter: "blur(6px)"
    });
  }

  if (contactUnderline) {
    gsap.set(contactUnderline, {
      autoAlpha: 0,
      scaleX: 0,
      transformOrigin: "left center"
    });
  }

  // Set initial states for content cards
  if (contactCardLeft) {
    gsap.set(contactCardLeft, {
      autoAlpha: 0,
      x: -80,
      scale: 0.9,
      rotationY: -15,
      filter: "blur(8px)"
    });
  }

  if (contactMapContainer) {
    gsap.set(contactMapContainer, {
      autoAlpha: 0,
      y: 60,
      scale: 0.95,
      filter: "blur(6px)"
    });
  }

  if (contactFormContainer) {
    gsap.set(contactFormContainer, {
      autoAlpha: 0,
      x: 80,
      scale: 0.9,
      rotationY: 15,
      filter: "blur(8px)"
    });
  }

  // Header animation timeline
  const headerTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".contact-section",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse",

    }
  });

  // Header animation sequence
  headerTimeline
    // 1. Title animation with professional effects
    .to(contactTitle, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.7)"
    })

    // 2. Yellow underline animation
    .to(contactUnderline, {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.6,
      ease: "power2.out"
    }, "-=0.4")

    // 3. Subtitle slide from left
    .to(contactSubtitle, {
      autoAlpha: 1,
      y: 0,
      x: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.4");

  // Content cards animation timeline
  const cardsTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".contact-section",
      start: "top 60%",
      end: "bottom 20%",
      toggleActions: "play none none reverse",
    }
  });

  // Content cards animation sequence
  cardsTimeline
    // 1. Left card (accordions) slides from left
    .to(contactCardLeft, {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    })

    // 2. Map container fades up from bottom
    .to(contactMapContainer, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.7,
      ease: "power2.out"
    }, "-=0.5")

    // 3. Form container slides from right
    .to(contactFormContainer, {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.6");

  // Form fields stagger animation
  const formFields = document.querySelectorAll('#contactForm .mb-4, #contactForm .mb-6, #contactForm button');
  if (formFields.length > 0) {
    gsap.set(formFields, {
      autoAlpha: 0,
      y: 30,
      scale: 0.95
    });

    const formTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: "#contactForm",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    formTimeline.to(formFields, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      duration: 0.5,
      stagger: 0.1,
      ease: "power2.out"
    });
  }

  // Accordion items stagger animation
  const accordionItems = document.querySelectorAll('.accordion-item');
  if (accordionItems.length > 0) {
    gsap.set(accordionItems, {
      autoAlpha: 0,
      x: -30,
      scale: 0.95
    });

    const accordionTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: ".accordion-container",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    accordionTimeline.to(accordionItems, {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      duration: 0.6,
      stagger: 0.15,
      ease: "back.out(1.4)"
    });
  }

}
