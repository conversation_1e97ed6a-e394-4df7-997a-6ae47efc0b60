// Sequential Projects Card Animation with GSAP
export function initProjectsScrollAnimation() {
  // Check if GSAP and ScrollTrigger are available
  if (typeof gsap === 'undefined' || typeof ScrollTrigger === 'undefined') {
    return;
  }

  // Get the projects container, header, and all project cards
  const projectsContainer = document.querySelector('.container.h-\\[945px\\]');
  const projectHeader = projectsContainer?.querySelector('h2');
  const projectCards = document.querySelectorAll('.project-card');

  if (!projectsContainer || projectCards.length === 0) {
    return;
  }

  // Set initial state - header visible, all cards hidden
  gsap.set(projectHeader, {
    autoAlpha: 1,
    y: 0
  });

  gsap.set(projectCards, {
    autoAlpha: 0,
    y: 100,
    scale: 0.9
  });

  // Variables to track current card and scroll state
  let currentCardIndex = -1; // -1 means header is showing
  let isAnimating = false;

  // Function to show specific card and hide others
  function showCard(cardIndex) {
    if (isAnimating || cardIndex === currentCardIndex) return;

    isAnimating = true;

    // Hide current card (slide up and fade out)
    if (currentCardIndex >= 0) {
      gsap.to(projectCards[currentCardIndex], {
        autoAlpha: 0,
        y: -150,
        scale: 0.8,
        duration: 0.6,
        ease: "power2.in"
      });
    }

    // Show new card (slide up from bottom and fade in)
    if (cardIndex >= 0 && cardIndex < projectCards.length) {
      gsap.fromTo(projectCards[cardIndex],
        {
          autoAlpha: 0,
          y: 150,
          scale: 0.8
        },
        {
          autoAlpha: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          ease: "back.out(1.7)",
          delay: 0.3,
          onComplete: () => {
            isAnimating = false;
          }
        }
      );
    } else {
      setTimeout(() => {
        isAnimating = false;
      }, 600);
    }

    currentCardIndex = cardIndex;
  }

  // Mouse wheel scroll detection within container
  let scrollAccumulator = 0;
  const scrollThreshold = 100; // Amount of scroll needed to trigger card change

  projectsContainer.addEventListener('wheel', (e) => {
    e.preventDefault();

    if (isAnimating) return;

    // Accumulate scroll delta
    scrollAccumulator += e.deltaY;

    // Check if we've scrolled enough to trigger a card change
    if (Math.abs(scrollAccumulator) >= scrollThreshold) {
      if (scrollAccumulator > 0) {
        // Scrolling down - show next card
        const nextCard = currentCardIndex + 1;
        if (nextCard < projectCards.length) {
          showCard(nextCard);
        }
      } else {
        // Scrolling up - show previous card
        const prevCard = currentCardIndex - 1;
        if (prevCard >= -1) { // -1 means back to header only
          if (prevCard === -1) {
            // Hide current card, show only header
            if (currentCardIndex >= 0) {
              gsap.to(projectCards[currentCardIndex], {
                autoAlpha: 0,
                y: 150,
                scale: 0.8,
                duration: 0.6,
                ease: "power2.in"
              });
            }
            currentCardIndex = -1;
          } else {
            showCard(prevCard);
          }
        }
      }

      // Reset accumulator
      scrollAccumulator = 0;
    }

  });

  // Add hover effects for visible cards
  projectCards.forEach((card, index) => {
    card.addEventListener('mouseenter', () => {
      if (currentCardIndex === index) {
        gsap.to(card, {
          scale: 1.05,
          y: -10,
          boxShadow: "0 25px 50px rgba(0,0,0,0.2)",
          duration: 0.4,
          ease: "power2.out"
        });
      }
    });

    card.addEventListener('mouseleave', () => {
      if (currentCardIndex === index) {
        gsap.to(card, {
          scale: 1,
          y: 0,
          boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
          duration: 0.4,
          ease: "power2.out"
        });
      }
    });
  });

  // Keyboard navigation (optional)
  document.addEventListener('keydown', (e) => {
    if (!projectsContainer.matches(':hover')) return;

    if (e.key === 'ArrowDown' || e.key === 'PageDown') {
      e.preventDefault();
      const nextCard = currentCardIndex + 1;
      if (nextCard < projectCards.length) {
        showCard(nextCard);
      }
    } else if (e.key === 'ArrowUp' || e.key === 'PageUp') {
      e.preventDefault();
      const prevCard = currentCardIndex - 1;
      if (prevCard >= -1) {
        if (prevCard === -1) {
          if (currentCardIndex >= 0) {
            gsap.to(projectCards[currentCardIndex], {
              autoAlpha: 0,
              y: 150,
              scale: 0.8,
              duration: 0.6,
              ease: "power2.in"
            });
          }
          currentCardIndex = -1;
        } else {
          showCard(prevCard);
        }
      }
    }
  });

}
