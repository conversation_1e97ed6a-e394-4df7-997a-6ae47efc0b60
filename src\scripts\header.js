// Language toggle functionality
export function initLanguageToggle() {
  const langButton = document.getElementById('langToggle');
  const mobileLangButton = document.getElementById('mobileLangToggle');
  const html = document.documentElement;

  function toggleLanguage() {
    const currentLang = html.getAttribute('lang');

    if (currentLang === 'ar') {
      // Switch to English
      html.setAttribute('lang', 'en');
      html.setAttribute('dir', 'ltr');
      if (langButton) langButton.textContent = 'AR';
      if (mobileLangButton) mobileLangButton.textContent = 'AR';

      // Update navigation text to English
      updateNavigationText('en');
      updateMobileNavigationText('en');
    } else {
      // Switch to Arabic
      html.setAttribute('lang', 'ar');
      html.setAttribute('dir', 'rtl');
      if (langButton) langButton.textContent = 'EN';
      if (mobileLangButton) mobileLangButton.textContent = 'EN';

      // Update navigation text to Arabic
      updateNavigationText('ar');
      updateMobileNavigationText('ar');
    }
  }

  if (langButton) {
    langButton.addEventListener('click', toggleLanguage);
  }
}

// Update navigation text based on language
function updateNavigationText(lang) {
  const navItems = {
    ar: {
      home: 'الرئيسية',
      about: 'نبذة عنا',
      services: 'خدماتنا',
      portfolio: 'أعمالــنا',
      blog: 'المدونــة',
      brochure: 'الكتيب التعريفي',
      profile: 'بروفايل الشركة'
    },
    en: {
      home: 'Home',
      about: 'About Us',
      services: 'Our Services',
      portfolio: 'Our Work',
      blog: 'Blog',
      brochure: 'Brochure',
      profile: 'Company Profile'
    }
  };

  const texts = navItems[lang];

  // Update desktop navigation items
  const desktopNavItems = document.querySelectorAll('.nav-item [data-nav]');
  desktopNavItems.forEach(item => {
    const navKey = item.getAttribute('data-nav');
    if (texts[navKey]) {
      item.textContent = texts[navKey];
    }
  });

  // Update desktop profile button
  const desktopProfileBtn = document.querySelector('.header-profile-btn [data-nav="profile"]');
  if (desktopProfileBtn && texts.profile) {
    desktopProfileBtn.textContent = texts.profile;
  }
}

// Update mobile navigation text
function updateMobileNavigationText(lang) {
  const navItems = {
    ar: {
      home: 'الرئيسية',
      about: 'نبذة عنا',
      services: 'خدماتنا',
      portfolio: 'أعمالــنا',
      blog: 'المدونــة',
      brochure: 'الكتيب التعريفي',
      profile: 'بروفايل الشركة'
    },
    en: {
      home: 'Home',
      about: 'About Us',
      services: 'Our Services',
      portfolio: 'Our Work',
      blog: 'Blog',
      brochure: 'Brochure',
      profile: 'Company Profile'
    }
  };

  const texts = navItems[lang];
  const mobileMenu = document.getElementById('mobileMenu');

  if (mobileMenu) {
    // Update mobile navigation items
    const mobileNavItems = mobileMenu.querySelectorAll('[data-nav]');
    mobileNavItems.forEach(item => {
      const navKey = item.getAttribute('data-nav');
      if (texts[navKey]) {
        item.textContent = texts[navKey];
      }
    });
  }
}

// Add hover effect for navigation items
export function initNavigationHover() {
  const navItems = document.querySelectorAll('.nav-item');

  navItems.forEach(item => {
    const arrow = item.querySelector('.nav-arrow');

    item.addEventListener('mouseenter', () => {
      item.classList.add('text-primary-yello', 'font-semibold');
      if (arrow) {
        arrow.classList.remove('opacity-0');
        arrow.classList.add('opacity-100');
      }
    });

    item.addEventListener('mouseleave', () => {
      if (!item.classList.contains('active')) {
        item.classList.remove('text-primary-yello', 'font-semibold');
        if (arrow) {
          arrow.classList.remove('opacity-100');
          arrow.classList.add('opacity-0');
        }
      }
    });
  });
}
