import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Utility to get current direction
function getDirection() {
  return document.documentElement.dir || 'ltr';
}

// Professional Products Section GSAP Animation
export function initProductsGSAP() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;
  const skewXDir = dir === 'rtl' ? -1 : 1;

  // Create products section timeline
  const productsTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".container:has(.products-title)",
      start: "top 75%",
      end: "bottom 25%",
      toggleActions: "play none none reverse"
    }
  });

  // Set initial states for section elements
  gsap.set(".products-title", {
    autoAlpha: 0,
    y: 80,
    scale: 0.8,
    skewY: 6,
    filter: "blur(10px)"
  });

  // Set initial state for the yellow underline
  gsap.set(".products-title-underline", {
    autoAlpha: 0,
    scaleX: 0,
    transformOrigin: dir === 'rtl' ? "right center" : "left center"
  });

  gsap.set(".products-subtitle", {
    autoAlpha: 0,
    y: 60,
    x: 50 * xDir,
    scale: 0.9,
    filter: "blur(8px)"
  });

  // Animation sequence
  productsTimeline
    // 1. Title appears with dramatic effect
    .to(".products-title", {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.7)"
    })

    // 2. Yellow underline expands
    .to(".products-title-underline", {
      autoAlpha: 1,
      scaleX: 1,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.6")

    // 3. Subtitle slides from side
    .to(".products-subtitle", {
      autoAlpha: 1,
      y: 0,
      x: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "power2.out"
    }, "-=0.4");

  // Initialize template cards animations
  initTemplateCardsAnimation();
}

// Template Cards Animation
function initTemplateCardsAnimation() {
  const templateCards = document.querySelectorAll('.template-card');
  
  if (templateCards.length === 0) return;

  // Detect direction
  const dir = getDirection();
  const xDir = dir === 'rtl' ? 1 : -1;

  templateCards.forEach((card, index) => {
    // Set initial state
    gsap.set(card, {
      autoAlpha: 0,
      y: 100,
      x: 30 * xDir * (index % 2 === 0 ? 1 : -1), // Alternate sides
      scale: 0.8,
      rotationX: 15,
      skewY: 3,
      filter: "blur(8px)"
    });

    // Entrance animation with stagger
    gsap.to(card, {
      autoAlpha: 1,
      y: 0,
      x: 0,
      scale: 1,
      rotationX: 0,
      skewY: 0,
      filter: "blur(0px)",
      duration: 1.2,
      ease: "back.out(1.4)",
      scrollTrigger: {
        trigger: card,
        start: "top 85%",
        end: "bottom 15%",
        toggleActions: "play none none reverse"
      },
      delay: index * 0.15 // Stagger effect
    });

    // Parallax effect for the card
    gsap.to(card, {
      y: -30,
      ease: "none",
      scrollTrigger: {
        trigger: card,
        start: "top bottom",
        end: "bottom top",
        scrub: 1
      }
    });

    // Laptop website image animation on hover
    const laptopWebsite = card.querySelector('.laptop-website');
    if (laptopWebsite) {
      card.addEventListener('mouseenter', () => {
        gsap.to(laptopWebsite, {
          y: -50,
          duration: 2,
          ease: "power1.inOut"
        });
      });

      card.addEventListener('mouseleave', () => {
        gsap.to(laptopWebsite, {
          y: 0,
          duration: 1.5,
          ease: "power1.inOut"
        });
      });
    }

    // Card hover effects
    card.addEventListener('mouseenter', () => {
      gsap.to(card, {
        scale: 1.02,
        y: -10,
        boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    card.addEventListener('mouseleave', () => {
      gsap.to(card, {
        scale: 1,
        y: 0,
        boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    // Purchase button animation
    const purchaseBtn = card.querySelector('.btn-purchase');
    if (purchaseBtn) {
      purchaseBtn.addEventListener('mouseenter', () => {
        gsap.to(purchaseBtn, {
          scale: 1.05,
          backgroundColor: "#2458A7",
          color: "#ffffff",
          duration: 0.3,
          ease: "power2.out"
        });
      });

      purchaseBtn.addEventListener('mouseleave', () => {
        gsap.to(purchaseBtn, {
          scale: 1,
          backgroundColor: "transparent",
          color: "#2458A7",
          duration: 0.3,
          ease: "power2.out"
        });
      });

      purchaseBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Click animation
        gsap.to(this, {
          scale: 0.95,
          duration: 0.1,
          yoyo: true,
          repeat: 1,
          ease: "power2.inOut"
        });
      });
    }
  });

  // Add floating animation to template images
  templateCards.forEach((card, index) => {
    const templateImage = card.querySelector('.template-image');
    if (templateImage) {
      gsap.to(templateImage, {
        y: "+=10",
        duration: 3 + (index * 0.5), // Vary duration for each card
        ease: "power1.inOut",
        yoyo: true,
        repeat: -1
      });
    }
  });

  // Refresh ScrollTrigger on window resize
  window.addEventListener('resize', () => {
    ScrollTrigger.refresh();
  });
}
