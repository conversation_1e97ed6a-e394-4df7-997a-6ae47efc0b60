import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Entrepreneurship Section Animation
export function initEntrepreneurshipAnimation() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);


  // Get entrepreneurship section elements
  const entrepreneurshipSection = document.querySelector('.entrepreneurship-section');
  const entrepreneurshipImage = document.querySelector('.entrepreneurship-image img');
  const entrepreneurshipContent = document.querySelector('.entrepreneurship-content');
  const entrepreneurshipTitle = document.querySelector('.entrepreneurship-title');
  const entrepreneurshipSubtitle = document.querySelector('.entrepreneurship-subtitle');
  const entrepreneurshipForm = document.querySelector('.entrepreneurship-form');

  if (!entrepreneurshipSection) {
    return;
  }

  // Set initial states for all elements
  if (entrepreneurshipImage) {
    gsap.set(entrepreneurshipImage, {
      autoAlpha: 0,
      x: -100,
      scale: 0.8,
      rotationY: -15,
      filter: "blur(10px)"
    });
  }

  if (entrepreneurshipTitle) {
    gsap.set(entrepreneurshipTitle, {
      autoAlpha: 0,
      y: 80,
      scale: 0.9,
      skewY: 5,
      filter: "blur(8px)"
    });
  }

  if (entrepreneurshipSubtitle) {
    gsap.set(entrepreneurshipSubtitle, {
      autoAlpha: 0,
      y: 60,
      x: 50,
      scale: 0.95,
      filter: "blur(6px)"
    });
  }

  if (entrepreneurshipForm) {
    gsap.set(entrepreneurshipForm, {
      autoAlpha: 0,
      y: 100,
      scale: 0.85,
      rotationX: 15,
      filter: "blur(12px)"
    });
  }

  // Create main animation timeline
  const mainTimeline = gsap.timeline({
    scrollTrigger: {
      trigger: entrepreneurshipSection,
      start: "top 70%",
      end: "bottom 30%",
      toggleActions: "play none none reverse",
    }
  });

  // Animation sequence
  mainTimeline
    // 1. Image slides in from left with 3D effect
    .to(entrepreneurshipImage, {
      autoAlpha: 1,
      x: 0,
      scale: 1,
      rotationY: 0,
      filter: "blur(0px)",
      duration: 1,
      ease: "back.out(1.7)"
    })

    // 2. Title appears with dramatic effect
    .to(entrepreneurshipTitle, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      skewY: 0,
      filter: "blur(0px)",
      duration: 0.8,
      ease: "back.out(1.4)"
    }, "-=0.6")

    // 3. Subtitle slides from right
    .to(entrepreneurshipSubtitle, {
      autoAlpha: 1,
      y: 0,
      x: 0,
      scale: 1,
      filter: "blur(0px)",
      duration: 0.7,
      ease: "power2.out"
    }, "-=0.4")

    // 4. Form appears with 3D flip effect
    .to(entrepreneurshipForm, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      rotationX: 0,
      filter: "blur(0px)",
      duration: 0.9,
      ease: "back.out(1.5)"
    }, "-=0.3");

  // Form fields stagger animation
  const formElements = document.querySelectorAll('#newsletterForm input, #newsletterForm button');
  if (formElements.length > 0) {
    gsap.set(formElements, {
      autoAlpha: 0,
      y: 20,
      scale: 0.9
    });

    const formTimeline = gsap.timeline({
      scrollTrigger: {
        trigger: "#newsletterForm",
        start: "top 80%",
        end: "bottom 20%",
        toggleActions: "play none none reverse"
      }
    });

    formTimeline.to(formElements, {
      autoAlpha: 1,
      y: 0,
      scale: 1,
      duration: 0.5,
      stagger: 0.15,
      ease: "power2.out"
    });
  }

  // Add floating animation to the image
  if (entrepreneurshipImage) {
    gsap.to(entrepreneurshipImage, {
      y: "+=15",
      duration: 3,
      ease: "power1.inOut",
      yoyo: true,
      repeat: -1
    });

    // Add subtle rotation on hover
    entrepreneurshipImage.addEventListener('mouseenter', () => {
      gsap.to(entrepreneurshipImage, {
        scale: 1.05,
        rotationY: 5,
        duration: 0.3,
        ease: "power2.out"
      });
    });

    entrepreneurshipImage.addEventListener('mouseleave', () => {
      gsap.to(entrepreneurshipImage, {
        scale: 1,
        rotationY: 0,
        duration: 0.3,
        ease: "power2.out"
      });
    });
  }

  // Add hover effects to the form
  if (entrepreneurshipForm) {
    entrepreneurshipForm.addEventListener('mouseenter', () => {
      gsap.to(entrepreneurshipForm, {
        scale: 1.02,
        y: -5,
        boxShadow: "0 20px 40px rgba(0,0,0,0.15)",
        duration: 0.3,
        ease: "power2.out"
      });
    });

    entrepreneurshipForm.addEventListener('mouseleave', () => {
      gsap.to(entrepreneurshipForm, {
        scale: 1,
        y: 0,
        boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
        duration: 0.3,
        ease: "power2.out"
      });
    });
  }

  // Add text animation effects
  const titleSpans = document.querySelectorAll('.entrepreneurship-title span');
  if (titleSpans.length > 0) {
    titleSpans.forEach((span, index) => {
      span.addEventListener('mouseenter', () => {
        gsap.to(span, {
          scale: 1.1,
          y: -5,
          duration: 0.2,
          ease: "power2.out"
        });
      });

      span.addEventListener('mouseleave', () => {
        gsap.to(span, {
          scale: 1,
          y: 0,
          duration: 0.2,
          ease: "power2.out"
        });
      });
    });
  }

  // Add parallax effect to the section background
  gsap.to(entrepreneurshipSection, {
    backgroundPosition: "50% 100%",
    ease: "none",
    scrollTrigger: {
      trigger: entrepreneurshipSection,
      start: "top bottom",
      end: "bottom top",
      scrub: true
    }
  });

}
