import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Contact Cards Animation
export function initContactCardsAnimation() {
  // Register ScrollTrigger plugin
  gsap.registerPlugin(ScrollTrigger);

  // Get contact cards
  const contactCards = document.querySelectorAll('.contact-card');


  if (contactCards.length === 0) {
    return;
  }

  // Set initial states for all cards
  contactCards.forEach((card, index) => {
    gsap.set(card, {
      autoAlpha: 0,
      y: 50,
      scale: 0.8,
      rotationY: 15,
      filter: "blur(8px)"
    });
  });

  // Create scroll-triggered animation
  const timeline = gsap.timeline({
    scrollTrigger: {
      trigger: ".contact-cards-container",
      start: "top 80%",
      end: "bottom 20%",
      toggleActions: "play none none reverse",
    }
  });

  // Animate cards with stagger effect
  timeline.to(contactCards, {
    autoAlpha: 1,
    y: 0,
    scale: 1,
    rotationY: 0,
    filter: "blur(0px)",
    duration: 0.6,
    stagger: 0.2,
    ease: "back.out(1.7)"
  });

  // Add hover effects for each card
  contactCards.forEach((card, index) => {
    // Hover in
    card.addEventListener('mouseenter', () => {
      gsap.to(card, {
        scale: 1.05,
        y: -5,
        rotationY: 5,
        duration: 0.3,
        ease: "power2.out"
      });

      // Animate the icon inside
      const icon = card.querySelector('i');
      if (icon) {
        gsap.to(icon, {
          scale: 1.2,
          rotation: 10,
          duration: 0.3,
          ease: "back.out(1.7)"
        });
      }
    });

    // Hover out
    card.addEventListener('mouseleave', () => {
      gsap.to(card, {
        scale: 1,
        y: 0,
        rotationY: 0,
        duration: 0.3,
        ease: "power2.out"
      });

      // Reset the icon
      const icon = card.querySelector('i');
      if (icon) {
        gsap.to(icon, {
          scale: 1,
          rotation: 0,
          duration: 0.3,
          ease: "power2.out"
        });
      }
    });
  });

}
