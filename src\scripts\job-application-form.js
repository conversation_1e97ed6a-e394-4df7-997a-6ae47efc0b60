// Job Application Form Functionality
export function initJobApplicationForm() {
  const form = document.querySelector('form[action=""]');
  const dropZone = document.getElementById('dropZone');
  const fileInput = document.getElementById('fileInput');
  const browseFiles = document.getElementById('browseFiles');
  const fileList = document.getElementById('fileList');
  const emailError = document.getElementById('emailError');

  if (!form) {
    return;
  }

  // Get form elements
  const clientName = form.querySelector('#clientName');
  const email = form.querySelector('#email');
  const phone = form.querySelector('#phone');
  const countryCode = form.querySelector('#countryCode');
  const submitButton = form.querySelector('button[type="submit"]');

  // Store uploaded files
  let uploadedFiles = [];

  // Validation rules
  const validationRules = {
    clientName: {
      required: true,
      minLength: 2,
      pattern: /^[\u0600-\u06FFa-zA-Z\s]+$/,
      errorMessages: {
        required: 'اسم العميل مطلوب',
        minLength: 'الاسم يجب أن يكون أكثر من حرفين',
        pattern: 'الاسم يجب أن يحتوي على أحرف فقط'
      }
    },
    email: {
      required: true,
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      errorMessages: {
        required: 'البريد الإلكتروني مطلوب',
        pattern: 'يرجى إدخال بريد إلكتروني صحيح'
      }
    },
    phone: {
      required: true,
      pattern: /^[0-9]{8,15}$/,
      errorMessages: {
        required: 'رقم الهاتف مطلوب',
        pattern: 'يرجى إدخال رقم هاتف صحيح (8-15 رقم)'
      }
    }
  };

  // Show error message
  function showError(field, message) {
    if (field && field.id === 'email' && emailError) {
      emailError.textContent = message;
      emailError.classList.remove('hidden');
    }
    if (field) {
      field.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
      field.classList.remove('border-gray-300', 'focus:ring-primary-blue', 'focus:border-transparent');
    }
  }

  // Hide error message
  function hideError(field) {
    if (field && field.id === 'email' && emailError) {
      emailError.classList.add('hidden');
    }
    if (field) {
      field.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');
      field.classList.add('border-gray-300', 'focus:ring-primary-blue', 'focus:border-transparent');
    }
  }

  // Validate single field
  function validateField(field, rules) {
    if (!field || !rules) return false;
    const value = field.value.trim();

    // Check required
    if (rules.required && !value) {
      showError(field, rules.errorMessages.required);
      return false;
    }

    // Check pattern
    if (value && rules.pattern && !rules.pattern.test(value)) {
      showError(field, rules.errorMessages.pattern);
      return false;
    }

    // Check min length
    if (value && rules.minLength && value.length < rules.minLength) {
      showError(field, rules.errorMessages.minLength);
      return false;
    }

    hideError(field);
    return true;
  }

  // File handling functions
  function handleFiles(files) {
    Array.from(files).forEach(file => {
      // Check file type
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        showNotification('يرجى رفع ملفات PDF أو Word فقط', 'error');
        return;
      }

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        showNotification('حجم الملف يجب أن يكون أقل من 5 ميجابايت', 'error');
        return;
      }

      // Add to uploaded files
      uploadedFiles.push(file);
      displayFile(file);
    });
  }

  function displayFile(file) {
    const fileDiv = document.createElement('div');
    fileDiv.className = 'flex items-center justify-between bg-gray-50 p-3 rounded-lg';
    fileDiv.innerHTML = `
      <div class="flex items-center gap-3">
        <i class="fas fa-file-alt text-primary-blue"></i>
        <span class="text-sm font-medium">${file.name}</span>
        <span class="text-xs text-gray-500">(${(file.size / 1024 / 1024).toFixed(2)} MB)</span>
      </div>
      <button type="button" class="text-red-500 hover:text-red-700" onclick="removeFile('${file.name}')">
        <i class="fas fa-times"></i>
      </button>
    `;

    fileList.appendChild(fileDiv);
    fileList.classList.remove('hidden');
  }

  // Make removeFile function global
  window.removeFile = function (fileName) {
    uploadedFiles = uploadedFiles.filter(file => file.name !== fileName);
    const fileElements = fileList.querySelectorAll('div');
    fileElements.forEach(element => {
      if (element.textContent.includes(fileName)) {
        element.remove();
      }
    });

    if (uploadedFiles.length === 0) {
      fileList.classList.add('hidden');
    }
  };

  // Show notification
  function showNotification(message, type = 'success') {
    const notificationDiv = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    notificationDiv.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
    notificationDiv.innerHTML = `
      <div class="flex items-center gap-2">
        <i class="fas ${type === 'success' ? 'fa-check' : 'fa-exclamation-triangle'}"></i>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(notificationDiv);

    // Animate in
    setTimeout(() => {
      notificationDiv.classList.remove('translate-x-full');
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
      notificationDiv.classList.add('translate-x-full');
      setTimeout(() => {
        if (document.body.contains(notificationDiv)) {
          document.body.removeChild(notificationDiv);
        }
      }, 300);
    }, 3000);
  }

  // Event listeners for drag and drop
  if (dropZone) {
    dropZone.addEventListener('dragover', (e) => {
      e.preventDefault();
      dropZone.classList.add('border-primary-blue', 'bg-blue-50');
    });

    dropZone.addEventListener('dragleave', (e) => {
      e.preventDefault();
      dropZone.classList.remove('border-primary-blue', 'bg-blue-50');
    });

    dropZone.addEventListener('drop', (e) => {
      e.preventDefault();
      dropZone.classList.remove('border-primary-blue', 'bg-blue-50');
      const files = e.dataTransfer.files;
      handleFiles(files);
    });
  }

  // Browse files button
  if (browseFiles) {
    browseFiles.addEventListener('click', () => {
      if (fileInput) {
        fileInput.click();
      }
    });
  }

  // File input change
  if (fileInput) {
    fileInput.addEventListener('change', (e) => {
      handleFiles(e.target.files);
    });
  }

  // Real-time validation
  if (clientName) {
    clientName.addEventListener('input', () => {
      validateField(clientName, validationRules.clientName);
    });
  }

  if (email) {
    email.addEventListener('input', () => {
      validateField(email, validationRules.email);
    });
  }

  if (phone) {
    phone.addEventListener('input', () => {
      validateField(phone, validationRules.phone);
    });
  }

  // Form submission
  if (form) {
    form.addEventListener('submit', (e) => {
      e.preventDefault();

      // Validate all fields
      const isClientNameValid = clientName ? validateField(clientName, validationRules.clientName) : false;
      const isEmailValid = email ? validateField(email, validationRules.email) : false;
      const isPhoneValid = phone ? validateField(phone, validationRules.phone) : false;

      const isFormValid = isClientNameValid && isEmailValid && isPhoneValid;

      if (isFormValid) {
        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<span>جاري الإرسال...</span><i class="fas fa-spinner fa-spin"></i>';
        submitButton.classList.add('opacity-75', 'cursor-not-allowed');

        // Simulate form submission
        setTimeout(() => {
          // Show success message
          showNotification('تم إرسال طلب التوظيف بنجاح!', 'success');

          // Reset form
          form.reset();
          uploadedFiles = [];
          fileList.innerHTML = '';
          fileList.classList.add('hidden');

          // Reset button
          submitButton.disabled = false;
          submitButton.innerHTML = '<span>إرسال</span><i class="fas fa-arrow-left"></i>';
          submitButton.classList.remove('opacity-75', 'cursor-not-allowed');

        }, 2000);
      } else {
        // Focus on first invalid field
        const firstInvalidField = form.querySelector('.border-red-500');
        if (firstInvalidField) {
          firstInvalidField.focus();
        }
      }
    });
  }
}
